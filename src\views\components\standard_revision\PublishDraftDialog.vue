<template>
  <el-dialog
    width="930px"
    title="发布意见征求"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="queryRef"
      :model="form"
      :inline="true"
      :label-position="'top'"
      :rules="rules"
      class="dialog-form-inline"
      @submit.prevent
    >
      <el-form-item label="征求意见稿" prop="draftOpinionFileList">
        <div>
          <ele-upload-image
            v-model:value="form.draftOpinionFileList"
            :fileSize="5"
            :multiple="false"
            :fileType="['pdf', 'doc', 'docx']"
            :responseFn="handleResponse"
            @success="handleUpload('draftOpinionFileList')"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：word，pdf；文件大小不超过5MB
          </div>
        </div>
      </el-form-item>
      <el-form-item label="编制说明" prop="compilationInstructionFileList">
        <div>
          <ele-upload-image
            v-model:value="form.compilationInstructionFileList"
            :fileSize="5"
            :multiple="false"
            :fileType="['pdf', 'doc', 'docx']"
            :responseFn="handleResponse"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：word，pdf；文件大小不超过5MB
          </div>
        </div>
      </el-form-item>
      <el-form-item label="设置截止时间" prop="deadline">
        <el-date-picker
          :editable="false"
          :clearable="false"
          type="datetime"
          v-model="form.deadline"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          :disabled-date="disabledDay"
          :disabled-hours="disabledHour"
          :disabled-minutes="disabledMinute"
        />
      </el-form-item>
      <el-form-item label="征求类型" prop="type">
        <el-select @change="changeScope" clearable filterable v-model="form.type" placeholder="请选择征求类型">
          <el-option v-for="item in bxc_solicit_type" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type == 1" prop="userList" class="one-column mt10">
        <template #label>
          <div class="flex flex-sb">
            <div>
              <span style="color: var(--el-color-danger)">*</span>
              意见征求成员
            </div>
            <el-button @click="handleAddPerson" type="primary" icon="Plus">选择成员</el-button>
          </div>
        </template>
        <el-table :data="form.userList" :border="true" class="mt15">
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column label="序号" type="index" width="55" fixed />
          <el-table-column prop="name" label="姓名" show-overflow-tooltip fixed min-width="150" />
          <el-table-column prop="unit" label="所在部门" show-overflow-tooltip min-width="180" />
          <el-table-column label="操作" min-width="50" fixed="right">
            <template #default="{ row, $index }">
              <el-button @click="form.userList.splice($index, 1)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
    <choose-person-dialog v-if="personVisible" v-model:visible="personVisible" :tags="form.userList" @handleChoose="personInfo" />
  </el-dialog>
</template>

<script setup>
  import { solicitOpinion } from '@/api/standard_revision/manage';
  import EleUploadImage from '@/components/EleUploadImage';
  import ChoosePersonDialog from '@/views/components/standard_revision/ChoosePersonDialog.vue';

  const { proxy } = getCurrentInstance();
  const { bxc_solicit_type } = proxy.useDict('bxc_solicit_type');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const checkDadline = (rule, value, callback) => {
    if (Date.parse(value) < Date.now()) {
      callback(new Error('截止时间应大于当前时间'));
    } else {
      callback();
    }
  };

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    draftOpinionFileList: [{ required: true, message: '请上传征求意见稿', trigger: 'change' }],
    deadline: [
      { required: true, message: '请选择截止时间', trigger: 'change' },
      { validator: checkDadline, trigger: 'change' },
    ],
    type: [{ required: true, message: '请选择征求类型', trigger: 'change' }],
  });
  const personVisible = ref(false);

  form.value.deadline = proxy.parseTime(Date.now(), '{y}-{m}-{d} {h}:{i}');

  const disabledDay = data => {
    return data.getTime() < Date.now() - 8.64e7;
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const disabledHour = () => {
  const now = new Date();
  const nowHours = now.getHours();
  if (new Date(form.value.deadline).toDateString() == now.toDateString()) {
    return range(0, nowHours);
  }
};

const disabledMinute = () => {
  const now = new Date();
  const nowHours = now.getHours();
  const nowMinutes = now.getMinutes();
  const currentHours = new Date(form.value.deadline).getHours();
  if ((new Date(form.value.deadline).toDateString() == now.toDateString()) && (currentHours <= nowHours)) {
    return range(0, nowMinutes);
  }
};

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = type => {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  };

  const changeScope = () => {
    form.value.userList = [];
  };

  const handleAddPerson = () => {
    personVisible.value = true;
  };

  const personInfo = data => {
    form.value.userList = data;
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        form.value.userIdList = form.value.userList.map(item => item.id);
        form.value.joinId = props.id;
        solicitOpinion(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('发布成功！');
            emit('success');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped></style>
