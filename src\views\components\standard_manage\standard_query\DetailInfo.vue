<template>
  <div class="info">
    <div class="flex flex-ai-center pl50 pb50" :class="form.publishDate ? 'mt30' : ''">
      <div class="info-progress">
        <div
          v-if="form.publishDate"
          class="info-progress-time"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.publishDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f16"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div
          class="info-progress-title"
          :class="[0, 1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'"
        >
          发布
        </div>
      </div>
      <div class="info-line" :class="[1, 3, 4].includes(Number(form.standardStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="info-progress">
        <div
          v-if="form.executeDate"
          class="info-progress-time"
          :class="[1, 3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.executeDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f16"
          :class="[1, 3, , 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div class="info-progress-title" :class="[1, 3, , 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'">
          实施
        </div>
      </div>
      <div class="info-line" :class="[3, 4].includes(Number(form.standardStatus)) ? 'bgc-primary' : 'bgc-gray'"></div>
      <div class="info-progress">
        <div
          v-if="form.repealDate && (form.standardStatus == 3 || form.standardStatus == 4)"
          class="info-progress-time"
          :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-99'"
        >
          {{ form.repealDate }}
        </div>
        <span
          class="iconfont icon-xuanzhong f16"
          :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary' : 'c-gray'"
        ></span>
        <div class="info-progress-title" :class="[3, 4].includes(Number(form.standardStatus)) ? 'c-primary f-bold' : 'c-99'">
          废止
        </div>
      </div>
    </div>
    <div class="h-title">基础信息</div>
    <div class="mt15" style="display: flex; flex-wrap: wrap">
      <div class="info-item">
        <div class="info-item-title">标准类型：</div>
        <div class="info-item-content">{{ form.standardTypeName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">标准状态：</div>
        <div class="info-item-content">{{ form.standardStatusName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">发布日期：</div>
        <div class="info-item-content">{{ form.publishDate }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">实施日期：</div>
        <div class="info-item-content">{{ form.executeDate }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">CCS号：</div>
        <div class="info-item-content">{{ form.standardTypeCodeGb }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">ICS号：</div>
        <div class="info-item-content">{{ form.standardTypeCodeIso }}</div>
      </div>
      <!-- 状态为废止或被替代时展示 -->
      <template v-if="form.standardStatus == 3 || form.standardStatus == 4">
        <div class="info-item">
          <div class="info-item-title">废止日期：</div>
          <div class="info-item-content">{{ form.repealDate }}</div>
        </div>
      </template>
      <div class="info-item" v-if="form.standardType != 5">
        <div class="info-item-title">归口单位：</div>
        <div class="info-item-content">{{ form.registryUnit }}</div>
      </div>
      <template v-if="[0, 1, 2, 3, 4].includes(Number(form.standardType))">
        <div class="info-item">
          <div class="info-item-title">制修订：</div>
          <div class="info-item-content">{{ form.amendName }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">标准性质：</div>
          <div class="info-item-content">{{ form.standardAttrName }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">标准类别：</div>
          <div class="info-item-content">{{ form.standardCategoryName }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 0">
        <div class="info-item">
          <div class="info-item-title">执行单位：</div>
          <div class="info-item-content">{{ form.applyUnit }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">主管部门：</div>
          <div class="info-item-content">{{ form.manageDept }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 1 || form.standardType == 2">
        <div class="info-item">
          <div class="info-item-title">批准发布部门：</div>
          <div class="info-item-content">{{ form.confirmPublishDept }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">备案日期：</div>
          <div class="info-item-content">{{ form.filingsDate }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">备案号：</div>
          <div class="info-item-content">{{ form.filingsNumber }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 1">
        <div class="info-item">
          <div class="info-item-title">行业分类：</div>
          <div class="info-item-content">{{ form.industryClassification }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">行业领域：</div>
          <div class="info-item-content">{{ form.industryCategory }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 2">
        <div class="info-item">
          <div class="info-item-title">发布地区：</div>
          <div class="info-item-content">{{ form.address }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 3">
        <div class="info-item">
          <div class="info-item-title">团体名称：</div>
          <div class="info-item-content">{{ form.associationName }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">是否包含专利：</div>
          <div class="info-item-content">{{ form.isPatentInfoName }}</div>
        </div>
      </template>
      <template v-if="form.standardType == 4 || form.standardType == 8 || form.standardType == 9">
        <div class="info-item">
          <div class="info-item-title">发布单位：</div>
          <div class="info-item-content">{{ form.applyUnit }}</div>
        </div>
      </template>
      <div class="info-item" v-if="form.articleRepealContent && form.isArticleRepeal == 1">
        <div class="info-item-title">条文废止说明：</div>
        <div class="info-item-content">{{ form.articleRepealContent }}</div>
      </div>
      <template v-if="form.standardType == 5">
        <div class="info-item">
          <div class="info-item-title">发布组织：</div>
          <div class="info-item-content">{{ form.ctName }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">标准语言：</div>
          <div class="info-item-content">{{ form.languageType }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);
</script>

<style lang="scss" scoped>
  .info {
    &-progress {
      position: relative;

      &-time {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }

      &-title {
        position: absolute;
        bottom: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 15px;
        white-space: nowrap;
      }
    }

    &-line {
      flex: 1;
      max-width: 300px;
      height: 4px;
    }

    &-item {
      width: 49%;
      display: flex;

      &:nth-child(n + 3) {
        margin-top: 15px;
      }

      &-title {
        width: 100px;
        color: #999;
        font-size: 14px;
        line-height: 25px;
        text-align: right;
      }

      &-content {
        flex: 1;
        color: #333;
        font-size: 14px;
        line-height: 25px;
        text-align: left;
      }
    }
  }

  .pl50 {
    padding-left: 50px;
    box-sizing: border-box;
  }

  .pb50 {
    padding-bottom: 50px;
    box-sizing: border-box;
  }

  .c-gray {
    color: #c8c8c8;
  }

  .bgc-gray {
    background-color: #c8c8c8;
  }
</style>
