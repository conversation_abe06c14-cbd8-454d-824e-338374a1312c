<template>
  <el-dialog
    width="930px"
    :title="props.id ? '制修订项目编辑' : '制修订立项'"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline">
      <el-form-item label="项目编号" prop="projectCode">
        <el-input :disabled="props.id != ''" v-model.trim="form.projectCode" maxlength="30" placeholder="请输入项目编号" />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model.trim="form.projectName" maxlength="50" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="项目负责人" prop="projectManager">
        <el-select clearable filterable v-model="form.projectManager" placeholder="请选择项目负责人">
          <el-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
        </el-select>
      </el-form-item>
      <el-form-item label="制修订类型" prop="amend">
        <el-select
          @change="handleChange"
          :disabled="props.id != ''"
          clearable
          filterable
          v-model="form.amend"
          placeholder="请选择制修订类型"
        >
          <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划开始日期" prop="planStartTime">
        <el-date-picker
          clearable
          v-model="form.planStartTime"
          type="date"
          placeholder="请选择计划开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="计划结束日期" prop="planEndTime">
        <el-date-picker
          clearable
          v-model="form.planEndTime"
          type="date"
          placeholder="请选择计划结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <!-- <el-form-item label="标准号" prop="standardCode">
        <el-input maxlength="100" v-model.trim="form.standardCode" placeholder="请输入标准号" />
      </el-form-item>
      <el-form-item label="标准名称" prop="standardName">
        <el-input maxlength="50" placeholder="请输入标准名称" v-model.trim="form.standardName" />
      </el-form-item> -->
      <template v-if="form.amend == 1">
        <el-form-item label="替代标准号" prop="beReplacedStandardCode">
          <el-input maxlength="50" placeholder="请输入替代标准号" v-model.trim="form.beReplacedStandardCode">
            <template #append>
              <el-button @click="handleSelectStandard" icon="Search" class="f-16" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="替代标准名称" prop="beReplacedStandardName">
          <el-input maxlength="50" placeholder="请输入替代标准名称" v-model.trim="form.beReplacedStandardName" />
        </el-form-item>
      </template>
      <el-form-item label="可见范围" prop="viewScope">
        <el-select @change="changeScope" clearable filterable v-model="form.viewScope" placeholder="请选择可见范围">
          <el-option v-for="item in bxc_apply_scope" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目介绍" prop="projectIntroduce" class="one-column">
        <el-input
          v-model="form.projectIntroduce"
          :rows="5"
          :show-word-limit="true"
          maxlength="500"
          type="textarea"
          placeholder="请项目介绍说明信息"
        />
      </el-form-item>
      <div class="flex flex-sb flex-ai-center one-column mt10">
        <div class="h-title">项目成员</div>
        <div class="flex flex-ai-center">
          <el-button @click="handleAddSystemPerson" type="primary" icon="Plus">选择内部成员</el-button>
          <el-button @click="handleAddExternalPerson" type="primary" icon="Plus">添加外部成员</el-button>
        </div>
      </div>
      <el-table :data="form.memberList" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" type="index" fixed width="55" />
        <el-table-column prop="name" label="姓名" show-overflow-tooltip fixed min-width="150" />
        <el-table-column prop="contactNumber" label="联系方式" show-overflow-tooltip min-width="150" />
        <el-table-column prop="memberTypeName" label="成员类型" show-overflow-tooltip min-width="120" />
        <el-table-column prop="unit" label="所在部门/单位" show-overflow-tooltip min-width="180" />
        <el-table-column fixed="right" label="操作" min-width="100">
          <template #default="{ row, $index }">
            <el-button @click="form.memberList.splice($index, 1)" type="danger" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-if="approveStatus == 1 && disabled">
        <div class="h-title f-18 c-33 fw-b one-column mt15">申请信息</div>
        <el-form-item label="申请说明" prop="applyExplain" class="one-column">
          <el-input
            v-model="form.applyExplain"
            :rows="5"
            :show-word-limit="true"
            maxlength="500"
            type="textarea"
            placeholder="请输入审批申请说明信息"
          />
        </el-form-item>
        <el-form-item label="附件" prop="applyTextFilesList" class="one-column">
          <div>
            <ele-upload-image
              v-model:value="form.applyTextFilesList"
              :fileSize="5"
              :multiple="true"
              :fileType="['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
              :responseFn="handleResponse"
              @success="handleUpload('applyTextFilesList')"
            />
            <div class="c-EE0000 mt10 flex flex-ai-center">
              <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
              支持文件格式：jpeg、jpg、png、bmp、doc、docx、xls、xlsx、pdf；单个文件大小不超过5MB
            </div>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">
        {{ approveStatus == 1 && disabled ? '提交审批' : '提交' }}
      </el-button>
    </template>
    <select-standard-dialog v-if="selectStandardVisible" v-model:open="selectStandardVisible" @selected="standardInfo" />
    <choose-person-dialog
      v-if="personVisible"
      v-model:visible="personVisible"
      :tags="form.memberList"
      @handleChoose="personInfo"
    />
    <add-external-person
      v-if="externalPersonVisible"
      :haveSex="false"
      v-model:visible="externalPersonVisible"
      @handleChoose="externalPersonInfo"
    />
  </el-dialog>
</template>

<script setup>
  import { getUserList, getApproveStatus } from '@/api/common';
  import { getRevisionDetail, addRevision, putRevision, getProcessData } from '@/api/standard_revision/manage';
  import EleUploadImage from '@/components/EleUploadImage';
  import SelectStandardDialog from '@/views/components/application_tool/PopComparisonSelectStandard';
  import ChoosePersonDialog from '@/views/components/standard_revision/ChoosePersonDialog.vue';
  import AddExternalPerson from '@/views/components/standard_revision/AddExternalPerson.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
      default: '',
    },
    isRestart: {
      type: Boolean,
      default: false,
    },
  });

  const { proxy } = getCurrentInstance();
  const { bxc_standard_amend, bxc_apply_scope } = proxy.useDict('bxc_standard_amend', 'bxc_apply_scope');

  const loading = ref(false);
  const approveStatus = ref(0);
  const userList = ref([]);
  const form = ref({
    amend: '0',
    viewScope: '0',
    memberList: [],
  });
  const rules = reactive({
    projectCode: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
    projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    projectManager: [{ required: true, message: '请选择项目负责人', trigger: 'change' }],
    amend: [{ required: true, message: '请选择制修订类型', trigger: 'change' }],
    planStartTime: [
      { required: true, message: '请选择计划开始日期', trigger: 'change' },
      { validator: validateStartTime, trigger: 'change' },
    ],
    planEndTime: [
      { required: true, message: '请选择计划完成日期', trigger: 'change' },
      { validator: validateEndTime, trigger: 'change' },
    ],
    viewScope: [{ required: true, message: '请选择可见范围', trigger: 'change' }],
    memberList: [{ required: true, message: '请选择可见成员', trigger: 'change' }],
    deptList: [{ required: true, message: '请选择可见部门', trigger: 'change' }],
    beReplacedStandardCode: [{ required: true, message: '请输入替代标准号', trigger: 'change' }],
    beReplacedStandardName: [{ required: true, message: '请输入替代标准名称', trigger: 'change' }],
    drafters: [{ required: true, message: '请输入主要起草人姓名', trigger: 'blur' }],
    draftUnits: [{ required: true, message: '请输入主要起草单位名称', trigger: 'blur' }],
  });
  const selectStandardVisible = ref(false);
  const personVisible = ref(false);
  const externalPersonVisible = ref(false);

  let disabled = computed(() => {
    return props.isRestart || props.id == '' ? true : false;
  });

  function getSelectList() {
    getUserList({ status: 0 }).then(res => {
      userList.value = res.rows;
    });
    getApproveStatus().then(res => {
      approveStatus.value = res.data;
    });
    if (props.isRestart) {
      getProcessData({ procInsId: props.id }).then(res => {
        form.value = res.data;
      });
      return;
    } else {
      if (props.id) {
        getRevisionDetail(props.id).then(res => {
          form.value = res.data;
        });
      }
    }
  }

  const handleSelectStandard = () => {
    selectStandardVisible.value = true;
  };

  const standardInfo = data => {
    form.value.beReplacedStandardCode = data.standardCode;
    form.value.beReplacedStandardName = data.standardName;
  };

  const handleAddSystemPerson = () => {
    personVisible.value = true;
  };

  const personInfo = data => {
    data.forEach(item => {
      if (item.userId) {
        item.memberType = 0;
        item.memberTypeName = '内部成员';
      }
    });
    form.value.memberList = data;
  };

  const handleAddExternalPerson = () => {
    externalPersonVisible.value = true;
  };

  const externalPersonInfo = data => {
    data.memberType = 1;
    data.memberTypeName = '外部成员';
    form.value.memberList.push(data);
  };

  function handleChange(e) {
    if (e == 0) {
      form.beReplacedStandardCode == '';
      form.beReplacedStandardName == '';
    }
  }

  function changeScope() {
    form.value.memberList = [];
  }

  function validateStartTime(rule, value, callback) {
    if (form.value.planEndTime) {
      if (new Date(value).getTime() > new Date(form.value.planEndTime).getTime()) {
        form.value.planStartTime = '';
        callback(new Error('计划开始日期需小于计划完成日期'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  }

  function validateEndTime(rule, value, callback) {
    if (form.value.planStartTime) {
      if (new Date(value).getTime() < new Date(form.value.planStartTime).getTime()) {
        form.value.planEndTime = '';
        callback(new Error('计划完成日期需大于计划开始日期'));
      } else {
        callback();
      }
    } else {
      callback();
    }
  }

  function handleResponse(response, file, fileList) {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  }

  function handleUpload(type) {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  }

  function handleConfirm() {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        if (!form.value.memberList || form.value.memberList.length == 0) {
          proxy.$modal.msgError('请选择项目成员！');
          loading.value = false;
          return;
        }
        if (props.id && !props.isRestart) {
          // 编辑
          putRevision(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('制修订项目保存成功！');
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          // 新增  重启
          addRevision(form.value)
            .then(res => {
              let title = '';
              if (props.isRestart) {
                title = '重启成功！';
              } else {
                approveStatus.value == 1 ? (title = '制修订立项审批提交成功！') : (title = '制修订立项成功！');
              }
              proxy.$modal.msgSuccess(title);
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  }

  function handleClose() {
    emit('update:visible', false);
    loading.value = false;
  }

  const emit = defineEmits(['update:visible', 'updateData']);

  getSelectList();
</script>

<style lang="scss" scoped>
  :deep(.el-input-group__append .el-button:focus:not(.el-button:hover)) {
    border-color: transparent !important;
    background-color: transparent !important;
    color: inherit !important;
  }
</style>
