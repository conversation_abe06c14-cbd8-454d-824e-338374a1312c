<template>
  <div class="app-container">
    <div class="knowledge-main">
      <div class="knowledge-main-left">
        <div class="nav-wrap">
          <div class="nav-img">
            <img :src="info.cover" alt="">
          </div>
          <div class="nav-content">
            <div class="nav-title">{{info.name || '-'}}</div>
            <div class="nav-desc">{{info.describe || '-'}}</div>
          </div>
        </div>
        <div class="knowledge-menu-list">
          <el-input
            v-model="searchStr"
            placeholder="输入检索知识资料名称"
            suffix-icon="Search"
          />
          <div @click="selectHome" class="menu-item home-item mt20" :class="commonInfo.isHome ? 'active-item' : ''">
            <i class="iconfont icon-ai-home"></i>
            <span>主页</span>
          </div>
          <div class="menu-item category-item">
            <i class="iconfont icon-mulu"></i>
            <span>目录</span>
            <span v-hasPermi="['knowledge:main:more']" class="cate-plus">
              <knowledge-plus-menu :uploadDom="uploadDom"></knowledge-plus-menu>
            </span>
          </div>
          <el-tree
            v-loading="menuLoading"
            :data="categoryList"
            ref="treeRef"
            node-key="id"
            empty-text="暂无数据"
            :highlight-current="true"
            :props="defaultProps" 
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :filter-node-method="filterNode"
            :draggable="isDrag"
            @node-drop="handleDrop"
            :allow-drop="handleAllowDrop"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                  <span class="custom-label">
                    <suffix-icon class="mr5" :fileName="data.fileName" :fileType="data.fileType"></suffix-icon>
                    <span v-showToolTip="['tree']">
                      <el-tooltip placement="bottom-start" :content="node.label">
                        <span>{{ node.label }}</span>
                      </el-tooltip>
                    </span>
                  </span>
                  <span>
                    <knowledge-op-menu v-hasPermi="['knowledge:main:more']" :item="data" :uploadDom="uploadDom"></knowledge-op-menu>
                  </span>
                </span>
              </template>
          </el-tree>  
          
        </div>
      </div>
      <div class="knowledge-main-right">
        <knowledge-main-home v-if="commonInfo.isHome" :info="info"></knowledge-main-home>
        <knowledge-main-content v-else ></knowledge-main-content>
      </div>

      <knowledge-upload ref="kFileUpload" @success="uploadSuccess"></knowledge-upload>
    </div>
  </div>
</template>

<script setup>
import {detailKnowledge,getLibraryTree,dragMove} from '@/api/knowledge/library'
import KnowledgeMainHome from '@/views/components/knowledge/main/KnowledgeMainHome'
import KnowledgeMainContent from '@/views/components/knowledge/main/KnowledgeMainContent'
import KnowledgeUpload from '@/views/components/knowledge/main/KnowledgeUpload'
import KnowledgePlusMenu from '@/views/components/knowledge/main/KnowledgePlusMenu'
import KnowledgeOpMenu from '@/views/components/knowledge/main/KnowledgeOpMenu'
import SuffixIcon from '@/components/SuffixIcon/SuffixIcon'

const { proxy } = getCurrentInstance()

const data = reactive({
  categoryList: [],
  defaultProps:{
    children: 'childrenList',
    label: 'fileName',
  },
  loading: false,
  info:{},
  uploadDom: null,
  searchStr: '',
  isDrag: true,
  menuLoading: false
})
const { categoryList,defaultProps,loading,info,uploadDom,searchStr,isDrag,menuLoading } = toRefs(data)
data.isDrag = proxy.$auth.hasPermi('knowledge:main:drag')

const commonInfo = reactive({
  libraryId: undefined,
  fileId: 0,
  fileName: undefined,
  currentCategory: {},
  isHome: true,
  refreshCategory: () => {
    getCategoryTree()
  },
  refreshDetail: () => {
    getData()
  },
  setSelectedCategory: async (deleteItem) => {
    await getCategoryTree()
    if(data.categoryList && data.categoryList.length > 0){
      handleNodeClick(data.categoryList[0])
    }else{
      selectHome()
    }
  },
  refreshMove: () => {
    selectHome()
    getCategoryTree()
  }
})

provide('commonInfo',commonInfo)

const mouseenter = (data) => {
  data.show = true
}
const mouseleave = (data) => {
  data.show = false
}
const selectHome = () => {
  proxy.$refs.treeRef.setCurrentKey(null)
  commonInfo.isHome = true
  commonInfo.fileId = 0
  commonInfo.fileName = undefined
  commonInfo.currentCategory = {}
}
const handleNodeClick = (item, nodes) => {
  commonInfo.isHome = false
  commonInfo.fileId = item.id
  commonInfo.fileName = item.fileName
  commonInfo.currentCategory = item

  setTimeout(()=>{
    proxy.$refs.treeRef.setCurrentKey(item.id)
  },200)
}
const uploadSuccess = () => {
  //通过修改commonInfo.fileId值激活watch监听
  const fId = commonInfo.fileId
  commonInfo.fileId = -1
  setTimeout(()=>{
    commonInfo.fileId = fId
  },100)
}
const getData = () => {
  data.loading = true;
  detailKnowledge(commonInfo.libraryId).then((response) => {
    data.info = response.data || {};
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const getCategoryTree = () => {
  data.loading = true;
  getLibraryTree(commonInfo.libraryId).then((response) => {
    data.categoryList = response.data
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
if(proxy.$route.query.id){
  commonInfo.libraryId = proxy.$route.query.id
  if(proxy.$route.query.fileId){
    commonInfo.fileId = proxy.$route.query.fileId
    commonInfo.isHome = false
  }else{
    commonInfo.isHome = true
  }

  getData()
  getCategoryTree()
}
const handleAllowDrop = (draggingNode, dropNode, type) => {
  return (type=='inner' ? ((dropNode.data.fileType == '0') ? true : false) : true)
}
const handleDrop = (draggingNode,dropNode,dropType,ev) => {
  // if(dropNode.data.type == 1) return 

  let data1 = dropType != "inner" ? dropNode.parent.data : dropNode.data;
  let nodeData = dropNode.level == 1 && dropType != "inner" ? data1 : data1.childrenList;
  // 设置父ID,当level为1说明在第一级，pid为空
  let nextId = undefined
  let pid = undefined
  let currentIndex = 0
  nodeData.forEach((element,index) => {
    element.pid =  data1.id || 0; 
    if(element.id == draggingNode.data.id){
      currentIndex = index
      pid = element.pid
    }
  });

  let len = nodeData.length
  if(!(len == 1 || currentIndex == len-1)){
    nextId = nodeData[currentIndex+1].id
  }
  
  let params = {
    id: draggingNode.data.id,
    pid: pid,
    nextId: nextId
  }
  data.menuLoading = true;
  dragMove(params).then((response) => {
    data.menuLoading = false
  }).catch(() => {
    getCategoryTree()
    data.menuLoading = false
  });
}
watch(searchStr, val => {
  proxy.$refs.treeRef.filter(val);
});
/** 通过条件过滤节点  */
const filterNode = (value, obj) => {
  if (!value) return true;
  return obj.fileName.indexOf(value) !== -1;
}
onMounted(() => {
  data.uploadDom = proxy.$refs.kFileUpload.$refs.uploadRef.$el.children[0].children[0]
})

</script>

<style lang="scss" scoped>
.knowledge-main {
  background: #FFFFFF;
  border-radius: 5px;
  height: calc(100vh - 126px);
  display: flex;
  .knowledge-main-left {
    max-height: 850px;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
    width: 250px;
    border-right: 1px solid #E5E8EF;
    .nav-wrap{
      display: flex;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #E5E8EF;
      .nav-img{
        img{
          width: 70px;
          height: 46px;
          object-fit: cover;
        }
      }
      .nav-content{
        margin: 0 10px;
        color: #3E4967;
        font-size: 14px;
        .nav-title{
          font-weight: bold;
        }
        .nav-desc{
          width: 140px;
          white-space: nowrap;
          overflow: hidden;//文本超出隐藏
          text-overflow: ellipsis;//文本超出省略号替代
        }
      }
    }
    .knowledge-menu-list{
      padding: 15px;
      .menu-item{
        font-size: 16px;
        font-weight: bold;
        color: #3E4967;
        padding: 0 15px;
        height: 40px;
        line-height: 40px;
        display: flex;
        align-items: center;
        span{
          margin: 0 10px;
        }
      }
      .home-item{
        cursor: pointer;
      }
      .category-item{
        // padding-right: 0px !important;
        position: relative;
        .cate-plus{
          position: absolute;
          right: 0px;
          top:10px;
          cursor: pointer;
        }
      }
      .active-item{
        color: #3377FF;
        background: #E9F0FE;
        border-radius: 5px;
      }
      
      :deep(.el-tree-node__content){
        height: 40px !important;
        &:hover{
          background: #E9F0FE;
          border-radius: 5px;
          color:#3377FF;
        }
        .custom-tree-node{
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding-right: 8px;
          height: 40px;
          overflow: hidden;
          .custom-label{
            flex: 1;
            white-space: nowrap;
            overflow: hidden;//文本超出隐藏
            text-overflow: ellipsis;
          }
        }
      }
      :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content){
        color:#3377FF;
      }
      
    }
  }
  .knowledge-main-right {
    flex: 1;
    padding: 30px 30px 0px 30px;
    max-height: 850px;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

</style>