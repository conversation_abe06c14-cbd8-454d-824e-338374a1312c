<template>
  <div class="comparison-full-text-wrap">
    <div class="left">
      <div class="left-tree-wrap scroll-style">
        <el-tree
          :data="originTreeList"
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps" 
          default-expand-all
          :expand-on-click-node="false"
          :current-node-key="currentOriginNode.id"
          @node-click="(item, nodes) => comparisonStore.handleArticleNodeClick(item, nodes, originInfo.id,'origin')"
          class="custom-origin-tree"
          style="background-color: #ECF5FC;"
          >
            <template #default="{ node, data }">
            
              <span class="custom-tree-node">
                <span v-if="node.level == 1" class="custom-label">
                  <i class="iconfont icon-wendang2 c-0096FF ml5 mr8"></i>
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else class="custom-label">
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
            </template>
        </el-tree>
      </div>
      <div class="left-content scroll-style" ref="div1" @scroll="handleScroll(1)">
        <div v-if="originContent" v-html="originContent"></div>
        <empty v-else />
      </div>
    </div>
    <div class="right">
      <div class="right-content scroll-style" ref="div2" @scroll="handleScroll(2)">
        <div v-if="compareContent" v-html="compareContent"></div>
        <empty v-else />
      </div>
      <div class="right-tree-wrap scroll-style">
        <el-tree
          :data="compareTreeList"
          ref="tree2Ref"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps" 
          default-expand-all
          :expand-on-click-node="false"
          :current-node-key="currentCompareNode.id"
          @node-click="(item, nodes) => comparisonStore.handleArticleNodeClick(item, nodes,compareInfo.id,'compare')"
          class="custom-compare-tree"
          style="background-color: #EBF1FC;"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span v-if="node.level == 1" class="custom-label">
                  <i class="iconfont icon-wendang2 c-primary ml5 mr8"></i>
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else class="custom-label">
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span class="level">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
            </template>
        </el-tree>
      </div>
    </div>
    <div class="vs-btn" @click="handleVs">
      <img src="@/assets/images/application_tool/comparison/vs-btn.png" alt="">
    </div>
  </div>
</template>
<script setup>
import useComparisonStore from '@/store/modules/comparison'
import useSyncScroll from '@/composables/useSyncScroll'

const props = defineProps({
  syncScroll: {
    type: Boolean,
    default: false,
  }
})

const comparisonStore = useComparisonStore()
const {div1,div2,setSyncScroll,handleScroll,handlescrollOffsets} = useSyncScroll()

const defaultProps = {
  children: 'children',
  label: 'name',
}
const { originTreeList, compareTreeList, originInfo, compareInfo, originContent, compareContent, currentOriginNode, currentCompareNode } = storeToRefs(comparisonStore);

const handleVs = () => {
  comparisonStore.handleVs()
}

watchEffect(()=>{
  setSyncScroll(props.syncScroll)
  if(props.syncScroll){
    handlescrollOffsets()
  }
})

</script>
<style lang="scss">
@import '@/assets/styles/comparison.scss';
</style>