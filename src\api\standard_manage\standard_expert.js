import request from '@/utils/request'

//标技委专家
//新增
export const addStcExpert = (data) => {
  return request({
    url: '/process/stcExpert',
    method: 'post',
    data
  })
}

//列表
export const stcExpertList = (params) => {
  return request({
    url: '/process/stcExpert/list',
    method: 'get',
    params
  })
}

//详情
export const getStcExpertDetail = (id) => {
  return request({
    url: '/process/stcExpert/' + id,
    method: 'get'
  })
}

//编辑
export const editStcExpert = (data) => {
  return request({
    url: '/process/stcExpert',
    method: 'put',
    data
  })
}

//删除
export const delStcExpert = (data) => {
  return request({
    url: '/process/stcExpert/remove',
    method: 'delete',
    data
  })
}