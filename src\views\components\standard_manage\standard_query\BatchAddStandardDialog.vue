<template>
  <div>
    <el-dialog
      width="465px"
      title="批量入库"
      v-model="visible"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
      <div class="flex flex-sb flex-ai-center f-14 c-33">
        入库方式：
        <el-switch
          v-model="switchValue"
          size="large"
          :active-value="'1'"
          :inactive-value="'0'"
          active-text="联网入库"
          inactive-text="不联网入库"
        />
      </div>
      <el-upload
        ref="uploadRef"
        v-model:file-list="fileList"
        :limit="1"
        :before-upload="handleBeforeUpload"
        :headers="upload.headers"
        :data="{ importType: switchValue }"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="true"
        :show-file-list="false"
      >
        <div style="width: 100%" class="el-upload__text">
          <el-button style="width: 100%" class="mt20 h50 f-16 bgc-primary" type="primary">
            <i class="iconfont icon-shangchuan mr5 f-14"></i>
            上传批量入库标准数据
          </el-button>
        </div>
        <template #tip>
          <div class="el-upload__tip mt10 f-14 c-FF0000 text-center">
            <i class="iconfont icon-tishi mr5 f-14"></i>
            仅支持上传xlsx,xls格式文件，文件不能超过100MB
          </div>
        </template>
      </el-upload>
      <el-button class="mt20 h50 f-16 bgc-primary" style="width: 100%" type="primary" @click="fhandleDownload">
        <i class="iconfont icon-xiazai mr5 f-20"></i>
        下载导入模板
      </el-button>
      <div class="mt10 c-FF0000 text-center f-14">
        <i class="iconfont icon-tishi mr5 f-16"></i>
        请按导入模板正确填写入库标准数据
      </div>
      <!-- 导入更新结果弹框 -->
      <batch-fail-standard-dialog
        v-if="batchFailStandardVisible"
        v-model:visible="batchFailStandardVisible"
        :dataList="dataList"
        :total="total"
        :successTotal="successTotal"
        :failureTotal="failureTotal"
        :switchValue="switchValue"
        :errorMsg="errorMsg"
      />
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  import { ElLoading } from 'element-plus';
  import BatchFailStandardDialog from '@/views/components/standard_manage/standard_query/BatchFailStandardDialog.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const { visible } = toRefs(props);

  const data = reactive({
    switchValue: false,
    batchFailStandardVisible: false,
    dataList: [],
    total: 0,
    successTotal: 0,
    failureTotal: 0,
    upload: {
      // 是否禁用上传
      isUploading: false,
      // 设置上传的请求头部
      headers: { Authorization: 'Bearer ' + getToken() },
      // 上传的地址
      url: process.env.VITE_APP_BASE_API + '/process/common/standard/importData',
    },
    fileSize: 100,
    fileType: ['xlsx', 'xls'],
    fileList: [],
    errorMsg: ''
  });

  const {
    switchValue,
    batchFailStandardVisible,
    dataList,
    total,
    successTotal,
    failureTotal,
    upload,
    fileSize,
    fileType,
    fileList,
    errorMsg
  } = toRefs(data);

  let downloadLoadingInstance;

  const emit = defineEmits(['update:visible', 'updateData']);

  const fhandleDownload = () => {
    window.location.href = switchValue.value == 1 ? '/file/批量联网入库模板.xlsx' : '/file/批量不联网入库模板.xlsx';
  };

  const handleBeforeUpload = file => {
    let isValid = false;
    if (data.fileType.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = data.fileType.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${data.fileType.join('/')}格式文件!`);
      return false;
    }
    if (data.fileSize) {
      const isLt = file.size / 1024 / 1024 < data.fileSize;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${data.fileSize} MB!`);
        return false;
      }
    }
    return true;
  };

  const handleFileUploadProgress = (event, file, fileList) => {
    data.upload.isUploading = true;
    downloadLoadingInstance = ElLoading.service({ text: '正在上传数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  const handleFileSuccess = (response, file, fileList) => {
    data.upload.isUploading = false;
    proxy.$refs.uploadRef.clearFiles();
    downloadLoadingInstance.close();
    const { code, data: dataT, msg } = response;
    if (code == 200) {
      handleClose();
      emit('updateData');
      data.total = dataT.total;
      data.dataList = dataT.list;
      data.successTotal = dataT.successTotal;
      data.failureTotal = dataT.failureTotal;
      data.batchFailStandardVisible = true;
      data.errorMsg = dataT.errorMsg || '';
    } else {
      proxy.$modal.msgError('解析失败,请重试！');
    }
  };

  const handleFileError = (error, uploadFile, uploadFiles) => {
    downloadLoadingInstance.close();
  };

  const handleClose = () => {
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-upload--text) {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  :deep(.el-dialog__body) {
    padding: 25px 30px 40px !important;
  }
</style>
