<template>
  <el-dialog
    width="96%"
    title="标准对比"
    append-to-body
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <div class="tbale-main">
        <div class="table-header" ref="tableHeaderRef">
          <div class="w10">比对主题</div>
          <div class="w35">{{ comparisionInfo?.original }}</div>
          <div class="w35">{{ comparisionInfo?.compare }}</div>
          <div class="w20">差异总结</div>
        </div>
        <el-scrollbar max-height="calc(100vh - 270px)" v-if="dataInfo?.length > 0">
          <div class="table-tbody">
            <table class="tables" border="1" cellspacing="0" cellpadding="0" style="width: 100%; border-collapse: collapse">
              <tr v-for="(row, inde) in dataInfo" :key="inde">
                <td class="w10" align="center" :style="{ width: tableHeaderWidth * 0.1 + 'px' }">
                  <p v-if="row.keyword">【{{ row.keyword.split('+')[0] }}】</p>
                  <span v-if="row.keyword && row.keyword.split('+')[1]">【{{ row.keyword.split('+')[1] }}】</span>
                </td>
                <td class="w35">
                  <!-- <div class="text" v-html="removeMarkdown(row.sourceContent)"
                                        :style="{ width: tableHeaderWidth * 0.35 - 50 + 'px' }"></div> -->
                  <MarkdownView :content="row.sourceContent" :style="{ width: tableHeaderWidth * 0.35 - 50 + 'px' }" />
                </td>
                <td class="w35">
                  <!-- <div class="text" v-html="removeMarkdown(row.targetContent)"
                                        :style="{ width: tableHeaderWidth * 0.35 - 50 + 'px' }"></div> -->

                  <MarkdownView :content="row.targetContent" :style="{ width: tableHeaderWidth * 0.35 - 50 + 'px' }" />
                </td>
                <td class="w20">
                  <!-- <div class="text" v-html="removeMarkdown(row.differenceSummary)"
                                        :style="{ width: tableHeaderWidth * 0.2 - 50 + 'px' }"></div> -->

                  <MarkdownView :content="row.differenceSummary" :style="{ width: tableHeaderWidth * 0.2 - 50 + 'px' }" />
                </td>
              </tr>
            </table>
          </div>
        </el-scrollbar>
        <div v-else>
          <empty />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import MarkdownView from '@/views/components/business_manage/common/MarkdownView.vue'

  import { processMathText, replaceMathText } from '@/utils/index';
  const { proxy } = getCurrentInstance();
  const visible = ref(false);
  const loading = ref(true);

  const type = ref('');
  const dataInfo = ref({});
  const comparisionInfo = ref({
    original: '',
    compare: '',
  });
  const tableHeaderRef = ref(null);
  const tableHeaderWidth = ref(0);
  const open = (val, data, comparision) => {
    visible.value = true;
    nextTick(() => {
      if (tableHeaderRef.value) {
        const width1 = tableHeaderRef.value.offsetWidth;
        tableHeaderWidth.value = width1;
      }
    });
    type.value = val;
    comparisionInfo.value = comparision;
    dataInfo.value = data;
    loading.value = false;
  };
  const handleClose = () => {
    visible.value = false;
  };

  const parseText = text => {
    return text.replace(/\$([\s\S]*?)\$/g, (match, p1) => {
      return '$' + p1.replace(/\\\\+/g, '\\') + '$';
    });
  };

  function removeMarkdown(text) {
    const patterns = [
      { regex: /`{3,}[\s\S]*?`{3,}/g, replace: '' }, // 代码块 ```code```
      { regex: /`(.+?)`/g, replace: '$1' }, // 行内代码 `code`
      { regex: /\*\*(.+?)\*\*/g, replace: '$1' }, // 粗体 **text**
      { regex: /\*(.+?)\*/g, replace: '$1' }, // 斜体 *text*
      { regex: /__(.+?)__/g, replace: '$1' }, // 粗体 __text__
      { regex: /_(.+?)_/g, replace: '$1' }, // 斜体 _text_
      { regex: /^#{1,6}\s*/gm, replace: '' }, // 标题 # Header → Header
      { regex: /^(\s*[-*+]|\d+\.)\s+/gm, replace: '' }, // 列表 - item → item
      { regex: /^>\s*/gm, replace: '' }, // 引用 > text → text
      { regex: /---+/g, replace: '' }, // 水平线 ---
    ];

    let cleanedText = text;
    patterns.forEach(({ regex, replace }) => {
      cleanedText = cleanedText.replace(regex, replace);
    });

    return cleanedText.trim();
  }

  defineExpose({
    open,
  });
</script>

<style lang="scss">
  .tbale-main {
    .w10 {
      width: 10%;
    }

    .w35 {
      width: 35%;
    }

    .w20 {
      width: 20%;
    }
  }

  .table-header {
    background-color: #0272ff;
    color: #fff;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    border-radius: 4px;
    padding: 0 20px;

    div {
      padding: 15px;
      text-align: center;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }

      &:last-child {
        border-radius: 0 4px 4px 0;
      }
    }
  }

  .table-tbody {
    width: 100%;
    background-color: #d9ecff;
    margin: 10px 0;
    padding: 15px 20px;
    border-radius: 4px;

    mjx-container[jax='SVG'] > svg {
      max-width: 100%;
    }

    .tables {
      border-collapse: collapse;

      td {
        border: 1px solid #f8f8f8;
        padding: 10px 15px;
        vertical-align: top;
      }

      .text {
        word-wrap: break-word;
        white-space: normal;
        overflow: hidden;
        line-height: 1.5;
      }

      table {
        border-collapse: collapse;

        td {
          border: 1px solid #dddddd;
          padding: 10px 15px;
        }
      }
    }
  }
</style>
