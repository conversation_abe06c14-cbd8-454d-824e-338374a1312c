<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="流程名称:" prop="procDefName">
          <el-input
            v-model="queryParams.procDefName"
            placeholder="请输入流程名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发起人:" prop="processCreateUserId">
          <el-select
            v-model="queryParams.processCreateUserId"
            :remote-method="getPersonList"
            value-key="userId"
            filterable
            remote
            clearable
            placeholder="请搜索和选择发起人"
          >
            <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName" />
          </el-select>
        </el-form-item>
        <el-form-item label="发起时间:">
          <el-date-picker
            v-model="processDateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectProcessDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="指派时间:">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="container-bar">
        <div class="bar-right">
        </div>
      </div>
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column 
          type="index"
          align="center" 
          label="序号" 
          fixed="left"  
          min-width="55"
        >
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="procDefName"
          label="审批流程名称"
          min-width="200"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="startUserName"
          label="发起人"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="startDeptName"
          label="所在部门"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processCreateTime"
          label="发起时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createTime"
          label="指派时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="duration"
          label="流程耗时"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button
              @click.stop="handleAudit(scope.row)"
              v-hasPermi="['approve:todo:audit']"
              link
              size="small"
              class="f-14 c-primary"
            >
              审批
            </el-button>
            <el-button
              @click.stop="handleProcess(scope.row)"
              v-hasPermi="['approve:todo:process']"
              link
              size="small"
              class="f-14 c-primary"
            >
              审批流程
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 审批弹框 -->
    <pop-todo-audit v-if="openAudit" v-model:open="openAudit" :info="currentInfo" @success="getList" />

    <!-- 审批流程图弹框 -->
    <approve-process-view v-if="openProcess" v-model:open="openProcess" :procInsId="currentInfo.procInsId" />
  </div>
</template>

<script setup>
import { getTodoList } from '@/api/approve/todo'
import { getUserList } from '@/api/common'
import PopTodoAudit from '@/views/components/approve/PopTodoAudit'
import ApproveProcessView from '@/views/components/approve/ApproveProcessView'

const { proxy } = getCurrentInstance()

const data = reactive({
  loading: false,
  dateRange: [],
  processDateRange: [],
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    procDefName: undefined,
    processCreateUserId: undefined,
    processStartTime: undefined,
    processEndTime: undefined,
    startTime: undefined,
    endTime: undefined,
  },
  personList: [],
  openAudit: false,
  openProcess: false,
  currentInfo: {}
});
const {
  loading,
  dateRange,
  processDateRange,
  total,
  dataList,
  queryParams,
  personList,
  openAudit,
  openProcess,
  currentInfo
} = toRefs(data);
const resetQuery = () => {
  data.dateRange = [];
  data.processDateRange = [];
  proxy.resetForm("queryFormRef");
  selectDate();
  selectProcessDate();
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.queryParams.pageNum = 1;
  getList();
}

const getList = () => {
  data.loading = true;
  getTodoList(data.queryParams).then((response) => {
    if(response.rows){
      data.dataList = response.rows
      data.total = response.total
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const selectProcessDate = () => {
  if (data.processDateRange && data.processDateRange.length > 0) {
    data.queryParams.processStartTime = data.processDateRange[0];
    data.queryParams.processEndTime = data.processDateRange[1];
  } else {
    data.queryParams.processStartTime = undefined;
    data.queryParams.processEndTime = undefined;
  }
}
const selectDate = () => {
  if (data.dateRange && data.dateRange.length > 0) {
    data.queryParams.startTime = data.dateRange[0];
    data.queryParams.endTime = data.dateRange[1];
  } else {
    data.queryParams.startTime = undefined;
    data.queryParams.endTime = undefined;
  }
}
const getPersonList = (query) => {
  getUserList({nickName: query,pageSize:10,pageNum:1}).then((response) => {
    if(response.rows){
      data.personList = response.rows
    }
  }).catch(() => {

  });
}
const handleAudit = (row) => {
  data.currentInfo = row
  data.openAudit = true
}
const handleProcess = (row) => {
  data.currentInfo = row
  data.openProcess = true
}

getList()
getPersonList()
</script>

<style lang="scss" scoped>

</style>