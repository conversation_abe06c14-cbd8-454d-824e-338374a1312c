<template>
  <div v-resize="onResize" :class="className" :style="{ height: height, width: width }" />
</template>

<script setup>
import * as echarts from 'echarts'
// import resize from "@/views/dashboard/mixins/resize";

const {proxy} = getCurrentInstance()
// mixins: [resize],
const props = defineProps({
  className: {
    type: String,
    default: 'chart'
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: "350px"
  },
  autoResize: {
    type: Boolean,
    default: true
  },
  showTitle: {
    type: Boolean,
    default: false
  },
  chartData: {
    type: Object,
    required: true
  }
})
const {className,width,height,autoResize,chartData} = toRefs(props)

const data = reactive({
  chart: null
})
const emit = defineEmits(["select"]);

const {chart} = toRefs(data)

watch(()=>props.chartData,(newVal)=>{
  setOptions(newVal)
},{deep: true})

onMounted(()=>{
  proxy.$nextTick(() => {
    initChart()
    data.chart.on('click',(params) => {
      if(params.componentType == 'title'){
        emit('select', {type: '-99',list: []})
      }else{
        let dataT = params.data
        if(dataT.statusList) {
          emit('select', {type: dataT.code,list:dataT.statusList})
        }
      }
      
    })
  })
})

const sumNum = computed(() => {
  return props.chartData.reduce(( acc, cur ) => acc + cur.value, 0)
})

onBeforeUnmount(() => {
  if (!data.chart) {
    return
  }
  data.chart.dispose()
  data.chart = null
})

const initChart = () => {
  data.chart = markRaw(echarts.init(proxy.$el))
  setOptions(props.chartData)
}

const setOptions = () => {
  let colors = ["#2F5AFF", "#36CB22", "#FBD437","#A54DF6", "#63D9E4", "#F27AFA", "#F5960E"];
  data.chart.setOption({
    tooltip: {
      trigger: "item",
      formatter: "{b} : {d}% <br/> {c}",
    },
    title: {
      show: props.showTitle,
      text:sumNum.value,
      left:'center',
      top:'41%',
      triggerEvent: true,
      textStyle:{
        color:'#333',
        fontSize:25,
        align:'center'
      }
    },
    legend: {
      x:'center',
      y:'bottom',
      itemWidth: 14,
      itemHeight: 14,
      itemGap: 25,
      textStyle: {
        fontSize: 14,
      }
    },
    series: [
      {
        type: "pie",
        radius: ["30%", "50%"],
        center: ["50%", "45%"],
        color: colors,
        itemStyle: {
        },
        data: props.chartData,
        labelLine: {
          show: true,
          length: 15,
          length2: 50,
          // lineStyle: {
          //     color: '#CCCCCC',
          //     width: 2
          // }
        },
        label: {
          formatter: `{b|{b}}\n{d|{d}%}`,
          rich: {
            b: {
              fontSize: 14,
              color: "#333",
              align: "left",
              padding: 4,
            },
            d: {
              fontSize: 14,
              // color: '#2F5AFF',
              align: "center",
              padding: 4,
            },
          },
        },
      },
    ],
  });
}
const onResize = () => {
  data.chart && data.chart.resize()
}
</script>
