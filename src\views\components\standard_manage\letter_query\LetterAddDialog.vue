<template>
  <div>
    <el-dialog
      width="930"
      :title="id ? '编辑函文' : '新增函文'"
      v-model="addDialogVisible"
      :before-close="handleClose"
    >
      <el-form
        :inline="true"
        label-width="auto"
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="dialog-form-inline"
      >
        <el-form-item label="文号" prop="letterNumber">
          <el-input
            maxlength="100"
            placeholder="请输入文号"
            v-model="form.letterNumber"
          />
        </el-form-item>
        <el-form-item label="名称" prop="letterName">
          <el-input
            v-model="form.letterName"
            maxlength="100"
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item label="分类" prop="letterType">
          <el-select clearable filterable v-model="form.letterType" placeholder="请选择分类">
            <el-option v-for="item in bxc_letter_type" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="letterStatus">
          <el-select clearable filterable v-model="form.letterStatus" placeholder="请选择状态">
            <el-option v-for="item in bxc_letter_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="发文单位" prop="issueUnit">
          <el-input
            maxlength="100"
            placeholder="请输入发文单位"
            v-model="form.issueUnit"
          />
        </el-form-item>
        <el-form-item label="发文日期" prop="issueDate">
          <el-date-picker
            v-model="form.issueDate"
            type="date"
            placeholder="请选择源发布时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="附件" prop="letterFileList" class="one-column">
          <div>
            <ele-upload-image
              :multiple="true"
              :responseFn="handleResponse"
              :fileType="fileType"
              v-model:value="form.letterFileList"
              :fileSize="200"
              :limit="fileLimit"
              @success="handleUpload('letterFileList')"
            />
            <div class="c-EE0000 flex upload-notice flex-ai-center">
              <i class="iconfont icon-tishi f-18 mr5"></i>
              支持文件格式：word、pdf、Excel、ppt、jpeg、png；单个文件大小不超过200MB,文件数不超过9个
            </div>
          </div>
        </el-form-item>
        
        
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button  @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import EleUploadImage from "@/components/EleUploadImage";
import {addStandardLetter,getStandardLetterDetail,editStandardLetter} from '@/api/standard_manage/letter_query.js'

import { toRefs } from "vue";
import { useDateFormat } from "@vueuse/core";


const { proxy } = getCurrentInstance();

const { bxc_letter_type, bxc_letter_status } = proxy.useDict(
  'bxc_letter_type',
  'bxc_letter_status'
);


const props = defineProps({
  id: null,
  addDialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { id, addDialogVisible } = toRefs(props);

const data = reactive({
  currentId:null,
  clientDialog: false,
  clientContactDialog: false,
  loading: false,
  options: [],
  form: {},
  fileLimit:9,
  fileType:['png', 'jpg', 'jpeg', 'bmp', 'gif','bmp','pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  rules: {
    letterNumber:[
      {
        required: true,
        message: "请输入文号",
        trigger: "blur",
      },
    ],
    letterName:[
      {
        required: true,
        message: "请输入名称",
        trigger: "blur",
      },
    ],
    letterType:[
      {
        required: true,
        message: "请选择分类",
        trigger: "change",
      },
    ],
    letterStatus:[
      {
        required: true,
        message: "请选择状态",
        trigger: "change",
      },
    ],
    issueUnit:[
      {
        required: true,
        message: "请输入发文单位",
        trigger: "blur",
      },
    ],
    
    letterFileList:[
      {
        required: true,
        message: "请选择附件",
        trigger: "blur",
      },
    ]
    
  },
});
const {loading, form, rules,fileType,fileLimit } = toRefs(data);

onMounted(() => {
  if (id.value) getDetail();
});

const getDetail = () => {
  getStandardLetterDetail(id.value).then((res) => {
    let data = res.data;
    if(data.standardList){
      form.value.itemList = data.standardList;
    }
    form.value = Object.assign(form.value,data);
    form.value.noticeFileList = data.noticeFileList || [];
  });
};

const handleResponse = (response, file, fileList) => {
  return { id: response.data.id, url: response.data.url, name: response.data.name };;
};


const handleUpload = (type) => {
  nextTick(() => {
    proxy.$refs.queryRef.validateField(type);
  });
};

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (id.value) {
        editStandardLetter(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('更新成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addStandardLetter(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('新建成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
};

const handleClose = () => {
  emit("update:addDialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

const emit = defineEmits(["update:addDialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
.upload-notice{
  margin-top: 5px;
  img{
    height: 18px;
    margin-right: 8px;
  }
}
</style>
