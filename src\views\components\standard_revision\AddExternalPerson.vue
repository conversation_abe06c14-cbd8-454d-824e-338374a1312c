<template>
  <el-dialog
    v-model="props.visible"
    title="外部成员"
    width="480px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form :model="form" label-position="top" ref="formRef" :rules="formRules" @submit.native.prevent>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" maxlength="20" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item v-if="props.haveSex" label="性别" prop="sex">
        <el-select v-model="form.sex" placeholder="请输入性别">
          <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="联系方式" prop="contactNumber">
        <el-input v-model="form.contactNumber" placeholder="请输入联系方式" />
      </el-form-item>
      <el-form-item label="所在单位" prop="unit">
        <el-input v-model="form.unit" maxlength="50" placeholder="请输入所在单位名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button @click="handleSubmit" type="primary" :loading="loading">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { telMobileValidPattern } from '@/utils/index';

  const { proxy } = getCurrentInstance();
  const { sys_user_sex } = proxy.useDict('sys_user_sex');

  const props = defineProps({
    visible: Boolean,
    haveSex: {
      type: Boolean,
      default: true,
    },
  });

  const loading = ref(false);
  const form = ref({});
  const formRules = ref({
    name: [{ required: true, trigger: 'blur', message: '请输入姓名' }],
    sex: [{ required: true, trigger: 'change', message: '请输入性别' }],
    contactNumber: [{ pattern: telMobileValidPattern, message: '请输入正确的联系方式', trigger: 'blur' }],
  });

  const handleSubmit = () => {
    loading.value = true;
    proxy.$refs.formRef.validate(valid => {
      if (valid) {
        emit('handleChoose', form.value);
        handleClose();
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'handleChoose']);
</script>

<style lang="scss" scoped>
  :deep(.el-select) {
    width: 100% !important;
  }
</style>
