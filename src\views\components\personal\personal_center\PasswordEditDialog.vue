<template>
  <el-dialog
    width="460"
    title="修改密码"
    v-model="dialogVisible"
    :modal-append-to-body="false"
    :before-close="handleClose"
  >
    <el-form
      label-width="auto"
      :model="form"
      :label-position="'top'"
      ref="queryRef"
      :rules="rules"
    >
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input v-model="form.oldPassword" placeholder="请输入旧密码" type="password" show-password />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" placeholder="请输入新密码" type="password" show-password />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          @click="handleConfirm"
          :loading="loading"
          type="primary"
          
        >
          提交
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateUserPwd } from "@/api/system/user";

const { proxy } = getCurrentInstance();


const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { dialogVisible } = toRefs(props);

const data = reactive({
  loading: false
});
const {loading} = toRefs(data);

const form = reactive({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined
});

const equalToPassword = (rule, value, callback) => {
  if (form.newPassword != value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};
const rules = ref({
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  newPassword: [{ required: true, message: "新密码不能为空", trigger: "blur" }, { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }],
  confirmPassword: [{ required: true, message: "确认密码不能为空", trigger: "blur" }, { required: true, validator: equalToPassword, trigger: "blur" }]
});


/** 提交按钮 */
function handleConfirm() {
  proxy.$refs.queryRef.validate(valid => {
    if (valid) {
      updateUserPwd(form.oldPassword, form.newPassword).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        handleClose()
      });
    }
  });
};

const handleClose = () => {
  emit("update:dialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

const emit = defineEmits(["update:dialogVisible", "getUser"]);
</script>

<style lang="scss" scoped>
:deep(.el-form-item){
  margin-right: 0 !important;
}


</style>
