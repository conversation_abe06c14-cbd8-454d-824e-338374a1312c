<template>
  <div class="pop-container">
    <el-dialog :modal-append-to-body="false" v-model="open" width="860px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="f-22 f-bold c-33 mb10">体系节点与标准</div>
      
      <div v-for="(item,index) in dataList" :key="index" class="standard-import-list">
        <div class="flex">
          <div class="flex-1 flex">
            <el-cascader
              @change="e => handleCascaderChange(e,index)"
              v-model="item.systemId"
              :options="categoryList"
              :show-all-levels="false"
              :props="{ value: 'id', label: 'name', children: 'children', emitPath: false, checkStrictly: true }"
              placeholder="请选择标准导入体系"
            />
            <div class="search">
              <el-autocomplete
                style="width: 100%;"
                v-model="item.searchStr"
                value-key="standardName"
                :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,index)})"
                placeholder="请搜索及选择需要加入体系的标准"
                @select="handleSelect"
                clearable
              >
                <template #default="{ item }">
                  <div class="flex">
                    <div>{{ item.standardCode }}</div>
                    <div class="ml10" style="margin-right: auto;">{{ item.standardName }}</div>
                    <div class="ml10 f-18 c-primary flex flex-ai-center flex-shrink"><el-icon><CirclePlusFilled /></el-icon></div>
                  </div>
                </template>
              </el-autocomplete>
            </div>
          </div>
          <div class="ml20">
            <el-button
              v-if="dataList.length > 1"
              type="primary"
              icon="Delete"
              plain
              @click="handleDelete(index)"
            >删除</el-button>
          </div>
        </div>

        <!-- 表格区 -->
        <el-table
          v-loading="loading"
          :data="item.itemList"
          class="mt15 mb20"
          :border="true"
        >
          <el-table-column type="index" align="center" min-width="55" fixed="left" />
          <el-table-column
            prop="standardCode"
            label="标准号"
            min-width="200"
            show-overflow-tooltip
            fixed="left"
          />
          <el-table-column
            prop="standardName"
            label="标准名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="standardTypeName"
            label="标准类型"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            prop="standardStatusName"
            label="标准状态"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="executeDate"
            label="实施日期"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
            <template #default="scope">
              <el-button
                type="danger"
                icon="Delete"
                link
                @click="handleItemDelete(scope.$index,item.itemList)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="btn-wrap">
        <el-button
          type="primary"
          plain
          @click="handleAdd"
        >新增</el-button>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getStandardSystemTree,importStandardSystem } from '@/api/standard_manage/standard_system'
import { getStandardList } from '@/api/standard_manage/standard_query';

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  id: [String, Number]
});
const { open } = toRefs(props)

const data = reactive({
  title: '导入标准',
  loading: false,
  dataList: [],
  categoryList: [],
  currentId: undefined
})

const {title,loading,dataList,categoryList} = toRefs(data)

data.dataList.push({
  systemId: props.id,
  searchStr: '',
  itemList: []
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const handleAdd = () => {
  data.dataList.push({
    systemId: undefined,
    searchStr: '',
    itemList: []
  })
}
const handleDelete = (index) => {
  data.dataList.splice(index,1)
}
const handleItemDelete = (index,arr) => {
  arr.splice(index,1)
}
const querySearchAsync = (queryString,cb,index) => {
  let list = data.dataList[index].itemList.map(item=>item.standardId)
  let params = {
    title: queryString,
    searchStandardIdList: list,
    joinType: 0,//关联类型(0: 标准体系，1:意见反馈，2:公告法规,3:标准应用)
    joinId: data.dataList[index].systemId,
    pageNum: 1,
    pageSize: 10
  }
  getStandardList(params).then((response) => {
    if(response.rows){
      cb(response.rows)
      data.currentId = index
    }
  }).catch(() => {
    cb()
  });
}
const handleSelect = (item) => {
  if(data.currentId != undefined){
    let idx = data.dataList[data.currentId].itemList.findIndex(obj => obj.standardId == item.standardId)

    if(idx == -1) {
      data.dataList[data.currentId].itemList.push(item)
    }
    data.dataList[data.currentId].searchStr = undefined
    document.activeElement.blur()
  }
}
const handleCascaderChange = (e, index) => {
  const count = data.dataList.filter(item => item.systemId == e).length;
  if(count > 1) {
    proxy.$modal.msgError("选择标准体系已选过");
    proxy.$nextTick(()=>{
      data.dataList[index].systemId = undefined    
    })
  }
}
const getCategoryTree = () => {
  data.loading = true;
  getStandardSystemTree().then((response) => {
    if(response.data){
      data.categoryList = response.data
    }
    
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const save = () => {
  //判断条件
  if(!isValid()){
    return
  }
  data.loading = true;

  // 参数
  let params = []
  data.dataList.map(d1 => {
    let sId = d1.systemId
    let ids = d1.itemList.map(d2 => d2.standardId)
    params.push({
      standardIds: ids,
      systemId: sId
    })
  })

  importStandardSystem(params).then((response) => {
    data.loading = false;
    emit('update:open',false);
    emit('success');
    proxy.$modal.msgSuccess("导入标准成功");
  }).catch(() => {
    data.loading = false
  });
}
const isValid = () => {
  if(data.dataList.some(item => item.systemId == undefined)){
    proxy.$modal.msgError("请选择标准体系");
    return false
  }
  if(data.dataList.some(item => item.itemList.length == 0)){
    proxy.$modal.msgError("请选择导入标准");
    return false
  }
  return true
}
getCategoryTree()
</script>

<style lang="scss" scoped>
.search{
  margin-left: 20px;
  width: 260px;
}
</style>