<template>
  <el-drawer title="制修订反馈详情" size="75%" append-to-body lock-scroll v-model="dialogVisible" @close="close">
    <div class="notice-content">
      <div class="h-title">项目信息</div>
      <el-descriptions :column="3" class="mt10">
        <el-descriptions-item label="项目编号：">{{ form.projectCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目名称：">{{ form.projectName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人：">{{ form.projectManagerName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="制修订类型：">{{ form.amendName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="标准号：">{{ form.standardCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="标准名称：">{{ form.standardName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="计划开始日期：">{{ form.planStartTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="计划完成日期：">{{ form.planEndTime || '-' }}</el-descriptions-item>
        <template v-if="form.amend == 1">
          <el-descriptions-item label="替代标准号：">{{ form.beReplacedStandardCode || '-' }}</el-descriptions-item>
          <el-descriptions-item label="替代标准名称：">{{ form.beReplacedStandardName || '-' }}</el-descriptions-item>
        </template>
        <el-descriptions-item label="项目介绍：">{{ form.projectIntroduce || '-' }}</el-descriptions-item>
      </el-descriptions>
      <div class="h-title mt20">反馈信息</div>
      <el-descriptions class="mt10" :column="3">
        <el-descriptions-item label="意见类别：">{{ form.opinionTypeName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="反馈时间：">{{ form.feedbackTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="意见说明：">{{ form.projectIntroduce || '-' }}</el-descriptions-item>
        <el-descriptions-item label="附件：" :span="3">
          <ele-upload-image
            v-if="form.feedbackFileList && form.feedbackFileList.length > 0"
            class="accept-content"
            :responseFn="handleResponse"
            :isShowUploadIcon="false"
            v-model:value="form.feedbackFileList"
          ></ele-upload-image>
          <div v-else>无</div>
        </el-descriptions-item>
      </el-descriptions>
      <template v-if="form.processResult != 0">
        <div class="h-title mt20">反馈处理</div>
        <el-descriptions class="mt10" :column="3">
          <el-descriptions-item label="处理人：">{{ form.processUser || '-' }}</el-descriptions-item>
          <el-descriptions-item label="处理时间：">{{ form.processTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="处理结果：">{{ form.processResultName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="处理说明：" :span="3">{{ form.processContent || '-' }}</el-descriptions-item>
          <el-descriptions-item label="附件：" :span="3">
            <ele-upload-image
              v-if="form.processFileList && form.processFileList.length > 0"
              class="accept-content"
              :responseFn="handleResponse"
              :isShowUploadIcon="false"
              v-model:value="form.processFileList"
            ></ele-upload-image>
            <span v-else>无</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </div>
  </el-drawer>
</template>

<script setup>
  import EleUploadImage from '@/components/EleUploadImage';
  import { standardFeedbackInfo } from '@/api/standard_revision/feedback.js';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number],
  });
  const { dialogVisible, id } = toRefs(props);

  const state = reactive({
    loading: false,
    form: {},
    dataList: [],
  });

  const { title, loading, form, dataList } = toRefs(state);

  onMounted(() => {
    if (id.value) getDetail();
  });

  const getDetail = () => {
    standardFeedbackInfo(id.value).then(res => {
      let data = res.data;
      state.form = data;
      state.dataList = data.standardList;
    });
  };

  const emit = defineEmits(['update:dialogVisible', 'success']);
  const close = () => {
    emit('update:dialogVisible', false);
  };
  const cancel = () => {
    emit('update:dialogVisible', false);
  };
  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };
</script>

<style lang="scss" scoped>
  .ele-upload-image {
    display: block !important;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
