<template>
  <el-drawer title="协作团队" size="480" append-to-body lock-scroll v-model="props.visible" @close="handleClose">
    <div class="f-16 c-33 f-bold">{{ props.standardName }}</div>
    <div class="f-14 c-33 mt5" v-if="collaborator.length == 0 && reviewer.length == 0">当前文档未邀请协作成员</div>
    <div class="f-14 c-33 mt5" v-else>
      当前文档共有 {{ collaborator.length }} 位协作者和 {{ reviewer.length }} 位批阅者一起编写
    </div>
    <el-button @click="handleAdd" type="primary" class="team-btn">账户邀请</el-button>
    <div class="flex flex-ai-center mt15">
      <span class="f-14 c-33">设置邀请成员身份</span>
      <el-dropdown @command="handleCommand" trigger="click" class="ml5">
        <div class="f-14 c-primary flex flex-ai-center ml5 pointer">
          {{ roleCode == 3 ? '批阅者' : '协作者' }}
          <el-icon class="ml5"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="3">批阅者</el-dropdown-item>
            <el-dropdown-item command="2">协作者</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-divider />
    <div class="h-title">所有者</div>
    <div v-for="item in owner" :key="item.id" class="team-personnel mt15">
      <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="team-personnel-avatar" />
      {{ item.teamMemberName }}
    </div>
    <el-divider />
    <div class="h-title">协作者（{{ collaborator.length }}）</div>
    <template v-if="collaborator && collaborator.length > 0">
      <div v-for="item in collaborator" :key="item.id" class="flex flex-sb flex-ai-center mt15">
        <div class="team-personnel">
          <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="team-personnel-avatar" />
          {{ item.teamMemberName }}
        </div>
        <div class="f-14 c-33">
          设为
          <span @click="handleSet(item, 3)" class="c-primary pointer">批阅者</span>
          <i @click="handleDelete(item, 2)" class="iconfont icon-remove-1-copy status-red pointer ml20"></i>
        </div>
      </div>
    </template>
    <empty v-else />
    <el-divider />
    <div class="h-title">批阅者（{{ reviewer.length }}）</div>
    <template v-if="reviewer && reviewer.length > 0">
      <div v-for="item in reviewer" :key="item.id" class="flex flex-sb flex-ai-center mt15">
        <div class="team-personnel">
          <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="team-personnel-avatar" />
          {{ item.teamMemberName }}
        </div>
        <div class="f-14 c-33">
          设为
          <span @click="handleSet(item, 2)" class="c-primary pointer">协作者</span>
          <i @click="handleDelete(item, 3)" class="iconfont icon-remove-1-copy status-red pointer ml20"></i>
        </div>
      </div>
    </template>
    <empty v-else />
  </el-drawer>
  <choose-person-dialog
    v-if="personVisible"
    v-model:visible="personVisible"
    :tags="userIdList"
    :filterUser="true"
    @handleChoose="personData"
  />
</template>

<script setup>
  import {
    getTeamInfo,
    addStandardWriteTeam,
    putStandardWriteTeam,
    removeStandardWriteTeam,
  } from '@/api/application_tool/standard_redact';
  import ChoosePersonDialog from '@/views/components/standard_revision/ChoosePersonDialog.vue';

  const emit = defineEmits(['update:visible', 'updateData']);
  const avatarImg = new URL('@/assets/images/avatar.png', import.meta.url).href;
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
    standardName: {
      type: [Number, String],
    },
  });

  const roleCode = ref('3');
  const owner = ref([]);
  const collaborator = ref([]);
  const reviewer = ref([]);
  const userIdList = ref([]);
  const personVisible = ref(false);

  onMounted(() => {
    if (props.id) getInfo();
  });

  const getInfo = () => {
    getTeamInfo(props.id).then(res => {
      let data = res.data;
      owner.value = data.owner;
      collaborator.value = data.collaborator;
      reviewer.value = data.reviewer;
      userIdList.value = data.userIdList;
    });
  };

  const handleAdd = () => {
    personVisible.value = true;
  };

  const handleCommand = val => {
    roleCode.value = val;
  };

  const personData = val => {
    addStandardWriteTeam({
      writeId: props.id,
      teamMemberIdList: val.map(item => item.userId),
      teamMemberRoleId: roleCode.value,
    }).then(() => {
      proxy.$message.success('邀请成员添加成功！');
      getInfo();
    });
  };

  const handleSet = (row, role) => {
    proxy
      .$confirm('确认将【' + row.teamMemberName + '】设置为' + (role == 2 ? '“协作者”' : '“批阅者”') + '？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        putStandardWriteTeam({
          id: row.id,
          writeId: props.id,
          teamMemberId: row.teamMemberId,
          teamMemberRoleId: role,
        }).then(() => {
          proxy.$message.success('设置成功！');
          getInfo();
        });
      })
      .catch(() => {});
  };

  const handleDelete = (row, role) => {
    proxy
      .$confirm('确认移除' + (role == 2 ? '协作者' : '批阅者') + '【' + row.teamMemberName + '】？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        removeStandardWriteTeam({ ids: [row.id] }).then(() => {
          proxy.$message.success('移除成功！');
          getInfo();
        });
      })
      .catch(() => {});
  };

  const handleClose = () => {
    emit('updateData');
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .team {
    &-btn {
      width: 100% !important;
      height: 40px !important;
      margin-top: 20px !important;
      font-weight: 400 !important;
    }

    &-personnel {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      font-weight: 600;

      &-avatar {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 15px;
      }
    }
  }

  :deep(.empty-img) {
    margin: 20px auto !important;
    width: 200px !important;
    height: auto !important;
  }
</style>
