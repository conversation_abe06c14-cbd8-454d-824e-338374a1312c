<template>
  <div>
    <el-dialog
      v-model="open"
      :title="title"
      :before-close="close"
      width="90%"
    >
    <process-designer
      key="designer"
      style="border:1px solid rgba(0, 0, 0, 0.1);"
      ref="modelDesigner"
      v-loading="designerData.loading"
      :bpmnXml="designerData.bpmnXml"
      :designerForm="designerData.form"
      @save="onSaveDesigner"
    />
     
    </el-dialog>
  </div>
</template>

<script setup name="BpmModelEditor">
import { getProcessSettingDetail,saveWorkflow } from "@/api/approve/process_setting";
import ProcessDesigner from '@/components/ProcessDesigner'

const { proxy } = getCurrentInstance();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  modelId: {
    type: String,
    default: undefined,
  }
});
const { open, modelId } = toRefs(props);

const data = reactive({
  loading: false,
  title: '流程设计',
  openDesigner: true,
  designerData: {
    loading: false,
    bpmnXml: '',
    modelId: null,
    form: {
      processName: null,
      processKey: null
    }
  },
});
const { loading, title, openDesigner, designerData } = toRefs(data);

const close = () => {
  emit("success");
  emit("update:open", false);
};

const emit = defineEmits(["update:open", "success"]);

/** 初始化 */
onMounted(async () => {
  getData()
})

const getData = () => {
  data.designerData.loading = true;

  getProcessSettingDetail(props.modelId).then(response => {
    if (response.data) {
      data.designerData.bpmnXml = response.data || '';

    }
    data.designerData.loading = false;
  }).catch(() => {
    data.designerData.loading = false;
  });
}
const onSaveDesigner = (bpmnXml) => {
  let dataBody = {
    modelId: props.modelId,
    bpmnXml: bpmnXml,
    newVersion: true
  }
  data.designerData.loading = true;
  saveWorkflow(dataBody).then(() => {
    emit('update:open',false)
    emit('success')
    proxy.$modal.msgSuccess('保存流程成功')
  }).finally(() => {
    data.designerData.loading = false;
  })
}

</script>

<style lang="scss" scoped>
.process-panel__container {
  position: absolute;
  right: 60px;
  top: 90px;
}
</style>