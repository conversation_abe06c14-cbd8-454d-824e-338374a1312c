import request from '@/utils/request'

// 我的待办

// 获取待办列表/我的待办
export const getTodoList = (params) => {
  return request({
    url: '/workflow/process/todoList',
    method: 'get',
    params
  })
}
// 查询流程详情信息
export const getProcessDetail = (params) => {
  return request({
    url: '/workflow/process/detail',
    method: 'get',
    params
  })
}
// 同意
export const agreeProcess = (data) => {
  return request({
    url: '/workflow/task/complete',
    method: 'post',
    data
  })
}
// 驳回
export const rejectProcess = (data) => {
  return request({
    url: '/workflow/task/reject',
    method: 'post',
    data
  })
}