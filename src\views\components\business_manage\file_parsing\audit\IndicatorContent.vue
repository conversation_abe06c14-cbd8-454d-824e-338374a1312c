<template>
  <div class="content-wrap">
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
      <el-form-item label="指标项" prop="keyword">
        <el-input style="width:300px" v-model.trim="queryParams.keyword" placeholder="请输入指标表格中指标项名称" />
      </el-form-item>
      <el-form-item label="" class="ml20">
        <el-button @click="getData" type="primary">提取</el-button>
      </el-form-item>
    </el-form>

    <div v-loading="loading" class="list mt20 bxcjx-content-wrap">
      <template v-if="dataList.length > 0">
        <div v-for="item in dataList" :key="item.standardId" class="item mt20">
          <div v-html="item.content"></div>
        </div>
      </template>
      <template v-else>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </div>
  </div>

</template>
<script setup>
import { getExtraction } from "@/api/business_manage/file_parsing";

const { proxy } = getCurrentInstance()

const props = defineProps(['id'])

const queryParams = ref({
  id: props.id,
  type: 1,
  keyword: "",
});
const dataList = ref([]);
const loading = ref(false);

const getData = () => {
  if (!queryParams.value.keyword) {
    proxy.$modal.msgError("请输入指标项");
    return
  }
  loading.value = true;
  getExtraction(queryParams.value).then(res => {
    
    if(res.data){
      dataList.value = res.data.tableList || [];
      console.log(dataList.value)
    }

  }).finally(() => {
    loading.value = false;
  })
};
</script>
<style lang="scss" scoped>
.bxcjx-content-wrap{
  min-height: 300px;
}
</style>
