<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="新建知识库"
      :before-close="handleClose"
      width="660px"
    >
      <!-- <el-scrollbar height="590px"> -->
      <div class="h-title">基本信息</div>
      <el-form
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5"
      >
        <el-form-item label="知识库名称" prop="name">
          <el-input
            size="large"
            maxlength="50"
            v-model="form.name"
            placeholder="请输入知识库名称"
          />
        </el-form-item>
        <el-form-item label="知识库封面" prop="cover">
          <image-cropper
            v-model:coverImage="form.cover"
            @uploadEnd="uploadEnd"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input
            size="large"
            maxlength="4"
            v-model="form.sort"
            placeholder="请输入排序值，1-9999的正整数（按数值由大到小排序）"
          />
        </el-form-item>
        <el-form-item label="知识库描述">
          <el-input
            v-model="form.describe"
            :rows="5"
            :show-word-limit="true"
            maxlength="300"
            type="textarea"
            placeholder="请输入知识库描述说明信息"
          />
        </el-form-item>
        <el-form-item label="可见范围" prop="visibleRange">
          <el-select
            @change="handleSelect"
            v-model="form.visibleRange"
            size="large"
            class="w-290"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.visibleRange == 1"
          label="知识库成员"
          prop="member"
        >
          <b-tags v-model:tags="form.member" />
        </el-form-item>
      </el-form>
      <!-- </el-scrollbar> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { addKnowledge } from "@/api/knowledge/library";
import BTags from "@/components/BTags";
import ImageCropper from "@/components/ImageCropper";
import { ElMessage, ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { dialogVisible } = toRefs(props);

const data = reactive({
  loading: false,
  form: {
    visibleRange: 1,
    member: [],
    sort: undefined,
  },
  options: [
    {
      value: 1,
      label: "私有：只有加入的成员才能看见此项目",
    },
    {
      value: 0,
      label: "公开：企业所有成员都可以看见此项目",
    },
  ],
  rules: {
    name: [
      {
        required: true,
        message: "请输入知识库名称",
        trigger: "blur",
      },
    ],
    cover: [
      {
        required: true,
        message: "请上传知识库封面",
        trigger: "change",
      },
    ],
    sort: [{ pattern: /^[1-9]{1}[0-9]{0,3}$/, message: "请输入1-9999的正整数", trigger: "blur" }]
  },
});
const { loading, form, options, rules } = toRefs(data);

const handleSelect = (val) => {
  if (val == 0) {
    form.value.member = [];
  }
};

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      addKnowledge(form.value)
        .then((res) => {
          ElMessage({
            type: "success",
            message: "新建成功",
          });
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const uploadEnd = () => {
  proxy.$refs.queryRef.validateField("cover");
};

const handleClose = () => {
  proxy.$refs.queryRef.resetFields();
  emit("getList");
  emit("update:dialogVisible", false);
};

const emit = defineEmits(["update:dialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
.w-290 {
  width: 290px;
}
</style>