import request from '@/utils/request'

// 标准反馈列表
export const standardFeedbackInfoListAll = (params) => {
  return request({
    url: '/process/standardFeedbackInfo/listAll',
    method: 'get',
    params
  })
}
// 标准反馈处理
export const standardFeedbackInfoHandle = (data) => {
  return request({
    url: '/process/standardFeedbackInfo/handle',
    method: 'put',
    data
  })
}

//详情
export const standardFeedbackDetail = (id) => {
  return request({
    url: '/process/standardFeedbackInfo/' + id,
    method: 'get'
  })
}

//详情
export const standardFeedbackInfo = (id) => {
  return request({
    url: '/process/solicitOpinionFeedback/' + id,
    method: 'get'
  })
}