<template>
  <div>
    <el-dialog
      width="1050"
      :title="id ? '编辑公告' : '新增公告'"
      append-to-body
      v-model="dialogVisible"
      :before-close="handleClose"
    >
      <el-form
        inline
        label-width="auto"
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5 dia-form"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            maxlength="50"
            placeholder="请输入标题"
            v-model="form.title"
          />
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input
            v-model="form.summary"
            maxlength="100"
            placeholder="请输入摘要"
          />
        </el-form-item>
        <el-form-item label="范围" prop="scope">
          <el-radio-group @change="handleChange" v-model="form.scope" class="ml-4">
            <el-radio v-for="item in bxc_notice_laws_scope"  :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="form.scope == '1'"
          prop="deptList"
        >
          <dept-tag v-model:tags="form.deptList" />
        </el-form-item>
        <el-form-item
          v-if="form.scope == '2'"
          prop="personList"
        >
          <staff-tag v-model:tags="form.personList" />
        </el-form-item>
        <el-form-item label="源发布时间" prop="sourcePublishDate">
          <el-date-picker
            v-model="form.sourcePublishDate"
            type="date"
            placeholder="请选择源发布时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="来源/部门" prop="source">
          <el-input
            maxlength="50"
            placeholder="请输入来源/部门"
            v-model="form.source"
          />
        </el-form-item>
        <el-form-item label="正文内容" prop="content" class="one-column">
          <editor v-model.trim="form.content" :height="300" />
        </el-form-item>
        <el-form-item label="附件" prop="noticeFileList" class="one-column">
          <div>
            <ele-upload-image
              :multiple="true"
              :responseFn="handleResponse"
              :fileType="fileType"
              v-model:value="form.noticeFileList"
              :fileSize="5"
              :limit="fileLimit"
              @success="handleUpload('noticeFileList')"
            />
            <div class="c-EE0000 flex upload-notice flex-ai-center">
              
              <i class="iconfont icon-tishi f-18 mr5"></i>
              支持文件格式：jpeg、jpg、png、bmp、doc、docx、xls、xlsx、ppt、pptx、pdf；单个文件大小不超过5MB
            </div>
          </div>
        </el-form-item>
        <el-form-item label="是否有关联标准" prop="isJoin" class="one-column">
          <el-radio-group v-model="form.isJoin" class="ml-4">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.isJoin == 1" prop="itemList" class="one-column">
          <!-- 条件区 -->
          <div class="w-30">
            <div class="search">
              <el-autocomplete
                style="width: 100%;"
                v-model="form.searchStr"
                value-key="standardName"
                :fetch-suggestions="((queryString,cb)=>{querySearchAsync(queryString,cb,0)})"
                placeholder="请搜索及选择需要的标准"
                @select="handleSelect"
                clearable
              >
                <template #default="{ item }">
                  <div class="flex">
                    <div>{{ item.standardCode }}</div>
                    <div class="ml10" style="margin-right: auto;">{{ item.standardName }}</div>
                    <div class="ml10 f-18 c-primary flex flex-ai-center flex-shrink"><el-icon><CirclePlusFilled /></el-icon></div>
                  </div>
                </template>
              </el-autocomplete>
            </div>
          </div>

          <!-- 表格区 -->
          <el-table
            v-loading="loading"
            ref="tableRef"
            :data="form.itemList"
            class="mt15 mb20"
            :border="true"
          >
            <el-table-column type="index" align="center" min-width="55" fixed="left" />
            <el-table-column
              prop="standardCode"
              label="标准号"
              min-width="200"
              show-overflow-tooltip
              fixed="left"
            />
            <el-table-column
              prop="standardName"
              label="标准名称"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="standardTypeName"
              label="标准类型"
              min-width="150"
              show-overflow-tooltip
            />
            <el-table-column
              prop="standardStatusName"
              label="标准状态"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="executeDate"
              label="实施日期"
              min-width="180"
              show-overflow-tooltip
            />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
              <template #default="scope">
                <el-button
                  type="danger"
                  icon="Delete"
                  link
                  @click="handleItemDelete(scope.$index,form.itemList)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        
        
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button  @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import DeptTag from "@/views/components/notice_rule/Tags/DeptTag";
import StaffTag from "@/views/components/notice_rule/Tags/StaffTag";
import Editor from '@/components/TinymceEditor'
import EleUploadImage from "@/components/EleUploadImage";
import {addNoticeLaws,getNoticeManageDetail,editNoticeManage} from '@/api/notice_rule/manage.js'
import { getStandardList } from '@/api/standard_manage/standard_query';

import { toRefs } from "vue";
import { useDateFormat } from "@vueuse/core";


const { proxy } = getCurrentInstance();
const  {bxc_notice_laws_scope}  = proxy.useDict('bxc_notice_laws_scope');


const props = defineProps({
  id: null,
  pid: null,
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { id, dialogVisible,pid } = toRefs(props);

const data = reactive({
  currentId:null,
  clientDialog: false,
  clientContactDialog: false,
  loading: false,
  options: [],
  form: {
    pid:null,
    isJoin:'0',
    scope:'0',
    searchStr: '',
    itemList: [],
    personList:[],
    deptList:[]
  },
  fileLimit:9,
  fileType:['png', 'jpg', 'jpeg', 'bmp', 'gif','bmp','pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  rules: {
    title:[
      {
        required: true,
        message: "请输入标题",
        trigger: "blur",
      },
    ],
    scope:[
      {
        required: true,
        message: "请选择范围",
        trigger: "blur",
      },
    ],
    deptList:[
      {
        required: true,
        message: "请选择部门",
        trigger: "blur",
      },
    ],
    personList:[
      {
        required: true,
        message: "请选择人员",
        trigger: "blur",
      },
    ],
    itemList:[
      {
        required: true,
        message: "请选择关联标准",
        trigger: "blur",
      },
    ],
    
    source:[
      {
        required: true,
        message: "请输入来源",
        trigger: "blur",
      },
    ],
    content:[
      {
        required: true,
        message: "请输入正文",
        trigger: "blur",
      },
    ],
    
  },
});
const {loading, form, rules,fileType,fileLimit } = toRefs(data);

onMounted(() => {
  if (id.value) getDetail();
});

const getDetail = () => {
  getNoticeManageDetail(id.value).then((res) => {
    let data = res.data;
    if(data.standardList){
      form.value.itemList = data.standardList;
    }
    form.value = Object.assign(form.value,data);
    form.value.noticeFileList = data.noticeFileList || [];
  });
};

const handleResponse = (response, file, fileList) => {
  return { id: response.data.id, url: response.data.url, name: response.data.name };;
};


const handleUpload = (type) => {
  nextTick(() => {
    proxy.$refs.queryRef.validateField(type);
  });
};

const handleChange = () => {
  // data.form.personList = null;
  // data.form.deptList = null;
}


//删除标准
const handleItemDelete = (index,arr) => {
  arr.splice(index,1)
}

const querySearchAsync = (queryString,cb,index) => {
  let list = data.form.itemList.map(item=>item.standardId)
  let params = {
    title: queryString,
    searchStandardIdList: list,
    pageNum: 1,
    pageSize: 10
  }
  
  getStandardList(params).then((response) => {
    if(response.rows){
      cb(response.rows)
      data.currentId = index
    }
  }).catch(() => {
    cb()
  });
}
const handleSelect = (item) => {
  if(data.currentId != undefined){
    let idx = data.form.itemList.findIndex(obj => obj.standardId == item.standardId)
    
    if(idx == -1) {
      data.form.itemList.push(item)
    }
    
    data.form.searchStr = undefined
    document.activeElement.blur()
  }
}


const handleConfirm = () => {

  data.form.personIdList = data.form.personList.map(item => item.id)
  data.form.deptIdList = data.form.deptList.map(item => item.id)
  data.form.pid = pid.value;

  data.form.standardIdList = data.form.itemList.map(item => {
    return item.standardId; 
  })
  //如果没有关联标准
  if(data.form.isJoin == '0'){
    data.form.standardIdList = []
  }
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (id.value) {
        editNoticeManage(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('更新成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addNoticeLaws(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('新建成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
};

const handleClose = () => {
  emit("update:dialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

const emit = defineEmits(["update:dialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
.dia-form{
  width: 100%;
  .el-form-item{
    width: 50%;
  }
  .one-column {
    flex: 0 0 calc(95% + 30px) !important;
  }
}
.upload-notice{
  margin-top: 5px;
  img{
    height: 18px;
    margin-right: 8px;
  }
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper){
  width: 100% !important;
}

.w-30{
  width: 30%;
}


</style>
