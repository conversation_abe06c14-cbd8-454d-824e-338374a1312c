<template>
  <div>
    <el-dialog
      width="930px"
      :title="id ? '编辑专家' : '新增专家'"
      append-to-body
      v-model="addDialogVisible"
      :before-close="handleClose"
    >
      <el-form
        :inline="true"
        label-width="auto"
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="dialog-form-inline"
      >
        <el-form-item label="姓名" prop="name">
          <el-input
            maxlength="50"
            placeholder="请输入姓名"
            v-model="form.name"
          />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select clearable filterable v-model="form.sex" placeholder="请选择性别">
            <el-option v-for="item in sys_user_sex" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="contactNumber">
          <el-input
            maxlength="50"
            placeholder="请输入联系方式"
            v-model="form.contactNumber"
          />
        </el-form-item>
        <el-form-item label="是否为外聘专家" prop="isExternalExpert">
          <el-select clearable filterable v-model="form.isExternalExpert" placeholder="请选择">
            <el-option v-for="item in yes_no" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作单位" prop="workUnit">
          <el-input
            maxlength="50"
            placeholder="请输入工作单位"
            v-model="form.workUnit"
          />
        </el-form-item>
        <el-form-item label="职务" prop="job">
          <el-input
            v-model="form.job"
            maxlength="100"
            placeholder="请输入职务"
          />
        </el-form-item>
        <el-form-item label="企业地址" prop="enterpriseAddress">
          <el-input
            maxlength="50"
            placeholder="请输入企业地址"
            v-model="form.enterpriseAddress"
          />
        </el-form-item>
        <el-form-item label="所在区域" prop="regionCode">
          <el-cascader
          clearable
          ref="areaRef"
          v-model="form.regionCode"
          :options="areaOptions"
          :props="areaProps"></el-cascader>
        </el-form-item>
        <el-form-item label="委员会名称" prop="committeeName">
          <el-input
            maxlength="50"
            placeholder="请输入委员会名称"
            v-model="form.committeeName"
          />
        </el-form-item>
        <el-form-item label="委员会职务" prop="committeeJob">
          <el-input
            maxlength="50"
            placeholder="请输入委员会职务"
            v-model="form.committeeJob"
          />
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <el-select clearable filterable v-model="form.education" placeholder="请选择学历">
            <el-option v-for="item in bxc_expert_education" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="出生年月" prop="birthDate">
          <el-date-picker
            v-model="form.birthDate"
            type="date"
            placeholder="请选择出生年月"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="擅长描述" class="one-column" prop="proficientDesc">
          <el-input
            v-model="form.proficientDesc"
            :rows="5"
            :show-word-limit="true"
            maxlength="300"
            type="textarea"
            placeholder="请输入擅长描述信息"
          />
        </el-form-item>

        
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button  @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addStcExpert,getStcExpertDetail,editStcExpert} from '@/api/standard_manage/standard_expert.js'

import {getDistrictList} from '@/api/common.js'

import { toRefs } from "vue";


const { proxy } = getCurrentInstance();
const  {bxc_expert_education,yes_no,sys_user_sex}  = proxy.useDict('bxc_expert_education','yes_no','sys_user_sex');


const props = defineProps({
  id: null,
  addDialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { id, addDialogVisible } = toRefs(props);

const data = reactive({
  areaOptions: [],
  areaProps: {
    emitPath: false,
    checkStrictly: false,
    expandTrigger: 'hover',
    label: 'name',
    value: 'code',
    children: 'childRegion'
  },
  currentId:null,
  loading: false,
  form: {},
  rules: {
    name:[
      {
        required: true,
        message: "请输入姓名",
        trigger: "blur",
      },
    ],
    sex:[
      {
        required: true,
        message: "请选择性别",
        trigger: "blur",
      },
    ],
    contactNumber:[
      { required: true, trigger: "blur", message: "联系电话不能为空" },
      { 
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, 
        message: "请输入正确的手机号码", 
        trigger: "blur" 
      }
    ],
    isExternalExpert:[
      {
        required: true,
        message: "请选择是否为外聘专家",
        trigger: "blur",
      },
    ],
    workUnit:[
      {
        required: true,
        message: "请输入工作单位",
        trigger: "blur",
      },
    ],
    regionCode:[
      {
        required: true,
        message: "请选择所在区域",
        trigger: "blur",
      },
    ],
  },
});
const {loading, form, rules,areaOptions,areaProps } = toRefs(data);

onMounted(() => {
  if (id.value) getDetail();
});

const getDetail = () => {
  getStcExpertDetail(id.value).then((res) => {
    let data = res.data;
    form.value = data;
  });
};

const getAreaTree = () => {
  getDistrictList().then((response) => {
    data.areaOptions = response.data;
  }).catch((err) => {
    console.log(err)
  })
}

getAreaTree()


const handleConfirm = () => {

  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (id.value) {
        editStcExpert(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('更新成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addStcExpert(form.value)
          .then(() => {
            proxy.$modal.msgSuccess('新建成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
};

const handleClose = () => {
  emit("update:addDialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

const emit = defineEmits(["update:addDialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
</style>
