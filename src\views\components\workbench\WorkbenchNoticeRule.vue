<template>
  <div class="standard-query-wrap">
    <div class="flex flex-sb">
      <div class="f-22 f-bold c-33 mb10">最新公告法规</div>
      <el-button @click="handleMore" type="primary" link>
        更多<el-icon class="el-icon--right"><ArrowRight /></el-icon>
      </el-button>
    </div>
    <div style="position: relative;">
      <el-tabs v-model="activeName" class="workbench-tabs" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in info" :key="index" :label="item.title" :name="index">
          <workbench-notice-rule-list :list="item.list" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import WorkbenchNoticeRuleList from '@/views/components/workbench/WorkbenchNoticeRuleList'
import { getNoticeList } from '@/api/workbench'

const { proxy }= getCurrentInstance()

const data = reactive({
  activeName: 0,
  info: {}
})
const { activeName,info } = toRefs(data)
const handleClick = (val) => {
  
}
const handleMore = () => {
  proxy.$router.push('/notice_rule/notice_rule')
}
const getData = () => {
  data.loading = true;
  getNoticeList({pageSize: 5}).then((response) => {
    if(response.data){
      data.info = response.data
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
getData()
</script>

<style lang="scss" scoped>

.more-btn{
  position: absolute;
  right: 0;
  top: 10px;
}
.standard-query-wrap {
  padding: 30px;
  flex: 1;
  height: 416px;
  background: #FFFFFF;
  border-radius: 15px;
  box-sizing: border-box;
  overflow: hidden;
}
</style>