import request from '@/utils/request'

//收藏列表
export const userCollectInfoList = (data) => {
  return request({
    url: '/process/userCollectInfo/list',
    method: 'get',
    params:data
  })
}

//下载列表
export const userDownloadInfoList = (data) => {
  return request({
    url: '/process/userDownloadInfo/list',
    method: 'get',
    params:data
  })
}

// 反馈列表
export const standardFeedbackInfoList = (params) => {
  return request({
    url: '/process/standardFeedbackInfo/list',
    method: 'get',
    params
  })
}