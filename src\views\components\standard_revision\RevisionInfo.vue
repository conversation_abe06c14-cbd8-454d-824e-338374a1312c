<template>
  <div class="h-title">项目信息</div>
  <el-descriptions :column="3" class="mt10 pl10">
    <el-descriptions-item label="项目编号：">{{ form.projectCode || '-' }}</el-descriptions-item>
    <el-descriptions-item label="项目名称：">{{ form.projectName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="项目负责人：">{{ form.projectManagerName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="制修订类型：">{{ form.amendName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="标准号：">{{ form.standardCode || '-' }}</el-descriptions-item>
    <el-descriptions-item label="标准名称：">{{ form.standardName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="计划开始日期：">{{ form.planStartTime || '-' }}</el-descriptions-item>
    <el-descriptions-item label="计划完成日期：">{{ form.planEndTime || '-' }}</el-descriptions-item>
    <template v-if="form.amend == 1">
      <el-descriptions-item label="替代标准号：">{{ form.beReplacedStandardCode || '-' }}</el-descriptions-item>
      <el-descriptions-item label="替代标准名称：">{{ form.beReplacedStandardName || '-' }}</el-descriptions-item>
    </template>
    <el-descriptions-item label="创建人：">{{ form.createByName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="创建时间：">{{ form.createTime || '-' }}</el-descriptions-item>
    <el-descriptions-item label="项目介绍：" :span="3">{{ form.projectIntroduce || '-' }}</el-descriptions-item>
  </el-descriptions>
  <div class="h-title mt-18">项目成员</div>
  <div class="pl10">
    <el-table :data="form.memberList" :border="true" class="mt15">
      <el-table-column label="序号" type="index" fixed width="55" />
      <el-table-column prop="name" label="姓名" show-overflow-tooltip fixed min-width="150" />
      <el-table-column prop="contactNumber" label="联系方式" show-overflow-tooltip min-width="150" />
      <el-table-column prop="memberTypeName" label="成员类型" show-overflow-tooltip min-width="120" />
      <el-table-column prop="unit" label="所在部门/单位" show-overflow-tooltip min-width="180" />
    </el-table>
  </div>
  <template v-if="props.isDetail && form.archiveStatus == 1">
    <div class="h-title mt15">归档信息</div>
    <el-descriptions :column="3" class="mt10 pl10">
      <el-descriptions-item label="归档人：">{{ form.archiveName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="归档时间：">{{ form.archiveDate || '-' }}</el-descriptions-item>
    </el-descriptions>
  </template>
</template>

<script setup>
  import { getRevisionDetail } from '@/api/standard_revision/manage';

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const form = ref({});

  getRevisionDetail(props.id).then(res => {
    form.value = res.data;
    if (form.value.status) emit('update:activeStep', Number(form.value.status));
  });

  const emit = defineEmits(['update:activeStep']);
</script>

<style lang="scss" scoped>
  .mt-18 {
    margin-top: 18px;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
