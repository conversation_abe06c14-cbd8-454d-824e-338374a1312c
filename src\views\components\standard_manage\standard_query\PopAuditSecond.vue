<template>
<el-dialog :append-to-body="true" v-model="open" width="40%" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close" class="pop-container">
  <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="mt-10">
    <el-form-item label="审核结果" prop="analysisStatus">
      <el-button :type="form.analysisStatus == '2' ? 'primary' : 'default'"  size="large" @click="changeResult('2')" >重新审核</el-button>
      <el-button :type="form.analysisStatus == '0' ? 'primary' : 'default'" size="large" @click="changeResult('0')" >重新解析</el-button>
      <el-button :type="form.analysisStatus == '3' ? 'primary' : 'default'" size="large" @click="changeResult('3')" >不再解析</el-button>
    </el-form-item>
    <div v-if="form.analysisStatus == '2'" class="tip-box">
      <span>重新审核，数据解析状态将重置为“待审核”，进行重新审核</span>
    </div>
    <div v-if="form.analysisStatus == '0'" class="tip-box">
      <span>重新解析，数据解析状态将重置为“待解析”，进行重新解析</span>
    </div>
    <div v-if="form.analysisStatus == '3'" class="tip-box">
      <span>不再解析，数据解析状态将设置为“不解析”</span>
    </div>
  </el-form>

  <template #footer>
    <div class="dialog-footer">
      <el-button @click="close">关闭</el-button>
      <el-button @click.prevent="finish" type="primary" :loading="finishLoading">
        <span v-if="!finishLoading">确认</span>
        <span v-else>确认中...</span>
      </el-button>
    </div>
  </template>
</el-dialog>
</template>
<script setup>
import { auditSecond } from "@/api/standard_analysis/analysis_audit";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  standardItem: {
    type: Object,
    default: {}
  }
});
const { open,standardItem } = toRefs(props)

const title = ref('二次审核结果')
const finishLoading = ref(false)
const form = ref({
  standardId: undefined,
  analysisStatus: undefined
})
const rules = ref({
  analysisStatus: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
});
onMounted(()=>{
  form.value.standardId = standardItem.value.standardId
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const changeResult = (val) => {
  form.value.analysisStatus = val
}
const finish = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      finishLoading.value = true
      auditSecond(form.value).then(response => {
        finishLoading.value = false;
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("数据二次审核完成");
      }).catch(error => {
        finishLoading.value = false;
      });
    }
  });
}

</script>
<style lang="scss" scoped>
.tip-box {
  padding: 15px 20px;
  background: #EFF6FF;
  border-radius: 5px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
:deep(.el-form-item){
  margin-right: 0px !important;
}
// :deep(.el-form-item__label){
//   font-size: 14px !important;
// }
</style>