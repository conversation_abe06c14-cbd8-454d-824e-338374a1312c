<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" :lock-scroll="false" v-model="open" width="1000px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <el-form ref="queryRef" :model="queryParams" @submit.prevent>
        <el-form-item label="" prop="keyword">
          <el-input
            size="large"
            :style="{width: '450px'}"
            v-model="queryParams.keyword"
            placeholder="检索标准号或标准名称"
            @keyup.enter="handleQuery"
          >
            <template #append>
              <div @click="handleQuery" class="search">
                <el-icon><Search /></el-icon>
              </div> 
            </template>
          </el-input>
          <div @click="resetQuery" class="refresh">
            <el-icon><Refresh /></el-icon>
          </div> 
        </el-form-item>
      </el-form>

      <el-table 
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        highlight-current-row
        @row-click="handleRowClick"
        class="mt20"
        :border="false">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" min-width="55">
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" label="标准号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="100" show-overflow-tooltip />
        <el-table-column prop="standardStatusName" label="标准状态" min-width="100" show-overflow-tooltip />
        <el-table-column prop="publishDate" label="发布日期" min-width="120" show-overflow-tooltip />
        <el-table-column prop="standardTextPages" label="页数" min-width="70" show-overflow-tooltip />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="mt30"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close" plain>取消</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">确定</span>
            <span v-else>确定中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getStandardList } from '@/api/application_tool/comparison'

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  starndardInfo: {
    // type: Object,
    type: Object,
    default: () => {
      return {}
    } 
  },
  selectedIds:{
    type: Array,
    default: () => {
      return []
    }
  }
});
const { open } = toRefs(props)

const title = ref('选择标准')
const loading = ref(false)
const queryRef = ref()
const tableRef = ref()
const total = ref(0)
const dataList = ref([])
const selectedList = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  searchStandardIdList: []
})

const emit = defineEmits(['update:open','selected'])

const close = () => {
  emit('update:open',false)
}
const resetQuery = () => {
  queryRef?.value.resetFields();
  handleQuery();
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
const getList = () => {
  getStandardList(queryParams.value).then((res) => {
    total.value = res.total || 0
    dataList.value = res.rows || []

    if(selectedList.value.length > 0){
      dataList.value.forEach((item) => {
        if(selectedList.value[0].id == item.id){
          nextTick(() => {
            tableRef.value.setCurrentRow(item)
          })

        }
      })
    }
  })
}
const handleRowClick = (row) => {
  tableRef.value.clearSelection();
  selectedList.value = [row];
  tableRef.value.toggleRowSelection(row, true);
}
const save = () => {
  if(selectedList.value.length == 0){
    proxy.$modal.msgError('请选择标准')
    return
  }
  emit('selected',selectedList.value[0])
  close()
}
const init = () => {
  if(JSON.stringify(props.starndardInfo) != '{}'){
    selectedList.value = [props.starndardInfo]
  }
  queryParams.value.searchStandardIdList = props.selectedIds
}

onMounted(() => {
  init()
  getList()
})

</script>
<style lang="scss" scoped>
.search{
  width: 40px;
  height: 100%;
  text-align: center;
  background: $primary-color;
  font-size: 16px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0px 3px 3px 0px;
  cursor: pointer;
  .el-icon{
    color: #FFFFFF !important;
    height: 14px;
    width: 14px;
  }
}
.refresh{
  margin-left: 10px;
  width: 40px;
  height: 100%;
  text-align: center;
  font-size: 16px;
  background: #FFFFFF;
  border-radius: 5px;
  border: 1px solid #D2D9E9;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .el-icon{
    color: #333333 !important;
    height: 14px;
    width: 14px;
  }
}
:deep(.el-input-group__append){
  padding: 0px;
}
</style>