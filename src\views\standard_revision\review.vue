<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="关键词:" prop="keyword">
          <el-input @keyup.enter="handleQuery" v-model="queryParams.keyword" placeholder="请输入项目编号、项目名称" />
        </el-form-item>
        <el-form-item label="项目阶段:" prop="phase">
          <el-select v-model="queryParams.phase" placeholder="请选择项目阶段" clearable>
            <el-option v-for="dict in bxc_project_review_phase" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目状态:" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择项目状态" clearable>
            <el-option v-for="dict in bxc_project_review_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人:" prop="projectManagerId">
          <el-cascader
            v-model="queryParams.projectManagerId"
            filterable
            clearable
            :options="memberTreeList"
            :show-all-levels="false"
            :props="{ emitPath: false, label: 'name', value: 'id' }"
            placeholder="请选择负责人"
          />
        </el-form-item>
        <el-form-item label="创建人:" prop="createId">
          <el-cascader
            v-model="queryParams.createId"
            filterable
            clearable
            :options="memberTreeList"
            :show-all-levels="false"
            :props="{ emitPath: false, label: 'name', value: 'id' }"
            placeholder="请选择创建人"
          />
        </el-form-item>
        <el-form-item label="创建时间:">
          <el-date-picker
            v-model="createTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="计划完结时间:">
          <el-date-picker
            v-model="planTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="实际完结时间:">
          <el-date-picker
            v-model="actualTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="归档时间:">
          <el-date-picker
            v-model="filingTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button v-hasPermi="['standard_revision:review:add']" type="primary" icon="Plus" @click="handleClick('add')">
          新增
        </el-button>
      </div>
      <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15" :border="true">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" width="60" fixed>
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="200" show-overflow-tooltip fixed />
        <el-table-column prop="projectName" label="项目名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="reviewStandardCount" label="复审范围" min-width="120" show-overflow-tooltip />
        <el-table-column prop="reviewExpertCount" label="复审专家" min-width="120" show-overflow-tooltip />
        <el-table-column prop="phaseName" label="项目阶段" min-width="150" show-overflow-tooltip />
        <el-table-column prop="statusName" label="项目状态" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <span :class="getStatusClass(scope.row.status)">{{ scope.row.statusName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="projectManagerName" label="负责人" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createBy" label="创建人" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="planFinishDate" label="计划完结时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="actualFinishTime" label="实际完结时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="archiveTime" label="归档时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="reviewConclusion" label="复审结论（继续有效/修订/废止）" min-width="230" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <el-button @click="handleClick('detail', row)" link type="primary">查看</el-button>
            <el-button
              v-if="isAuth(row.authorizedUserIdList) && row.status == 0"
              v-hasPermi="['standard_revision:review:edit']"
              @click="handleClick('edit', row)"
              link
              type="primary"
            >
              编辑
            </el-button>
            <el-button
              v-if="isAuth(row.authorizedUserIdList) && row.status == 1"
              v-hasPermi="['standard_revision:review:manage']"
              @click="handleClick('manage', row)"
              link
              type="primary"
            >
              管理
            </el-button>
            <el-button
              v-if="isAuth(row.terminateAuthorizedUserIdList) && row.status == 0"
              v-hasPermi="['standard_revision:review:delete']"
              @click="handleClick('delete', row)"
              link
              type="danger"
            >
              删除
            </el-button>
            <el-button
              v-if="isAuth(row.terminateAuthorizedUserIdList) && row.status == 1"
              v-hasPermi="['standard_revision:review:terminate']"
              @click="handleClick('terminate', row)"
              link
              type="danger"
            >
              终止
            </el-button>
            <el-button
              v-if="row.status == 2"
              v-hasPermi="['standard_revision:review:filing']"
              @click="handleClick('filing', row)"
              link
              type="primary"
            >
              归档
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <add-dialog v-if="addVisible" v-model:visible="addVisible" :id="selectId" @success="handleQuery" />
    <manage-dialog v-if="manageVisible" v-model:visible="manageVisible" :id="selectId" :phase="phase" />
    <detail-drawer v-if="detailVisible" v-model:visible="detailVisible" :id="selectId" :phase="phase" />
  </div>
</template>

<script setup>
  import { getReviewList, putArchive, putTerminate, delReview } from '@/api/standard_revision/review';
  import { memberTree } from '@/api/common';
  import { isAuth } from '@/utils/index';
  import AddDialog from '@/views/components/standard_revision/review/AddDialog.vue';
  import ManageDialog from '@/views/components/standard_revision/review/ManageDialog.vue';
  import DetailDrawer from '@/views/components/standard_revision/review/DetailDrawer.vue';

  const { proxy } = getCurrentInstance();
  const { bxc_project_review_phase, bxc_project_review_status } = proxy.useDict(
    'bxc_project_review_phase',
    'bxc_project_review_status'
  );

  const loading = ref(false);
  const memberTreeList = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const dataList = ref([]);
  const total = ref(0);
  const createTime = ref([]);
  const planTime = ref([]);
  const actualTime = ref([]);
  const filingTime = ref([]);
  const selectId = ref('');
  const phase = ref('');
  const addVisible = ref(false);
  const manageVisible = ref(false);
  const detailVisible = ref(false);

  const getStatusClass = status => {
    switch (Number(status)) {
      case 0:
        return 'status-intro-blue';
        break;
      case 1:
        return 'status-blue';
        break;
      case 2:
        return 'status-green';
        break;
      case 3:
        return 'status-red';
        break;
      case 4:
        return 'status-purple';
        break;
      default:
        return 'status-green';
        break;
    }
  };

  const handleData = data => {
    data.forEach(item => {
      if (item.type == 0) {
        item.disabled = false;
        return;
      }
      if (item.type == 1 && item.children && item.children.length > 0) {
        item.disabled = false;
        handleData(item.children);
      } else {
        item.disabled = true;
      }
    });
    return data;
  };

  memberTree().then(res => {
    memberTreeList.value = handleData(res.data);
  });

  const getList = () => {
    loading.value = true;
    queryParams.value.createStartTime = createTime.value.length > 0 ? createTime.value[0] : '';
    queryParams.value.createEndTime = createTime.value.length > 0 ? createTime.value[1] : '';
    queryParams.value.planFinishStartDate = planTime.value.length > 0 ? planTime.value[0] : '';
    queryParams.value.planFinishEndDate = planTime.value.length > 0 ? planTime.value[1] : '';
    queryParams.value.actualFinishStartTime = actualTime.value.length > 0 ? actualTime.value[0] : '';
    queryParams.value.actualFinishEndTime = actualTime.value.length > 0 ? actualTime.value[1] : '';
    queryParams.value.archiveStartTime = filingTime.value.length > 0 ? filingTime.value[0] : '';
    queryParams.value.archiveEndTime = filingTime.value.length > 0 ? filingTime.value[1] : '';
    getReviewList(queryParams.value)
      .then(res => {
        dataList.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetQuery = () => {
    createTime.value = [];
    planTime.value = [];
    actualTime.value = [];
    filingTime.value = [];
    proxy.$refs.queryFormRef.resetFields();
    handleQuery();
  };

  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const handleClick = (type, row) => {
    switch (type) {
      case 'add':
        selectId.value = '';
        addVisible.value = true;
        break;
      case 'edit':
        selectId.value = row.id;
        addVisible.value = true;
        break;
      case 'detail':
        selectId.value = row.id;
        phase.value = row.phase;
        detailVisible.value = true;
        break;
      case 'manage':
        selectId.value = row.id;
        phase.value = row.phase;
        manageVisible.value = true;
        break;
      case 'filing':
        proxy
          .$confirm('确认归档该项目吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            putArchive(row.id).then(() => {
              proxy.$modal.msgSuccess('已归档！');
              getList();
            });
          })
          .catch(() => {});
        break;
      case 'terminate':
        proxy
          .$confirm('确认终止该项目吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            putTerminate(row.id).then(() => {
              proxy.$modal.msgSuccess('已终止！');
              getList();
            });
          })
          .catch(() => {});
        break;
      case 'delete':
        proxy
          .$confirm('确认删除该项目吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delReview({ ids: [row.id] }).then(() => {
              proxy.$modal.msgSuccess('已删除！');
              getList();
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  getList();
</script>

<style lang="scss" scoped></style>
