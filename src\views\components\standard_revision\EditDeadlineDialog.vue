<template>
  <el-dialog
    width="500px"
    :title="props.title"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="props.deadline">当前截止时间： {{ props.deadline }}</div>
    <el-form ref="queryRef" :model="form" :label-position="'top'" :rules="rules" @submit.prevent>
      <el-form-item label="设置截止时间" prop="deadline">
        <el-date-picker
          :editable="false"
          :clearable="false"
          type="datetime"
          v-model="form.deadline"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          :disabled-date="disabledDay"
          :disabled-hours="disabledHour"
          :disabled-minutes="disabledMinute"
          placeholder="请选择截止时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">更新</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { setDeadline } from '@/api/standard_revision/manage';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
    title: {
      type: String,
      default: '更新截止时间',
    },
    deadline: {
      type: String,
      default: '',
    },
  });

  const checkDadline = (rule, value, callback) => {
    if (Date.parse(value) < Date.now()) {
      callback(new Error('截止时间应大于当前时间'));
    } else {
      callback();
    }
  };

  const loading = ref(false);
  const form = ref({
    id: props.id,
  });
  const rules = reactive({
    deadline: [
      { required: true, message: '请选择意见征求截止日期', trigger: 'blur' },
      { validator: checkDadline, trigger: 'change' },
    ],
  });

  form.value.deadline = props.deadline ? props.deadline : proxy.parseTime(Date.now(), '{y}-{m}-{d} {h}:{i}');

  const disabledDay = data => {
    return data.getTime() < Date.now() - 8.64e7;
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const disabledHour = () => {
  const now = new Date();
  const nowHours = now.getHours();
  if (new Date(form.value.deadline).toDateString() == now.toDateString()) {
    return range(0, nowHours);
  }
};

const disabledMinute = () => {
  const now = new Date();
  const nowHours = now.getHours();
  const nowMinutes = now.getMinutes();
  const currentHours = new Date(form.value.deadline).getHours();
  if ((new Date(form.value.deadline).toDateString() == now.toDateString()) && (currentHours <= nowHours)) {
    return range(0, nowMinutes);
  }
};

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        setDeadline(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('更新成功！');
            emit('success');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style>
  .date_picker .el-picker-panel__footer {
    display: none;
  }
</style>

<style lang="scss" scoped>
  :deep(.el-input) {
    width: 100% !important;
  }
</style>
