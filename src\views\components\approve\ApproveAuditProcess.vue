<template>
  <div class="approve-process-wrap">
    <div class="process-list">
      <div v-for="(item,index) in list" :key="index" class="process-item">
        <div class="icon-wrap">
          <img src="@/assets/images/approve/flow_audit.png" alt="">
          <div v-show="index < list.length - 1" class="line"></div>
        </div>
        <div class="item-container">
          <div class="title-wrap">
            <div class="title">{{item.activityName}}</div>
            <div class="date">{{item.endTime}}</div>
          </div>
          <div class="op-wrap mt10 f-14 c-33">
            <span>{{item.assigneeName}}</span>
            <span v-if="item.commentType" class="ml10" :class="getClass(item.commentType)">({{item.commentTypeName}})</span>
          </div>
          <div v-if="item.commentMessage" class="desc mt10">{{item.commentMessage}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  list:{
    type: Array,
    default: []
  }
})
const {list} = toRefs(props)

const getClass = (type) => {
  switch (type) {
    case '0':
      return 'c-running'
      break;
    case '1':
      return 'c-completed'
      break;
    case '3':
      return 'c-terminated'
      break;
    case '7':
      return 'c-canceled'
      break;
    default:
      return 'c-running'
      break;
  }
}
</script>

<style lang="scss" scoped>
.process-list {
  .process-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .icon-wrap {
      width: 33px;
      flex-shrink: 0;
      position: relative;
      .line{
        position: absolute;
        left: 16px;
        height: 100%;
        width: 2px;
        background: #E4E7EE;
      }
      img {
        width: 33px;
      }
    }
    .item-container {
      margin-left: 20px;
      flex: 1;
      overflow: hidden;
      
      .title-wrap {
        display: flex;
        justify-content: space-between;

        .title{
          flex: 0 0 1;
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .date{
          flex-shrink: 0;
          width: 150px;
          font-size: 14px;
          color: #999999;
          text-align: right;
        }
      }
      .desc{
        background: #F3F6FD;
        border-radius: 3px;
        font-size: 14px;
        color: #333333;
        padding: 10px 20px;
        line-height: 22px;
      }
    }
  }
}
</style>