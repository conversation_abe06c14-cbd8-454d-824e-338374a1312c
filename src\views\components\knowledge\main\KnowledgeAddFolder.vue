<template>
<div class="pop-container">
  <el-dialog :modal-append-to-body="false" v-model="open" width="25%" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
    <el-form :model="form" ref="formRef" @submit.prevent="save" :rules="formRules" :label-position="'top'">
      <el-form-item label="文件夹名称:" prop="fileName">
        <el-input ref="firstInputRef" maxlength="50" v-model="form.fileName" placeholder="请输入文件夹名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button @click.prevent="save" type="primary" :loading="loading">
          <span v-if="!loading">保存</span>
          <span v-else>保存中...</span>
        </el-button>
      </div>
    </template>
  </el-dialog>
</div>

</template>

<script setup>
import { setFolder } from "@/api/knowledge/library";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean
});
const { open } = toRefs(props)

const commonInfo = inject('commonInfo')

const data = reactive({
  title: '新建文件夹',
  loading: false,
  form: {
    pid: commonInfo.fileId, //	上级目录或文件夹id
    fileName: '',
    libId: commonInfo.libraryId
  },
  formRules: {
    fileName: [
      { required: true, trigger: "blur", message: "文件夹名称不能为空" }
    ]
  },
})
const { title,loading,form,formRules } = toRefs(data)

nextTick(() => {
  setTimeout(() => {
    proxy.$refs.firstInputRef.focus()
  }, 100);
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true;
      if(data.form.libId){
        setFolder(data.form).then(response => {
          data.loading = false;
          emit('update:open',false);
          emit('success');
          proxy.$modal.msgSuccess("新建文件夹成功");
        }).catch(error => {
          data.loading = false;
          proxy.$modal.msgError("新建文件夹失败");
        });
      }
    }
  });
}

</script>

<style lang="scss" scoped>

</style>