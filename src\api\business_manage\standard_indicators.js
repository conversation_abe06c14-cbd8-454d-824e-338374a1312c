import request from '@/utils/request';

export function getIndexLibList(params) {
  return request({
    url: '/indexLib/list',
    method: 'get',
    params,
  });
}

export function deleteIndexLib(params) {
  return request({
    url: '/indexLib/delete',
    method: 'delete',
    params,
  });
}

export function getAnalysisReviewList(params) {
  return request({
    url: '/sdc/stdStandard/analysisReviewList',
    method: 'get',
    params,
  });
}

export function getQueryExtractByCode(params) {
  return request({
    url: '/indexLib/queryExtractByCode',
    method: 'get',
    params,
    timeout: 300000,
  });
}

export function getIndexLibDetail(params) {
  return request({
    url: '/indexLib/queryById',
    method: 'get',
    params,
  });
}

export function addIndexLib(data) {
  return request({
    url: '/indexLib/add',
    method: 'post',
    data,
  });
}

export function updateIndexLib(data) {
  return request({
    url: '/indexLib/update',
    method: 'post',
    data,
  });
}

export function checkIndexLib(data) {
  return request({
    url: '/indexLib/check',
    method: 'post',
    data,
  });
}
