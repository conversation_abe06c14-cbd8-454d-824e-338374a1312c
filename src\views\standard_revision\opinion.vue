<template>
  <div class="app-container">
    <div class="app-container-tab">
      <el-tabs v-model="activeName" @tab-change="handleTabChange">
        <el-tab-pane label="我的制修订征集" name="myRevision"></el-tab-pane>
        <el-tab-pane label="全员制修订征集" name="allRevision"></el-tab-pane>
        <el-tab-pane label="全员复审征集" name="allReview"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="app-container-search mt15">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <template v-if="activeName == 'myRevision' || activeName == 'allRevision'">
          <el-form-item label="项目编号:" prop="projectCode">
            <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.projectCode" placeholder="请输入项目编号" />
          </el-form-item>
          <el-form-item label="项目名称:" prop="projectName">
            <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="制修订:" prop="amend">
            <el-select v-model="queryParams.amend" placeholder="请选择制修订类型">
              <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="征集状态:" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择征集状态">
              <el-option v-for="item in bxc_solicit_status" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="截止日期:">
            <el-date-picker
              v-model="deadlineTime"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="发布日期:">
            <el-date-picker
              v-model="publishTime"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="关键词搜索:" prop="keyword">
            <el-input
              @keyup.enter="getData('pageNum')"
              v-model="queryParams.keyword"
              placeholder="请输入标准编号/名称、项目编号/名称"
            />
          </el-form-item>
          <el-form-item label="征集状态:" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择征集状态">
              <el-option v-for="item in bxc_solicit_status" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="征集日期:">
            <el-date-picker
              v-model="publishTime"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </template>
        <el-form-item label="" class="one-column">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <el-table v-loading="loading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" width="60" fixed>
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <template v-if="activeName == 'myRevision' || activeName == 'allRevision'">
          <el-table-column prop="projectCode" label="项目编号" show-overflow-tooltip min-width="200" fixed />
          <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip min-width="200" />
          <el-table-column prop="amendName" label="制修订" show-overflow-tooltip min-width="150" />
          <el-table-column v-if="activeName == 'myRevision'" label="反馈状态" show-overflow-tooltip min-width="120">
            <template #default="{ row }">
              <span :class="row.feedbackStatus == 0 ? 'status-blue' : 'status-green'">{{ row.feedbackStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="deadline" label="截止日期" show-overflow-tooltip min-width="180" />
          <el-table-column prop="createTime" label="发布日期" show-overflow-tooltip min-width="180" />
          <el-table-column label="征集状态" show-overflow-tooltip min-width="120">
            <template #default="{ row }">
              <span :class="row.solicitStatus == 0 ? 'status-blue' : 'status-red'">{{ row.solicitStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="{ row }">
              <el-button @click="handleClick('opinionDetail', row)" link type="primary">查看</el-button>
              <el-button
                v-if="row.solicitStatus == 0"
                v-hasPermi="['standard_revision:opinion:review']"
                @click="handleClick('opinionFeedback', row)"
                link
                type="primary"
              >
                反馈
              </el-button>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column prop="standardCode" label="标准编号" show-overflow-tooltip min-width="200" />
          <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
          <el-table-column prop="projectCode" label="所属复审项目编号" show-overflow-tooltip min-width="200" />
          <el-table-column prop="projectName" label="所属复审项目名称" show-overflow-tooltip min-width="200" />
          <el-table-column prop="createTime" label="征集开始时间" show-overflow-tooltip min-width="200" />
          <el-table-column prop="deadline" label="征集结束时间" show-overflow-tooltip min-width="200" />
          <el-table-column label="征集状态" show-overflow-tooltip min-width="120">
            <template #default="{ row }">
              <span :class="row.solicitStatus == 0 ? 'status-blue' : 'status-red'">{{ row.solicitStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="我的反馈" show-overflow-tooltip min-width="100">
            <template #default="{ row }">
              <span @click="router.push('/personal')" class="c-primary pointer">{{ row.feedbackCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120" fixed="right">
            <template #default="{ row }">
              <el-button @click="handleClick('standardDetail', row)" link type="primary">查看</el-button>
              <el-button
                v-if="row.solicitStatus == 0"
                v-hasPermi="['standard_revision:opinion:review']"
                @click="handleClick('opinionFeedback', row)"
                link
                type="primary"
              >
                反馈
              </el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <add-opinion-dialog
      v-if="opinionVisible"
      v-model:visible="opinionVisible"
      :defaultForm="defaultForm"
      @updateData="getData"
    />
    <detail-opinion-drawer v-if="detailVisible" v-model:visible="detailVisible" :id="selectId" @updateData="getData" />
    <detail-drawer v-if="drawerVisible" v-model:visible="drawerVisible" :standardId="standardId" :standardType="standardType" />
  </div>
</template>

<script setup name="Opinion/index">
  import { getOpinionList } from '@/api/standard_revision/opinion';
  import AddOpinionDialog from '@/views/components/standard_revision/AddOpinionDialog.vue';
  import DetailOpinionDrawer from '@/views/components/standard_revision/DetailOpinionDrawer.vue';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { bxc_standard_amend, bxc_solicit_status } = proxy.useDict('bxc_standard_amend', 'bxc_solicit_status');

  const loading = ref(false);
  const deadlineTime = ref([]);
  const publishTime = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const activeName = ref('myRevision');
  const opinionVisible = ref(false);
  const defaultForm = ref({});
  const detailVisible = ref(false);
  const selectId = ref();
  const standardId = ref('');
  const standardType = ref('');
  const drawerVisible = ref(false);

  const getData = val => {
    loading.value = true;
    if (val) queryParams.value[val] = 1;
    switch (activeName.value) {
      case 'myRevision':
        queryParams.value.module = 0;
        queryParams.value.type = 1;
        break;
      case 'allRevision':
        queryParams.value.module = 0;
        queryParams.value.type = 0;
        break;
      case 'allReview':
        queryParams.value.module = 1;
        queryParams.value.type = '';
        break;
      default:
        break;
    }
    queryParams.value.deadlineStartTime = deadlineTime.value.length > 0 ? deadlineTime.value[0] : '';
    queryParams.value.deadlineEndTime = deadlineTime.value.length > 0 ? deadlineTime.value[1] : '';
    queryParams.value.publishStartTime = publishTime.value.length > 0 ? publishTime.value[0] : '';
    queryParams.value.publishEndTime = publishTime.value.length > 0 ? publishTime.value[1] : '';
    getOpinionList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  getData();

  const handleTabChange = e => {
    getData();
  };

  const handleClick = (type, row) => {
    switch (type) {
      case 'opinionFeedback':
        defaultForm.value = {
          amendId: row.amendId,
          joinId: row.joinId,
          opinionId: row.id,
          standardId: row.standardId,
          projectCode: row.projectCode,
          projectName: row.projectName,
          projectIntroduce: row.projectIntroduce,
          standardCode: row.standardCode,
          standardName: row.standardName,
        };
        switch (activeName.value) {
          case 'myRevision':
            defaultForm.value.joinType = 0;
            break;
          case 'allRevision':
            defaultForm.value.joinType = 0;
            break;
          case 'allReview':
            defaultForm.value.joinType = 1;
            break;
          default:
            break;
        }
        opinionVisible.value = true;
        break;
      case 'opinionDetail':
        selectId.value = row.id;
        detailVisible.value = true;
        break;
      case 'standardDetail':
        standardId.value = row.standardId;
        standardType.value = row.standardType;
        drawerVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleClear = () => {
    deadlineTime.value = [];
    publishTime.value = [];
    proxy.$refs.queryRef.resetFields();
    getData('pageNum');
  };
</script>

<style lang="scss" scoped>
  .app-container-tab {
    background-color: #fff;
    padding: 5px 20px 0;
    box-sizing: border-box;
  }

  :deep(.el-tabs__header) {
    margin: 0 !important;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background-color: #fff !important;
  }
</style>
