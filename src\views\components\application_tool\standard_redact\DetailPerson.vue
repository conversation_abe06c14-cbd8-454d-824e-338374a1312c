<template>
  <div class="h-title mt20">所有者</div>
  <div class="personnel">
    <div v-for="item in owner" :key="item.id" class="personnel-item">
      <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="personnel-item-avatar" />
      <div class="f-14 flex flex-column flex-sb">
        <span class="c-33 f-bold">{{ item.teamMemberName }}</span>
        <span class="c-99 mt5">{{ item.deptName }}</span>
      </div>
    </div>
  </div>
  <div class="h-title mt40">协作者</div>
  <div v-if="collaborator && collaborator.length > 0" class="personnel">
    <div v-for="item in collaborator" :key="item.id" class="personnel-item">
      <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="personnel-item-avatar" />
      <div class="f-14 flex flex-column flex-sb">
        <span class="c-33 f-bold">{{ item.teamMemberName }}</span>
        <span class="c-99 mt5">{{ item.deptName }}</span>
      </div>
    </div>
  </div>
  <empty v-else />
  <div class="h-title mt40">批阅者</div>
  <div v-if="reviewer && reviewer.length > 0" class="personnel">
    <div v-for="item in reviewer" :key="item.id" class="personnel-item">
      <img :src="item.avatar ? item.avatar : avatarImg" alt="头像" class="personnel-item-avatar" />
      <div class="f-14 flex flex-column flex-sb">
        <span class="c-33 f-bold">{{ item.teamMemberName }}</span>
        <span class="c-99 mt5">{{ item.deptName }}</span>
      </div>
    </div>
  </div>
  <empty v-else />
</template>

<script setup>
  import { getTeamInfo } from '@/api/application_tool/standard_redact';

  const avatarImg = new URL('@/assets/images/avatar.png', import.meta.url).href;

  const props = defineProps({
    id: {
      type: [Number, String],
    },
  });

  const owner = ref([]);
  const collaborator = ref([]);
  const reviewer = ref([]);

  onMounted(() => {
    if (props.id) getInfo();
  });

  const getInfo = () => {
    getTeamInfo(props.id).then(res => {
      let data = res.data;
      owner.value = data.owner;
      collaborator.value = data.collaborator;
      reviewer.value = data.reviewer;
    });
  };
</script>

<style lang="scss" scoped>
  .personnel {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;

    &-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      width: calc((100% - 100px) / 6);

      &:nth-child(6n) {
        margin-right: 0 !important;
      }

      &-avatar {
        display: block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 15px;
      }
    }
  }

  :deep(.empty-img) {
    margin: 20px auto !important;
    width: 300px !important;
    height: auto !important;
  }
</style>
