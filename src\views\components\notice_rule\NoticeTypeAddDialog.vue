<template>
  <div>
    <el-dialog
      width="550"
      :title="id ? '编辑目录' : '新增目录'"
      append-to-body
      v-model="dialogVisible"
      :before-close="handleClose"
    >
      <el-form
        inline
        @submit.native.prevent
        label-width="auto"
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5 dia-form"
      >
        <el-form-item label="目录名称" prop="name">
          <el-input
            maxlength="50"
            placeholder="请输入目录名称"
            v-model="form.name"
          />
        </el-form-item>
        
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addNoticeCatalog,getNoticeCatalogDetail,editNoticeCatalog} from '@/api/notice_rule/manage.js'

const { proxy } = getCurrentInstance();
const props = defineProps({
  id: null,
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { id, dialogVisible } = toRefs(props);

const data = reactive({
  loading: false,
  form: {},
  rules: {
    name: [
      {
        required: true,
        message: "请输入目录名称",
        trigger: "blur",
      },
    ],
  },
});
const {loading, form, rules } = toRefs(data);

onMounted(() => {
  if (id.value) getDetail();
});

//获取详情
const getDetail = () => {
  getNoticeCatalogDetail(id.value).then((res) => {
    data.form = res.data;
  });
};

//点击提交
const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if (id.value) {
        editNoticeCatalog(data.form)
          .then(() => {
            proxy.$modal.msgSuccess('编辑成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        addNoticeCatalog(data.form)
          .then(() => {
            proxy.$modal.msgSuccess('新建成功')
            emit("getList");
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
};

//关闭弹窗
const handleClose = () => {
  emit("update:dialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

//监听事件
const emit = defineEmits(["update:dialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
.dia-form{
  width: 100%;
  .el-form-item{
    width: 100%;
  }
  .one-column {
    flex: 0 0 calc(95% + 30px) !important;
  }
}
.upload-notice{
  margin-top: 5px;
  img{
    height: 18px;
    margin-right: 8px;
  }
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper){
  width: 100% !important;
}

:deep(.el-form-item__label){
  font-size: 14px !important;
  color: #333;
}


</style>
