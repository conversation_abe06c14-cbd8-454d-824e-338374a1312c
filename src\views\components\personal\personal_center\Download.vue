<template>
  <div class="">

    <div class="download-type flex">
      <div class="tab flex flex-center mr20 pointer" @click="toggle('standard')" :class="active == 'standard'? 'active' : ''">标准下载</div>
      <div class="tab flex flex-center mr20 pointer" @click="toggle('knowledge')" :class="active == 'knowledge'? 'active' : ''">知识下载</div>
    </div>

    <el-table
      v-if="active == 'standard'"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="标准号"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="toDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="recordName"
        label="标准名称"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updateTime"
        label="最新下载时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button
            @click.stop="handleDownload(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
            v-hasPermi="['personal:download']"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-table
      v-if="active == 'knowledge'"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="资料名称"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handlePreview(row)" class="c-primary pointer">{{ row.recordName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="libName"
        label="知识库类别"
        min-width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updateTime"
        label="最新下载时间"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button
            @click.stop="handleDownloadKnowledge(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
            v-hasPermi="['personal:knowledgeDownload']"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
    <!-- 标准查看 -->
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="standardId"
      :standardType="standardType"
    />
  </div>
</template>

<script setup>
import {userDownloadInfoList} from '@/api/personal/personal.js'
import PreviewFile from '@/components/PreviewFile';
import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer'

const { proxy } = getCurrentInstance();
const router = useRouter();
const data = reactive({
  loading: false,
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recordType:'0'
  },
  active:'standard',
  downloadParams:{
    recordId:null,
    recordType:null
  },
  openFile:false,
  currentUrl:null
});
const {
  loading,
  total,
  dataList,
  queryParams,
  form,
  active,
  openFile,
  currentUrl
} = toRefs(data);

const standardId = ref('');
const standardType = ref(undefined);
const drawerVisible = ref(false);

//获取列表
const getList = () => {
  data.loading = true;
  userDownloadInfoList(data.queryParams)
  .then(res => {
    data.dataList = res.rows;
    data.total = res.total;
    data.loading = false;
  })
  .catch(() => {
    data.loading = false;
  });
}

//标准下载
const handleDownload = (row) => {
  if(row.recordDelFlag == '0'){
    let fileName = row.recordName;
    if(row.fileList && row.fileList.length > 1 ){
      fileName += '.zip';
    }else{
      fileName = row.fileList[0].name;
    }
    data.downloadParams.recordId = row.recordId;
    data.downloadParams.recordType = '0';
    proxy.download('/process/userDownloadInfo/download', data.downloadParams, fileName)
  }else{
    proxy.$modal.msgError('该标准已经删除，无法进行此操作')
  }
}

//知识下载
const handleDownloadKnowledge = (row) => {
  if(row.recordDelFlag == '0'){
    let downloadParams = {
      recordId:row.recordId,
      recordType:'1'
    }
    proxy.download('/process/userDownloadInfo/download', downloadParams, row.recordName)
  }else{
    proxy.$modal.msgError('该知识已经删除，无法进行此操作')
  }
  
}

const toDetail = row => {
  if(row.recordDelFlag == '0'){
    standardId.value = row.recordId;
    standardType.value = row.standardType;
    drawerVisible.value = true;
  }else{
    proxy.$modal.msgError('该标准已经删除，无法进行此操作')
  }
  
};
const toggle = (type) => {
  data.active = type;
  switch(type) {
    case 'standard':
      data.queryParams.recordType = '0'
      break;
    case 'knowledge':
      data.queryParams.recordType = '1'
      break;  
  }
  getList()
}

const getFileExtension = urlStr => {
  let fileExtension = '';
  if (urlStr.lastIndexOf('.') > -1) {
    fileExtension = urlStr.slice(urlStr.lastIndexOf('.') + 1);
    fileExtension = fileExtension.toLowerCase();
  }
  return fileExtension;
};
// 点击查看
const handlePreview = (row) => {
  if (['.zip', '.rar'].includes(getFileExtension(row.file))) {
    proxy.$modal.msgError('zip,rar等压缩文件不能直接查看，请下载后查看！');
  } else {
    data.currentUrl = row.file;
    data.openFile = true;
  }
};
getList()
</script>

<style lang="scss" scoped>
.tab{
  width: 100px;
  height: 40px;
  border-radius: 5px;
  border: 1px solid #2F5AFF;
  color: #2F5AFF;
}
.active{
  background-color: #2F5AFF;
  color: #fff;
}

</style>