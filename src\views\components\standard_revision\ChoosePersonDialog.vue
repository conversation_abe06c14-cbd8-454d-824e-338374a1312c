<template>
  <el-dialog
    v-model="props.visible"
    title="选择"
    width="650px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content">
      <div class="content-left">
        <div class="content-top flex-center">
          <el-input v-model="inputValue" placeholder="请输入关键字" suffix-icon="Search" @input="handleSearch" />
        </div>
        <div class="content-left-tree">
          <el-scrollbar height="390px">
            <el-tree
              ref="treeRef"
              :data="treeList"
              show-checkbox
              node-key="userId"
              highlight-current
              :props="{ children: 'children', label: 'name' }"
              :filter-node-method="filterNode"
              @check-change="handleChange"
            />
          </el-scrollbar>
        </div>
      </div>
      <div class="content-right">
        <div class="content-top flex flex-sb flex-ai-center">
          <div>已选：{{ chooseList.length || 0 }}个员工</div>
          <el-button type="primary" plain color="#2F5AFF" size="small" @click="handleClear">清空</el-button>
        </div>
        <div>
          <div class="content-left-tree">
            <el-scrollbar height="390px">
              <b-tags @handleUpdate="handleUpdate" v-if="chooseList.length > 0" :addBtn="false" v-model:tags="chooseList" />
              <empty v-else />
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" color="#2F5AFF" @click="handleConfirm">保存</el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import BTags from '@/components/BTags';
  import { memberTree } from '@/api/common';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: { type: Boolean },
    tags: {
      type: Array,
      default: () => {
        return [];
      },
    },
    filterUser: { type: Boolean, default: false },
  });

  const inputValue = ref('');
  const treeList = ref([]);
  const chooseList = ref([]);
  const externalPerson = ref([]);

  const getData = () => {
    let params = {
      filterUserId: props.filterUser ? props.tags : [],
    };
    memberTree(params).then(res => {
      treeList.value = handleData(res.data);
      nextTick(() => {
        if (!props.filterUser && props.tags && props.tags.length > 0) {
          let personList = [];
          props.tags.forEach(item => {
            if (item.userId == null || item.userId == undefined || item.userId == '') {
              externalPerson.value.push(item);
            } else {
              personList.push(item);
            }
          });
          chooseList.value = personList;
          handleUpdate(chooseList.value);
        }
      });
    });
  };

  getData();

  const handleData = data => {
    data.forEach(item => {
      if (item.type == 0) {
        item.disabled = false;
        return;
      }
      if (item.type == 1 && item.children && item.children.length > 0) {
        item.disabled = false;
        handleData(item.children);
      } else {
        item.disabled = true;
      }
    });
    return data;
  };

  const handleSearch = () => {
    proxy.$refs.treeRef.filter(inputValue.value);
  };

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.name.includes(value);
  };

  const handleChange = () => {
    nextTick(() => {
      chooseList.value = proxy.$refs.treeRef.getCheckedNodes(true);
    });
  };

  const handleUpdate = data => {
    proxy.$refs.treeRef.setCheckedNodes(data);
  };

  const handleClear = () => {
    chooseList.value = [];
    handleUpdate([]);
  };

  const handleConfirm = () => {
    if (chooseList.value.length === 0 && externalPerson.value.length === 0) {
      proxy.$message.warning('请选择人员');
      return;
    }
    let arr = chooseList.value.concat(externalPerson.value);
    emit('handleChoose', arr);
    handleClose();
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['handleChoose', 'update:visible']);
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 20px 20px 0 !important;
  }

  .content {
    display: flex;
    border: 1px solid #d2d9e9;
    &-top {
      height: 55px;
      padding: 10px 15px;
      box-sizing: border-box;
      border-bottom: 1px solid #d2d9e9;
    }
    &-left {
      width: 275px;
      border-right: 1px solid #e5e8ef;
      &-tree {
        min-height: 320px;
        padding: 15px 20px;
        box-sizing: border-box;
      }
    }
    &-right {
      flex: 1;
    }
  }

  :deep(.el-input__wrapper) {
    background-color: #f3f6fd !important;
  }
  .el-input :deep(.el-input__icon) {
    font-size: 18px;
  }
</style>
