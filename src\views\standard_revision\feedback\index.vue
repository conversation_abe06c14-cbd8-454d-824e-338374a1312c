<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="反馈标准:" prop="standardCode">
          <el-input
            @keyup.enter="handleQuery"
            v-model="queryParams.standardCode"
            placeholder="请输入反馈标准"
          />
        </el-form-item>
        <el-form-item label="反馈用户:" prop="feedbackUser">
          <el-input
            @keyup.enter="handleQuery"
            v-model="queryParams.feedbackUser"
            placeholder="请输入反馈用户"
          />
        </el-form-item>
        <el-form-item label="处理状态:" prop="processStatus">
          <el-select
            v-model="queryParams.processStatus"
            placeholder="请选择处理状态"
            clearable
          >
            <el-option
              v-for="dict in bxc_standard_feedback_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈时间:">
          <el-date-picker
            v-model="dateRangeFeedback"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDateFeedback"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="处理人:" prop="processUser">
          <el-input
            @keyup.enter="handleQuery"
            v-model="queryParams.processUser"
            placeholder="请输入处理人"
          />
        </el-form-item>
        <el-form-item label="处理时间:">
          <el-date-picker
            v-model="dateRangeProcess"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDateProcess"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column
          prop="standardCode"
          label="反馈标准"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="feedbackUser"
          label="反馈用户"
          show-overflow-tooltip
        />
        <el-table-column
          prop="feedbackTime"
          label="反馈时间"
          show-overflow-tooltip
        />
        <el-table-column prop="processStatusName" label="处理状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag :class="getStatusClass(scope.row.processStatus)" >{{scope.row.processStatusName}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="processUser"
          label="处理人"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processTime"
          label="处理时间"
          show-overflow-tooltip
        />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              v-if="scope.row.processStatus == 0"
              @click.stop="handle(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
              v-hasPermi="['standard_revision:feedback:handle']"
            >
              处理
            </el-button>
            <el-button
              @click.stop="handleDetail(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <feedback-detail-dialog
      v-if="detailDialog"
      :id="currentId"
      v-model:dialogVisible="detailDialog"
    />
    <feedback-handle-dialog
      v-if="handleDialog"
      v-model:dialogVisible="handleDialog"
      :id="currentId"
      @getList="getList"
    />
  </div>
</template>

<script setup>
import { standardFeedbackInfoListAll } from '@/api/standard_revision/feedback.js'

import FeedbackDetailDialog from '@/views/components/standard_revision/feedback/FeedbackDetailDialog'
import FeedbackHandleDialog from '@/views/components/standard_revision/feedback/FeedbackHandleDialog'

const { proxy } = getCurrentInstance();
const {bxc_standard_feedback_status} = proxy.useDict('bxc_standard_feedback_status')
const router = useRouter();
const data = reactive({
  detailDialog:false,
  handleDialog:false,
  loading: false,
  dateRangeFeedback: [],
  dateRangeProcess:[],
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    standardCode:null,
    feedbackUser:null,
    processStatus:null,
    processUser:null
  },
  currentId:null
});
const {
  loading,
  dateRangeFeedback,
  dateRangeProcess,
  total,
  dataList,
  queryParams,
  handleDialog,
  detailDialog,
  currentId
} = toRefs(data);

const resetQuery = () => {
  data.dateRangeFeedback = [];
  data.dateRangeProcess = [];
  proxy.resetForm("queryFormRef");
  selectDateFeedback();
  selectDateProcess();
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.queryParams.pageNum = 1;
  getList();
}

//获取列表
const getList = () => {
  data.loading = true;
  standardFeedbackInfoListAll(data.queryParams)
  .then(res => {
    data.dataList = res.rows;
    data.total = res.total;
    data.loading = false;
  })
  .catch(() => {
    data.loading = false;
  });
}

const selectDateFeedback = () => {
  if (data.dateRangeFeedback && data.dateRangeFeedback.length > 0) {
    data.queryParams.feedbackStartTime = data.dateRangeFeedback[0];
    data.queryParams.feedbackEndTime = data.dateRangeFeedback[1];
  } else {
    data.queryParams.feedbackStartTime = undefined;
    data.queryParams.feedbackEndTime = undefined;
  }
}

const selectDateProcess = () => {
  if (data.dateRangeProcess && data.dateRangeProcess.length > 0) {
    data.queryParams.processStartTime = data.dateRangeProcess[0];
    data.queryParams.processEndTime = data.dateRangeProcess[1];
  } else {
    data.queryParams.processStartTime = undefined;
    data.queryParams.processEndTime = undefined;
  }
}
//查看
const handleDetail = (row) => {
  data.currentId = row.id;
  data.detailDialog = true;
};

//处理
const handle = (row) => {
  data.currentId = row.id;
  data.handleDialog = true;
};

const getStatusClass = (status) => {
  if(status == 0){
    return 's-red'
  }else if(status == 1){
    return 's-blue'
  }else{
    return 's-green'
  }
}

getList()
</script>

<style lang="scss" scoped>

</style>