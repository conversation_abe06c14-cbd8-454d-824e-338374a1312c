<template>
  <div v-if="treeList && treeList.length > 0" class="tree">
    <div class="tree-title">标准文本</div>
    <div class="tree-content">
      <el-tree
        ref="treeRef"
        node-key="id"
        :data="treeList"
        :highlight-current="true"
        :expand-on-click-node="false"
        :default-expanded-keys="highlightedNodes"
        :props="{ children: 'children', label: 'name' }"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span class="custom-label">
              <span v-if="node.level == 1" class="iconfont icon-lgcengji f-18 c-primary mr5"></span>
              <span v-showToolTip="['tree']">
                <el-tooltip placement="bottom-start" :content="node.label">
                  <span :class="isHighlighted(data) ? 'highlighted' : ''">{{ node.label }}</span>
                </el-tooltip>
              </span>
            </span>
          </span>
        </template>
      </el-tree>
    </div>
    <tree-preview v-if="open" v-model:open="open" :id="currentId" :standardItem="form" :content="props.content" />
  </div>
</template>

<script setup>
  import { getAnalysisIndexDetail, getHighLightAnalysisIndex } from '@/api/standard_manage/standard_query';
  import TreePreview from '@/views/components/standard_manage/standard_query/TreePreview';

  const route = useRoute();

  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
    content: {
      type: String,
    },
  });

  const data = reactive({
    open: false,
    currentId: undefined,
    highlightedNodes: [],
  });
  const { open, currentId, highlightedNodes } = toRefs(data);

  const treeList = ref([]);

  getAnalysisIndexDetail({ standardId: props.form.standardId }).then(res => {
    treeList.value = res.data.tree || [];
  });

  if (props.content) {
    getHighLightAnalysisIndex({
      standardId: props.form.standardId,
      content: props.content,
    }).then(res => {
      highlightedNodes.value = res.data || [];
    });
  }

  const isHighlighted = data => {
    return highlightedNodes.value.some(n => n === data.id);
  };

  const handleNodeClick = node => {
    if (!node.children || node.children.length == 0) {
      currentId.value = node.id;
      open.value = true;
    }
  };
</script>

<style lang="scss" scoped>
  .tree {
    width: 100%;
    background: #ffffff;
    border-radius: 5px;
    border: 1px solid #e8e8e8;
    padding: 25px 15px;
    overflow: hidden;

    &-title {
      font-size: 18px;
      color: #333;
      font-weight: 600;
    }

    &-content {
      height: 100%;
      overflow-y: scroll;
      margin-top: 10px;

      &::-webkit-scrollbar {
        width: 0;
      }
    }
  }

  :deep(.el-tree) {
    max-height: 500px;
  }

  :deep(.el-tree-node__content) {
    height: 40px !important;

    &:hover {
      background: #e9f0fe;
      border-radius: 5px;
      color: $primary-color;
    }
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 600;
      padding-right: 8px;
      height: 40px;
      overflow: hidden;

      &:hover {
        font-weight: bold !important;
      }

      .custom-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden; //文本超出隐藏
        text-overflow: ellipsis;
      }
    }
  }
  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    color: $primary-color;
  }

  .highlighted {
    color: #ff0000;
  }

  :deep(.el-tree-node__children .el-tree-node__content .custom-tree-node) {
    font-size: 14px;
    font-weight: 400;
  }
</style>
