import request from '@/utils/request'

//标准应用

// 查询标准应用列表
export const getStandardApplicationList = (params) => {
  return request({
    url: '/process/standardApply/list',
    method: 'get',
    params
  })
}

// 获取标准应用详细信息
export const getStandardApplicationDetail = (id) => {
  return request({
    url: '/process/standardApply/' + id,
    method: 'get'
  })
}
// 新增标准应用
export const addStandardApplication = (data) => {
  return request({
    url: '/process/standardApply',
    method: 'post',
    data
  })
}
// 修改标准应用
export const updateStandardApplication = (data) => {
  return request({
    url: '/process/standardApply',
    method: 'put',
    data
  })
}
// 删除标准应用
export const deleteStandardApplication = (data) => {
  return request({
    url: '/process/standardApply/remove',
    method: 'delete',
    data
  })
}
// 查询标准应用目录树
export const getStandardApplicationTree = (applicationId) => {
  return request({
    url: '/process/standardApplyMenu/tree/' + applicationId,
    method: 'get'
  })
}
// 新增标准应用目录
export const addStandardApplicationTree = (data) => {
  return request({
    url: '/process/standardApplyMenu',
    method: 'post',
    data
  })
}
// 重命名标准应用目录
export const updateStandardApplicationTree = (data) => {
  return request({
    url: '/process/standardApplyMenu',
    method: 'put',
    data
  })
}
// 删除标准应用目录
export const deleteStandardApplicationTree = (applyId,data) => {
  return request({
    url: '/process/standardApplyMenu/' + applyId,
    method: 'delete',
    data
  })
}
// 查询标准应用分页数据
export const getStandardApplicationItemList = (params) => {
  return request({
    url: '/process/standardApplyMenu/getStandardWithApplyMenuPage',
    method: 'get',
    params
  })
}
// 获取标准应用详细信息
export const getStandardApplicationItemDetail = (id) => {
  return request({
    url: '/process/standardApplyMenu/' + id,
    method: 'get'
  })
}
// 移动标准应用
export const moveStandardApplicationItem = (data) => {
  return request({
    url: '/process/standardApplyMenu/moveStandardApplyMenu',
    method: 'put',
    data
  })
}
// 移出 - 根据标准ids从标准应用移出
export const removeStandardApplicationItem = (data) => {
  return request({
    url: '/process/standardApplyMenu/removeOut',
    method: 'delete',
    data
  })
}
// 导入标准
export const importStandardApplicationItem = (data) => {
  return request({
    url: '/process/standardApplyMenu/importStandard',
    method: 'post',
    data
  })
}