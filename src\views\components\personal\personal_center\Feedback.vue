<template>
  <div class="download-type flex">
    <div class="tab flex flex-center mr20 pointer" @click="toggle('standard')" :class="active == 'standard' ? 'active' : ''">
      标准反馈
    </div>
    <div class="tab flex flex-center mr20 pointer" @click="toggle('revision')" :class="active == 'revision' ? 'active' : ''">
      制修订反馈
    </div>
    <div class="tab flex flex-center mr20 pointer" @click="toggle('recheck')" :class="active == 'recheck' ? 'active' : ''">
      复审反馈
    </div>
  </div>
  <div class="mt15">
    <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15" :border="true">
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <template v-if="active == 'standard'">
        <el-table-column prop="standardCode" label="反馈标准" min-width="200" show-overflow-tooltip fixed />
        <el-table-column prop="feedbackTime" label="反馈时间" show-overflow-tooltip min-width="180" />
        <el-table-column prop="processStatusName" label="处理状态" show-overflow-tooltip min-width="150" />
        <el-table-column prop="processUser" label="处理人" show-overflow-tooltip min-width="120" />
        <el-table-column prop="processTime" label="处理时间" show-overflow-tooltip min-width="180" />
      </template>
      <template v-else-if="active == 'revision'">
        <el-table-column label="项目编号" show-overflow-tooltip min-width="200" fixed>
          <template #default="{ row }">
            <span @click="handleDetail(row)" class="c-primary pointer">{{ row.projectCode }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="createTime" label="发布时间" show-overflow-tooltip min-width="180" />
        <el-table-column prop="opinionTypeName" label="意见类别" show-overflow-tooltip min-width="150" />
        <el-table-column prop="feedbackTime" label="反馈时间" show-overflow-tooltip min-width="180" />
        <el-table-column prop="processResultName" label="处理结果" show-overflow-tooltip min-width="200" />
      </template>
      <template v-else>
        <el-table-column prop="standardCode" label="标准编号" show-overflow-tooltip min-width="200" fixed />
        <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="opinionContent" label="意见" show-overflow-tooltip min-width="200" />
        <el-table-column label="附件" show-overflow-tooltip min-width="150">
          <template #default="{ row }">
            <el-button
              v-if="row.feedbackFileList && row.feedbackFileList.length > 0"
              @click="handleImg(row.feedbackFileList[0])"
              link
              type="primary"
            >
              {{ row.feedbackFileList[0].name }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="feedbackTime" label="反馈时间" show-overflow-tooltip min-width="180" />
        <el-table-column prop="opinionTypeName" label="意见类型" show-overflow-tooltip min-width="150" />
        <el-table-column prop="processResultName" label="处理结果" show-overflow-tooltip min-width="150" />
        <el-table-column prop="processContent" label="说明" show-overflow-tooltip min-width="200" />
        <el-table-column prop="processTime" label="处理时间" show-overflow-tooltip min-width="180" />
      </template>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
  </div>
  <feedback-detail-dialog v-if="detailDialog" :id="currentId" v-model:dialogVisible="detailDialog" />
  <preview-file v-if="fileVisible" v-model:open="fileVisible" :url="fileUrl" />
</template>

<script setup>
  import { standardFeedbackInfoList } from '@/api/personal/personal.js';
  import { getRevisionFeedbackList, recheckData } from '@/api/standard_revision/opinion';
  import PreviewFile from '@/components/PreviewFile';
  import FeedbackDetailDialog from '@/views/components/personal/personal_center/FeedbackDetailDialog';

  const data = reactive({
    loading: false,
    total: 0,
    dataList: [],
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    currentId: null,
    detailDialog: false,
  });

  const { loading, total, dataList, queryParams, currentId, detailDialog } = toRefs(data);

  const active = ref('standard');
  const fileVisible = ref(false);
  const fileUrl = ref('');

  const getList = () => {
    data.loading = true;
    standardFeedbackInfoList(data.queryParams)
      .then(res => {
        data.dataList = res.rows;
        data.total = res.total;
      })
      .finally(() => {
        data.loading = false;
      });
  };

  const getRevisionData = () => {
    data.loading = true;
    recheckData(data.queryParams)
      .then(res => {
        data.dataList = res.rows;
        data.total = res.total;
      })
      .finally(() => {
        data.loading = false;
      });
  };

  const getRecheckData = () => {
    data.loading = true;
    recheckData(data.queryParams)
      .then(res => {
        data.dataList = res.rows;
        data.total = res.total;
      })
      .finally(() => {
        data.loading = false;
      });
  };

  const toggle = type => {
    active.value = type;
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 10;
    getData();
  };

  const getData = () => {
    switch (active.value) {
      case 'standard':
        queryParams.value.joinType = '';
        getList();
        break;
      case 'revision':
        queryParams.value.joinType = 0;
        getRevisionData();
        break;
      case 'recheck':
        queryParams.value.joinType = 1;
        getRecheckData();
        break;
    }
  };

  const handleDetail = row => {
    data.currentId = row.id;
    data.detailDialog = true;
  };

  const handleImg = row => {
    fileUrl.value = row.url;
    fileVisible.value = true;
  };

  getList();
</script>

<style lang="scss" scoped>
  .tab {
    width: 100px;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #2f5aff;
    color: #2f5aff;
  }
  .active {
    background-color: #2f5aff;
    color: #fff;
  }
</style>
