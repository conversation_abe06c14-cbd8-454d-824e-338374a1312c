<template>
  <el-dialog
    width="1200px"
    title="标准入库"
    v-model="visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-jc-end flex-ai-center">
      <el-button @click="handleAdd" type="primary" icon="Plus">添加标准</el-button>
    </div>
    <el-table :data="tableData" ref="tableRef" :border="true" class="mt20">
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip min-width="200" />
      <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
      <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="150">
        <template #default="{ row }">
          <dict-tag :options="bxc_standard_type" :value="row.standardType" />
        </template>
      </el-table-column>
      <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="150">
        <template #default="{ row }">
          <dict-tag :options="bxc_standard_status" :value="row.standardStatus" />
        </template>
      </el-table-column>
      <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
      <el-table-column label="操作" min-width="150">
        <template #default="{ row, $index }">
          <el-button @click.stop="handleEdit(row, $index)" icon="Edit" size="small" link class="f-14 c-primary">编辑</el-button>
          <el-button @click.stop="handleDel($index)" icon="Delete" type="danger" size="small" link class="f-14">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :disabled="!addDisabled" type="primary" @click="handleConfirm">确认</el-button>
    </template>
    <!-- 内嵌Dialog -->
    <el-dialog
      width="830px"
      class="inner-body"
      title="请选择入库标准类型"
      append-to-body
      v-if="innerVisible"
      v-model="innerVisible"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleInnerClose"
    >
      <el-radio-group @change="handleInnerConfirm" v-model="standardType">
        <el-radio-button v-for="item in bxc_standard_type" :key="item.value" :label="item.value" class="_tag">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </el-dialog>
    <add-standard-dialog
      ref="addStandardRef"
      v-if="addStandardVisible"
      v-model:visible="addStandardVisible"
      v-model:standardType="standardType"
      :isEdit="isEdit"
      @updateData="updateData"
    />
  </el-dialog>
</template>

<script setup>
  import { addStandard } from '@/api/standard_manage/standard_query';
  import AddStandardDialog from '@/views/components/standard_manage/standard_query/AddStandardDialog.vue';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_type, bxc_standard_status } = proxy.useDict('bxc_standard_type', 'bxc_standard_status');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const { visible } = toRefs(props);

  const addStandardRef = ref(null);

  const data = reactive({
    isEdit: false,
    loading: false,
    tableData: [],
    innerVisible: false,
    standardType: undefined,
    addStandardVisible: false,
  });
  const { isEdit, loading, tableData, innerVisible, standardType, addStandardVisible } = toRefs(data);

  let addDisabled = computed(() => {
    return tableData.value.length > 0;
  });

  const handleAdd = () => {
    isEdit.value = false;
    innerVisible.value = true;
  };

  const handleEdit = (row, $index) => {
    isEdit.value = true;
    row.isEdit = true;
    row.index = $index;
    standardType.value = row.standardType;
    addStandardVisible.value = true;
    nextTick(() => {
      addStandardRef.value.setComponentsForm(row);
    });
  };

  const handleDel = index => {
    tableData.value.splice(index, 1);
  };

  const updateData = data => {
    if (data.isEdit) {
      tableData.value.splice(data.index, 1, data);
    } else {
      let include = tableData.value.some(item => {
        return item.standardCode == data.standardCode;
      });
      include
        ? proxy.$message({
            message: '标准【' + data.standardCode + '】已存在于入库列表中',
            type: 'warning',
          })
        : tableData.value.push(data);
    }
  };

  const handleInnerClose = () => {
    innerVisible.value = false;
    standardType.value = undefined;
  };

  const handleInnerConfirm = () => {
    addStandardVisible.value = true;
    innerVisible.value = false;
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const handleConfirm = () => {
    loading.value = true;
    addStandard(tableData.value)
      .then(res => {
        handleClose();
        emit('success', '标准入库成功！');
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss">
  .inner-body .el-dialog__body {
    padding: 40px 20px 50px 20px !important;
  }
</style>

<style lang="scss" scoped>
  :deep(.el-radio-button__inner) {
    color: $primary-color;
    font-size: 16px !important;
    height: 46px !important;
    line-height: 46px !important;
    text-align: center !important;
    padding: 0 18px !important;
    box-sizing: border-box !important;
    border: 1px solid $primary-color !important;
    border-radius: 5px !important;
  }

  .el-radio-group {
    width: 100% !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
