<template>
  <span>
    <el-dropdown @command="handleOpCommand" @visible-change="handleVisibleChange" trigger="click">
      <el-icon @click.stop class="ml5 mr5 pointer"><MoreFilled /></el-icon>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item :command="beforeHandleCommand('addFile',node)">添加章条</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('editFile',node)">编辑</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('deleteFile',node)">删除</el-dropdown-item> 
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </span>
</template>

<script setup>
const {proxy} = getCurrentInstance()

const props = defineProps({
  node: {
    type: Object,
    default: {}
  }
})
const { node } = toRefs(props)

const emit = defineEmits(['selected'])

const beforeHandleCommand = (opName,row) => {
 	return {
 		command:opName,
 		row: row
 	}
}
const handleOpCommand = (command) => {
  switch (command.command) {
    case "addFile":
      addFile(command.row)
      break;
    case "editFile":
      editFile(command.row)
      break;
    case "deleteFile":
      deleteFile(command.row)
      break;
    default:
      break;
  }
}
const handleVisibleChange = (isOpen) => {
  props.node.data.isOpen = isOpen
  if(!isOpen){
    props.node.data.isShow = false
  }
}
const addFile = (row) => {
  // opType 0:新增,1:编辑 2:删除
  emit('selected',{opType: 0,pid: row.data.id,id: '',name:'',outlineLevel:row.level-1}) 
}
const editFile = (row) => {
  emit('selected',{opType: 1,pid: row.data.pid,id: row.data.id,name:row.data.name,outlineLevel:row.level-1}) 
}
const deleteFile = (row) => {
  emit('selected',{opType: 2,pid: row.data.pid,id: row.data.id,name:row.data.name,outlineLevel:row.level-1})
}

</script>

<style lang="scss" scoped>
</style>