<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="模型名称:" prop="modelName">
          <el-input
            v-model="queryParams.modelName"
            placeholder="请输入模型名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="模型标识:" prop="modelKey">
          <el-input
            v-model="queryParams.modelKey"
            placeholder="请输入模型标识"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="container-bar">
        <div class="bar-right">
        </div>
      </div>
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column 
          type="index"
          align="center" 
          label="序号" 
          fixed="left"  
          min-width="55"
        >
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="modelKey"
          label="模型标识"
          min-width="200"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="modelName"
          label="模型名称"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="version"
          label="模型版本"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processVersion"
          label="流程版本"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="description"
          label="描述"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="lastDeployTime"
          label="最新部署时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="220">
          <template #default="scope">
            <el-button
              @click.stop="handleUpdate(scope.row)"
              v-hasPermi="['approve:process_setting:update']"
              link
              size="small"
              class="f-14 c-primary"
            >
              修改
            </el-button>
            <el-button
              @click.stop="handleDesign(scope.row)"
              v-hasPermi="['approve:process_setting:design']"
              link
              size="small"
              class="f-14 c-primary"
            >
              设计
            </el-button>
            <el-button
              @click.stop="handleDeploy(scope.row)"
              v-hasPermi="['approve:process_setting:deploy']"
              link
              size="small"
              class="f-14 c-primary"
            >
              部署
            </el-button>
            <el-button
              @click.stop="handleView(scope.row)"
              v-hasPermi="['approve:process_setting:view']"
              link
              size="small"
              class="f-14 c-primary"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 设计弹框 -->
    <approve-process-design v-if="open" v-model:open="open" :modelId="currentModelId" @success="getList" />
    <!-- 查看弹框 -->
    <approve-process-view v-if="openView" v-model:open="openView" :modelId="currentModelId" />
    <!-- 修改弹框 -->
    <pop-update-process v-if="openUpdate" v-model:open="openUpdate" :modelId="currentModelId" @success="getList" />
  </div>
</template>

<script setup>
import { getProcessSettingList,deployProcess } from '@/api/approve/process_setting'
import ApproveProcessDesign from '@/views/components/approve/ApproveProcessDesign'
import ApproveProcessView from '@/views/components/approve/ApproveProcessView'
import PopUpdateProcess from '@/views/components/approve/PopUpdateProcess'

const { proxy } = getCurrentInstance()

const data = reactive({
  loading: false,
  open: false,
  openView: false,
  openUpdate: false,
  currentModelId: undefined,
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    modelName: undefined,
    modelKey: undefined,
  }
});
const {
  loading,
  open,
  openView,
  openUpdate,
  currentModelId,
  total,
  dataList,
  queryParams,
} = toRefs(data);
const resetQuery = () => {
  proxy.resetForm("queryFormRef");
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.queryParams.pageNum = 1;
  getList();
}

const getList = () => {
  data.loading = true;
  getProcessSettingList(data.queryParams).then((response) => {
    if(response.rows){
      data.dataList = response.rows
      data.total = response.total
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const handleDesign = (row) => {
  data.currentModelId = row.modelId
  data.open = true
}
const handleView = (row) => {
  data.currentModelId = row.modelId
  data.openView = true
}
const handleUpdate = (row) => {
  data.currentModelId = row.modelId
  data.openUpdate = true
}
const handleDeploy = (row) => {
  let tip = '确认部署版本为【'+row.version+'】的【'+row.modelName+'】流程?'
  proxy.$modal.confirm(tip,'提示').then(function () {
    return deployProcess(row.modelId);
  }).then(() => {
    proxy.$modal.msgSuccess("流程部署成功");
    getList()
  }).catch(() => {
    
  });
}


getList()

</script>

<style lang="scss" scoped>

</style>