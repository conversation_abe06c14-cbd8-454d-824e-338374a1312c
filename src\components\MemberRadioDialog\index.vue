<template>
  <div class="app-select pointer" @click="handleDialog">
    <div
      class="flex-1 overflow-ellipsis"
      :class="!memberId ? '_placeholder' : ''"
    >
      {{ !memberId ? placeholder : memberName }}
    </div>
    <div class="flex flex-jc-end">
      <el-icon :size="13" color="#a8abb2"><ArrowDown /></el-icon>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    title="选择"
    :before-close="handleClose"
    width="650px"
    append-to-body
  >
    <div class="content">
      <div class="content-left">
        <div class="content-top flex-center">
          <el-input
            v-model="inputValue"
            placeholder="请输入关键字"
            suffix-icon="Search"
            @input="handleSearch"
          />
        </div>
        <div class="content-left-tree">
          <el-scrollbar height="390px">
            <el-tree
              ref="treeRef"
              node-key="id"
              show-checkbox
              check-strictly
              highlight-current
              default-expand-all
              :data="treeList"
              :props="defaultProps"
              :filter-node-method="filterNode"
              @check-change="handleChange"
            />
          </el-scrollbar>
        </div>
      </div>
      <div class="content-right">
        <div class="content-top flex flex-sb flex-ai-center">
          <div>已选：{{ chooseList.length || 0 }}个员工</div>
          <el-button
            type="primary"
            plain
            color="#2F5AFF"
            size="small"
            @click="handleClear"
            >清空</el-button
          >
        </div>
        <div>
          <div class="content-left-tree">
            <el-scrollbar height="390px">
              <b-tags
                @handleUpdate="handleUpdate"
                v-if="chooseList.length > 0"
                :addBtn="false"
                v-model:tags="chooseList"
              />
              <empty v-else />
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" color="#2F5AFF" @click="handleConfirm">
          保存
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import BTags from "@/components/BTags";
import { memberTree } from "@/api/common";
import { ElMessage } from "element-plus";

const { proxy } = getCurrentInstance();

const props = defineProps({
  memberId: {
    type: [Number, String],
    default: null,
  },
  placeholder: {
    type: String,
    default: "",
  },
});
const { memberId, placeholder } = toRefs(props);

const defaultProps = {
  children: "children",
  label: "name",
};

const data = reactive({
  memberName: "",
  dialogVisible: false,
  inputValue: "",
  treeList: [],
  chooseList: [],
});
const { memberName, dialogVisible, inputValue, treeList, chooseList } =
  toRefs(data);

watch(memberId, (newValue, oldValue) => {
  if (newValue) {
    handleMemberName(treeList.value);
  }
});

onMounted(() => {
  getMemberTree();
});

const getMemberTree = () => {
  memberTree().then((res) => {
    let data = res.data;
    handleTreeData(data);
    treeList.value = data;
    if (memberId.value) {
      handleMemberName(treeList.value);
    }
  });
};

// memberName回显
const handleMemberName = (data) => {
  data.some((item) => {
    if (item.children && item.children.length > 0) {
      handleMemberName(item.children);
    } else {
      if (item.id == memberId.value) {
        memberName.value = item.name;
        return;
      }
    }
  });
};

// 禁用
const handleTreeData = (data) => {
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      item.disabled = true;
      handleTreeData(item.children);
    } else {
      if (item.type == 0) {
        item.disabled = false;
      } else {
        item.disabled = true;
      }
    }
  });
};

const handleDialog = () => {
  dialogVisible.value = true;
  if (memberId.value) {
    handleChooseData(treeList.value);
  }
};

// 选中回显
const handleChooseData = (data) => {
  data.some((item) => {
    if (item.children && item.children.length > 0) {
      handleChooseData(item.children);
    } else {
      if (item.id == memberId.value) {
        nextTick(() => {
          handleUpdate([item]);
        });
        return;
      }
    }
  });
};

const handleSearch = () => {
  proxy.$refs.treeRef.filter(inputValue.value);
};

// 过滤节点内容
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const handleChange = (data) => {
  let arr = proxy.$refs.treeRef.getCheckedNodes(true);
  if (arr.length > 1) {
    handleUpdate([data]);
  } else {
    handleUpdate(arr);
  }
};

const handleUpdate = (data) => {
  chooseList.value = data;
  proxy.$refs.treeRef.setCheckedNodes(data);
};

const handleClear = () => {
  emit("update:memberId", null);
  memberName.value = null;
  handleUpdate([]);
  chooseList.value = [];
};

const handleConfirm = () => {
  emit(
    "update:memberId",
    chooseList.value.length > 0 ? chooseList.value[0].id : null
  );
  memberName.value =
    chooseList.value.length > 0 ? chooseList.value[0].name : null;
  dialogVisible.value = false;
};

const handleClose = () => {
  if (!memberId.value) {
    handleClear();
  }
  dialogVisible.value = false;
};

const emit = defineEmits(["update:memberId"]);
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 20px 20px 0 !important;
}

.content {
  display: flex;
  border: 1px solid #d2d9e9;

  &-top {
    height: 55px;
    padding: 10px 15px;
    box-sizing: border-box;
    border-bottom: 1px solid #d2d9e9;
  }

  &-left {
    width: 275px;
    border-right: 1px solid #e5e8ef;
    &-tree {
      min-height: 320px;
      padding: 15px 20px;
      box-sizing: border-box;
    }
  }

  &-right {
    flex: 1;
  }
}

:deep(.el-input__wrapper) {
  background-color: #F3F6FD !important;
}

.el-input :deep(.el-input__icon) {
  font-size: 18px;
}
</style>