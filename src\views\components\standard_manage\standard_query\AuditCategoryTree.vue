<template>
  <div class="category-wrap">
    <div class="top-wrap">
      <div class="title-wrap">
        <i class="iconfont icon-wenzhang c-primary f-18"></i>
        <span>{{ standardName }}</span>
      </div>
      <i @click="handleAddFirstLevel" class="add-icon iconfont icon-tianjia c-primary f-18"></i>
    </div>
    <div
      v-if="auditStore.categoryList && auditStore.categoryList.length > 0"
      style="width: 100%; height: calc(100vh - 225px); overflow: auto"
      class="scroller-bar-style"
    >
      <el-tree
        v-loading="menuLoading"
        :data="auditStore.categoryList"
        ref="treeRef"
        node-key="id"
        empty-text="标准文本暂未解析，请添加目次及内容"
        :highlight-current="true"
        :props="defaultProps"
        :expand-on-click-node="false"
        :default-expand-all="true"
        @node-click="handleNodeClick"
        :draggable="isDrag"
        @node-drop="handleDrop"
        :allow-drop="handleAllowDrop"
        @node-drag-over="handleDragOver"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node" @mouseenter="over(data)" @mouseleave="leave(data)">
            <span class="custom-label">
              <i v-if="node.level == 1" class="iconfont icon-levels c-primary f-18"></i>
              <span v-showToolTip="['tree']">
                <el-tooltip placement="bottom-start" :content="node.label">
                  <span class="level" :class="{ level1: node.level == 1, 'level-other': node.level != 1 }">{{ node.label }}</span>
                </el-tooltip>
              </span>
            </span>
            <span>
              <audit-op-menu v-show="data.isShow" @selected="selectedOpMenu" :node="node" />
            </span>
          </span>
        </template>
      </el-tree>
    </div>

    <el-empty style="height: 70%" v-else />
  </div>
</template>
<script setup>
  import { moveCategory, deleteSection } from '@/api/standard_analysis/analysis_audit';
  import useAuditStandardStore from '@/store/modules/auditStandard';
  import AuditOpMenu from '@/views/components/standard_manage/standard_query/AuditOpMenu.vue';

  const { proxy } = getCurrentInstance();
  const auditStore = useAuditStandardStore();

  const props = defineProps({
    standardName: {
      type: String,
    },
  });
  const { open, standardName } = toRefs(props);

  const menuLoading = ref(false);
  const isDrag = ref(false);
  const currentMenu = ref({});
  const defaultProps = {
    children: 'children',
    label: 'name',
  };

  onMounted(() => {
    isDrag.value = true;

    setTimeout(() => {
      auditStore.treeRef = proxy.$refs.treeRef;
    }, 200);
  });

  const handleAddFirstLevel = () => {
    auditStore.sectionInfo = {
      name: '',
      content: '',
      pid: 0,
      standardId: auditStore.standardId,
      id: null,
      outlineLevel: 0,
    };
    auditStore.currentId = null;
    auditStore.opType = 'add';
  };
  const selectedOpMenu = item => {
    // 0:新增,1:编辑 2:删除
    if (item.opType == 2) {
      //删除
      handleDelete(item);
    } else if (item.opType == 0) {
      //新增
      auditStore.sectionInfo = {
        name: '',
        content: '',
        pid: item.pid, // item.pid是当前选项id，之前步骤已取
        standardId: auditStore.standardId,
        id: '',
        outlineLevel: item.outlineLevel,
      };
      auditStore.currentId = item.pid;
      proxy.$refs.treeRef.setCurrentKey(item.pid);
      proxy.$nextTick(() => {
        auditStore.opType = 'add';
      });
    } else if (item.opType == 1) {
      //编辑
      auditStore.sectionInfo = {
        name: item.name,
        content: '', // 需调用接口获取
        pid: item.pid,
        standardId: auditStore.standardId,
        id: item.id,
        outlineLevel: item.outlineLevel,
      };
      auditStore.currentId = item.id;
      proxy.$refs.treeRef.setCurrentKey(item.id);
      proxy.$nextTick(() => {
        auditStore.opType = 'edit';
      });
    }
  };

  function handleDelete(item) {
    proxy.$modal
      .confirm('确认删除名称为【' + item.name + '】的章条？', '提示')
      .then(function () {
        return deleteSection(item.id);
      })
      .then(() => {
        auditStore.getAuditData(auditStore.standardId);
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }
  const handleNodeClick = async (item, node) => {
    currentMenu.value = item;
    auditStore.currentMenu = item;
    auditStore.scrollToId(item.id);
  };
  const handleAllowDrop = (draggingNode, dropNode, type) => {
    return true;
  };
  // 拖拽跟随滚动
  const handleDragOver = (node, event) => {
    const treeContainer = proxy.$refs.treeRef.$el;
    const scrollContainer = treeContainer.querySelector('.el-scrollbar__view');
    if (!scrollContainer) return;

    const scrollTop = scrollContainer.scrollTop;
    const treeOffsetTop = treeContainer.getBoundingClientRect().top;
    const dragOverOffsetTop = this.dragOverNode.$el.getBoundingClientRect().top;
    const dragOverTop = dragOverOffsetTop - treeOffsetTop;

    // 根据鼠标位置和目标节点位置来计算新的滚动位置
    const newScrollTop = scrollTop + event.clientY - dragOverTop - treeContainer.offsetHeight / 2;

    scrollContainer.scrollTop = newScrollTop;
  };
  const handleDrop = (draggingNode, dropNode, dropType, ev) => {
    let data1 = dropType != 'inner' ? dropNode.parent.data : dropNode.data;
    let nodeData = dropNode.level == 1 && dropType != 'inner' ? data1 : data1.children;

    // 设置父ID,当level为1说明在第一级，pid为空
    let nextId = undefined;
    let pid = undefined;
    let currentIndex = 0;
    nodeData.forEach((element, index) => {
      element.pid = data1.id || 0;
      if (element.id == draggingNode.data.id) {
        currentIndex = index;
        pid = element.pid;
      }
    });

    let len = nodeData.length;
    if (!(len == 1 || currentIndex == len - 1)) {
      nextId = nodeData[currentIndex + 1].id;
    }

    let outlineLevel = dropNode.level - 1;
    let params = {
      id: draggingNode.data.id,
      pid: pid,
      nextId: nextId,
      outlineLevel: outlineLevel, //0:一级 1:二级...
    };
    menuLoading.value = true;

    moveCategory(params)
      .then(response => {
        menuLoading.value = false;
        auditStore.getAuditData(auditStore.standardId);
      })
      .catch(() => {
        menuLoading.value = false;
      });
  };
  const over = item => {
    item.isShow = true;
  };
  const leave = item => {
    proxy.$nextTick(() => {
      if (!item.isOpen) {
        item.isShow = false;
      }
    });
  };
</script>
<style lang="scss" scoped>
  .top-wrap {
    padding: 10px 20px 0 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-wrap {
      flex: 1;
      font-size: 18px;
      color: #333333;
      font-weight: bold;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      span {
        margin-left: 10px;
      }
    }
    .add-icon {
      margin-left: 10px;
      width: 20px;
    }
  }
  .custom-tree-node {
    .level {
      color: #333333;
      margin-left: 10px;
    }
    .level1 {
      font-weight: bold;
      font-size: 16px;
    }
    .level-other {
      font-size: 14px;
    }
  }
  :deep(.el-tree) {
    margin-top: 10px;
  }
  :deep(.el-tree-node__content) {
    height: 40px !important;
    &:hover {
      background: #e9f0fe;
      border-radius: 5px;
      color: $primary-color;
    }
    .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // font-size: 16px;
      // font-weight: bold;
      padding-right: 8px;
      height: 40px;
      overflow: hidden;
      .custom-label {
        flex: 1;
        white-space: nowrap;
        overflow: hidden; //文本超出隐藏
        text-overflow: ellipsis;
      }
    }
  }
  :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
    color: $primary-color;
  }
</style>
