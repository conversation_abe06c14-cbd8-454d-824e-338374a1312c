<template>
  <el-drawer title="查看复审项目" size="75%" append-to-body lock-scroll v-model="props.visible" @close="handleClose">
    <div class="tabs flex flex-sa flex-ai-center">
      <div
        @click="handleCut(item)"
        v-for="item in tabList"
        :key="item.value"
        class="tabs-item pointer"
        :class="flowStatus == item.value ? 'tabs-active' : ''"
      >
        {{ item.title }}
      </div>
    </div>
    <component
      :is="subcomponent"
      :key="subcomponentKey"
      v-model:flowStatus="flowStatus"
      :id="props.id"
      :isDetail="true"
    ></component>
  </el-drawer>
</template>

<script setup>
  import ManageInfo from '@/views/components/standard_revision/review/ManageInfo.vue';
  import ManagePerson from '@/views/components/standard_revision/review/ManagePerson.vue';
  import ManageOpinion from '@/views/components/standard_revision/review/ManageOpinion.vue';
  import ManageStandard from '@/views/components/standard_revision/review/ManageStandard.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
    phase: {
      type: [Number, String],
    },
  });

  const subcomponentKey = ref(0);
  const flowStatus = ref(1);
  const tabList = reactive([
    { title: '项目立项', value: 0 },
    { title: '组建复审组', value: 1 },
    { title: '意见征集', value: 2 },
    { title: '标准复审', value: 3 },
  ]);

  let subcomponent = computed(() => {
    switch (flowStatus.value) {
      case 0:
        return ManageInfo;
        break;
      case 1:
        return ManagePerson;
        break;
      case 2:
        return ManageOpinion;
        break;
      case 3:
        return ManageStandard;
        break;
      default:
        break;
    }
  });

  flowStatus.value = Number(props.phase) || 0;

  const handleCut = item => {
    subcomponentKey.value = item.value;
    flowStatus.value = item.value;
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible']);
</script>

<style lang="scss" scoped>
  .tabs {
    width: 80%;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    color: #333333;
    background-color: #f3f6fd;
    border-radius: 5px;
    margin: 0 auto;

    &-item {
      position: relative;
    }

    &-active {
      color: $primary-color;
      font-weight: 600;

      &:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 3px;
        background-color: $primary-color;
        border-radius: 2px;
      }
    }
  }
</style>
