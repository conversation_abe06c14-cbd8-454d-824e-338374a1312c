import request from '@/utils/request'

//工作台

// 查询标准概述信息
export const getOverview = () => {
  return request({
    url: '/process/common/standard/query/overview',
    method: 'get'
  })
}
// 查询排名前6的标准信息
export const getStandardQueryList = (params) => {
  return request({
    url: '/process/common/standard/query/top',
    method: 'get',
    params
  })
}
// 工作台-最新公告法规
export const getNoticeList = (params) => {
  return request({
    url: '/process/noticeLaws/latest',
    method: 'get',
    params
  })
}
// 查询工作台 - 标准体系top
export const getStandardSystemList = (params) => {
  return request({
    url: '/process/standardSystem/getStandardTop',
    method: 'get',
    params
  })
}
// 工作台 - 获取雷达扫描预警数据TOP
export const getRadarScanList = (params) => {
  return request({
    url: '/process/standardWarnInfo/getRadarScanTop',
    method: 'get',
    params
  })
}
// 工作台 - 获取上次雷达预警时间
export const getRadarLatestTime = () => {
  return request({
    url: '/process/standardWarnInfo/getLastRadarScanWarnTime',
    method: 'get'
  })
}