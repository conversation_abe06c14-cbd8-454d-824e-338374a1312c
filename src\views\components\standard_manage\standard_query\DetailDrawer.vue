<template>
  <el-drawer title="标准详情" size="75%" append-to-body lock-scroll v-model="props.visible" :before-close="handleClose">
    <detail-intro :form="form" @updateData="updateData" />
    <div class="drawer-down">
      <div class="drawer-down-left">
        <detail-info :form="form" class="mb40" />
        <template v-if="form.replaceStdList && form.replaceStdList.length > 0">
          <div class="h-title">替代标准</div>
          <div
            @click="handleClick(item)"
            v-for="(item, index) in form.replaceStdList"
            :key="index"
            class="f-14 mt20"
            :class="{ valid_link: item.id }"
          >
            {{ item.standardCode }}
            <template v-if="item.standardName">| {{ item.standardName }}</template>
          </div>
        </template>
        <template v-if="form.beReplacedStdList && form.beReplacedStdList.length > 0">
          <div class="h-title mt40">被替代标准</div>
          <div
            @click="handleClick(item)"
            v-for="(item, index) in form.beReplacedStdList"
            :key="index"
            class="f-14 mt20"
            :class="{ valid_link: item.id }"
          >
            {{ item.standardCode }}
            <template v-if="item.standardName">| {{ item.standardName }}</template>
          </div>
        </template>
        <template v-if="form.adoptStdList && form.adoptStdList.length > 0">
          <div class="h-title mt40">采用标准</div>
          <div class="">
            <div
              @click="handleClick(item)"
              v-for="(item, index) in form.adoptStdList"
              :key="index"
              class="f-14 mt20"
              :class="{ valid_link: item.id }"
            >
              {{ item.standardCode }}
              <template v-if="item.standardName">| {{ item.standardName }}</template>
            </div>
          </div>
        </template>
        <template v-if="form.quoteStdList && form.quoteStdList.length > 0">
          <div class="h-title mt40">引用标准</div>
          <div
            @click="handleClick(item)"
            v-for="(item, index) in form.quoteStdList"
            :key="index"
            class="f-14 mt20"
            :class="{ valid_link: item.id }"
          >
            {{ item.standardCode }}
            <template v-if="item.standardName">| {{ item.standardName }}</template>
          </div>
        </template>
        <template v-if="[0, 1, 2, 3, 4].includes(Number(form.standardType)) && form.noticeCode">
          <div class="h-title mt40">发布公告</div>
          <div class="f-14 mt20">{{ form.noticeCode }}</div>
        </template>
        <template v-if="[0, 1, 2, 3, 4, 8, 9].includes(Number(form.standardType))">
          <detail-drafter v-if="form.draftersList && form.draftersList.length > 0" :form="form" class="mt40" />
          <detail-unit v-if="form.draftUnitsList && form.draftUnitsList.length > 0" :form="form" class="mt40" />
        </template>
      </div>
      <div class="drawer-down-right">
        <detail-tree
          :content="props.content"
          :form="{ standardId: props.standardId, standardCode: form.standardCode, standardName: form.standardName }"
          class="mb20"
        />
        <detail-graph v-if="isShow" :graphData="graphData" />
        <div v-if="form.files && form.files.length > 0" class="drawer-down-right-file">
          <div>标准文件</div>
          <img @click="handlePreview" :src="fileImage" alt="" />
        </div>
      </div>
    </div>
  </el-drawer>
  <detail-drawer v-if="drawerVisible" v-model:visible="drawerVisible" :standardId="selectionId" :standardType="selectionType" />
</template>

<script setup>
  import { splitStrToArray } from '@/utils/index.js';
  import { getStandardDetail, getNowStandardDetail } from '@/api/standard_manage/standard_query';
  import { getStandardAtlas } from '@/api/standard_manage/standard_query';
  import { base64Encode } from '@/utils/base64';
  import DetailIntro from '@/views/components/standard_manage/standard_query/DetailIntro.vue';
  import DetailInfo from '@/views/components/standard_manage/standard_query/DetailInfo.vue';
  import DetailDrafter from '@/views/components/standard_manage/standard_query/DetailDrafter.vue';
  import DetailUnit from '@/views/components/standard_manage/standard_query/DetailUnit.vue';
  import DetailTree from '@/views/components/standard_manage/standard_query/DetailTree.vue';
  import DetailGraph from '@/views/components/standard_manage/standard_query/DetailGraph.vue';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const fileImage = new URL('@/assets/images/standard/file.png', import.meta.url).href;

  const props = defineProps({
    visible: {
      required: true,
      type: Boolean,
      default: false,
    },
    standardId: {
      required: true,
      type: [String, Number],
    },
    standardType: {
      required: true,
      type: [String, Number],
    },
    content: {
      type: String,
    },
  });

  const form = ref({});
  const selectionId = ref('');
  const selectionType = ref('');
  const drawerVisible = ref(false);
  const isShow = ref(false);
  const graphData = ref({});

  const getDetail = () => {
    getStandardDetail(props.standardId).then(res => {
      handleData(res.data);
    });
    getStandardAtlas(props.standardId).then(res => {
      let data = res.data;
      if (data.data) {
        isShow.value = true;
        data.data = Array.from(
          data.data
            .reduce((map, obj) => {
              map.set(obj.name, obj);
              return map;
            }, new Map())
            .values()
        );
        graphData.value = data;
      }
    });
  };

  const handleData = data => {
    let detailData = data;
    if (detailData.standardTextFiles) {
      try {
        detailData.files = JSON.parse(detailData.standardTextFiles);
      } catch (error) {
        detailData.files = [];
      }
    }
    detailData.draftersList = splitStrToArray(detailData.drafters);
    detailData.draftUnitsList = splitStrToArray(detailData.draftUnits);
    form.value = detailData;
  };

  const updateData = () => {
    getNowStandardDetail(props.standardId).then(res => {
      handleData(res.data);
    });
  };

  const handleClick = row => {
    if (row.id) {
      selectionId.value = row.id;
      selectionType.value = row.standardType;
      drawerVisible.value = true;
    } else {
      proxy.$modal.msgWarning('该标准暂未收录');
    }
  };

  const handlePreview = () => {
    if (form.value.files && form.value.files.length > 0)
      window.open(
        `${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(base64Encode(form.value.files[0].url))}`,
        '_blank'
      );
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible']);

  getDetail();
</script>

<style lang="scss" scoped>
  .drawer-down {
    display: flex;
    justify-content: space-between;

    &-left {
      flex: 1;
    }

    &-right {
      width: 30%;
      max-width: 420px;
      margin-left: 30px;

      &-file {
        width: 100%;
        background: #ffffff;
        border-radius: 5px;
        border: 1px solid #e8e8e8;
        font-weight: 600;
        font-size: 18px;
        color: #333333;
        padding: 25px 15px;
        box-sizing: border-box;
        margin-top: 20px;

        img {
          display: block;
          height: 45px;
          margin-top: 10px;
          cursor: pointer;
        }
      }
    }
  }

  .valid_link:hover {
    cursor: pointer !important;
    color: $primary-color !important;
    text-decoration: underline !important;
  }
</style>
