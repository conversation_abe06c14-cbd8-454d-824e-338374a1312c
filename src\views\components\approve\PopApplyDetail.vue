<template>
  <el-drawer :title="title" size="75%" append-to-body lock-scroll v-model="open" @close="close">
    <approve-process-status :info="approveInfo.wfTaskSimpleVO" />
    <approve-apply-info :info="approveInfo.applyInfo" class="mt20" />
    <approve-revision-info
      v-if="approveInfo.processFormData && approveInfo.processFormData.amend_project_approval"
      :info="approveInfo.processFormData.amend_project_approval"
      class="mt20"
    />
    <div class="flex flex-sb mt20">
      <div class="c-33 f-18 f-bold">审批记录</div>
      <div class="c-primary f-14 pointer" @click="handleViewProcess">查看审批流程图</div>
    </div>
    <approve-audit-process :list="approveInfo.historyProcNodeList" class="mt15" />

    <!-- 流程图弹框 -->
    <approve-process-view v-if="openView" v-model:open="openView" :info="approveInfo" />
  </el-drawer>
</template>

<script setup>
  import { getProcessDetail } from '@/api/approve/todo';
  import ApproveApplyInfo from '@/views/components/approve/ApproveApplyInfo';
  import ApproveRevisionInfo from '@/views/components/approve/ApproveRevisionInfo';
  import ApproveAuditProcess from '@/views/components/approve/ApproveAuditProcess';
  import ApproveProcessView from '@/views/components/approve/ApproveProcessView';
  import ApproveProcessStatus from '@/views/components/approve/ApproveProcessStatus';

  const props = defineProps({
    open: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
      default: undefined,
    },
  });
  const { open, id } = toRefs(props);

  const data = reactive({
    loading: false,
    title: '申请详情',
    openView: false,
    approveInfo: {
      bpmnXml: '',
      flowViewer: {},
      historyProcNodeList: [],
      applyInfo: {}, // 申请信息
      processFormData: {}, // 立项信息
      taskFormData: {},
      processKey: '',
      wfTaskSimpleVO: {},
    },
  });
  const { loading, title, openView, approveInfo } = toRefs(data);

  const close = () => {
    emit('success');
    emit('update:open', false);
  };

  const emit = defineEmits(['update:open', 'success']);

  const getData = () => {
    data.loading = true;
    let params = {
      procInsId: props.id,
    };
    getProcessDetail(params)
      .then(response => {
        if (response.data) {
          data.approveInfo = response.data;
        }
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };

  const handleViewProcess = () => {
    data.openView = true;
  };

  getData();
</script>

<style lang="scss" scoped></style>
