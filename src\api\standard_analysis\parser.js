import request from '@/utils/request'

//列表
export const parseJobList = (params) => {
  return request({
    url: '/process/parseJob/list',
    method: 'get',
    params
  })
}

//结束任务
export const parseJobStop = () => {
  return request({
    url: '/process/parseJob/stop',
    method: 'put'
  })
}

//开始任务
export const parseJobStart = () => {
  return request({
    url: '/process/parseJob/start',
    method: 'put'
  })
}

//重新解析
export const parseJobReparse = (id) => {
  return request({
    url: '/process/parseJob/reparse/' + id,
    method: 'put',
  })
}

//移出
export const parseJobMoveOut = (data) => {
  return request({
    url: '/process/parseJob/moveOut',
    method: 'put',
    data:data
  })
}

//导入
export const parseImportByTemplate = () => {
  return request({
    url: '/process/parseJob/importByTemplate',
    method: 'put',
  })
}

// 解析器状态
export const parseJobStatus = () => {
  return request({
    url: '/process/parseJob/jobStatus',
    method: 'get',
  })
}