<template>
  <div class="intro">
    <div class="f-22 c-33">{{ form.standardCode || '-' }} | {{ form.standardName || '-' }}</div>
    <div v-if="form.standardNameEn" class="f-16 c-33 mt15">{{ form.standardNameEn }}</div>
    <div class="flex mt15">
      <div v-if="form.standardTypeName" class="intro-label label-one">{{ form.standardTypeName || '-' }}</div>
      <div v-if="form.standardAttrName" class="intro-label label-two">{{ form.standardAttrName || '-' }}</div>
      <div v-if="form.standardStatusName" class="intro-label label-three">{{ form.standardStatusName || '-' }}</div>
      <div
        v-if="form.isArticleRepeal == 1 && (form.standardStatus == 0 || form.standardStatus == 1)"
        class="intro-label label-seven"
      >
        条文废止
      </div>
      <div class="intro-label label-five">
        <span class="iconfont icon-xiazai f-20 c-00A2FF mr5"></span>
        {{ form.downloadTotal || '0' }}次
      </div>
      <div class="intro-label label-six">
        <span class="iconfont icon-chaxun f-17 c-04AF00 mr5"></span>
        {{ form.pv || '0' }}次
      </div>
    </div>
    <div class="flex flex-ai-center mt30 w-850">
      <div class="intro-card" :class="form.publishDate ? 'bgc-primary' : 'bgc-99'">
        <div class="intro-card-num" :class="form.publishDate ? ' c-primary' : 'c-99'">1</div>
        <div class="intro-card-date">
          <div class="f-14">发布日期</div>
          <div class="f-18 f-500">{{ form.publishDate || '-' }}</div>
        </div>
      </div>
      <div class="intro-line"></div>
      <div class="intro-card" :class="form.executeDate ? 'bgc-primary' : 'bgc-99'">
        <div class="intro-card-num" :class="form.executeDate ? ' c-primary' : 'c-99'">2</div>
        <div class="intro-card-date">
          <div class="f-14">实施日期</div>
          <div class="f-18 f-500">{{ form.executeDate || '-' }}</div>
        </div>
      </div>
      <div class="intro-line"></div>
      <div class="intro-card" :class="form.repealDate ? 'bgc-primary' : 'bgc-99'">
        <div class="intro-card-num" :class="form.repealDate ? ' c-primary' : 'c-99'">3</div>
        <div class="intro-card-date">
          <div class="f-14">废止日期</div>
          <div class="f-18 f-500">{{ form.repealDate || '-' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);
</script>

<style lang="scss" scoped>
  .intro {
    padding: 30px;
    background-color: #ffffff;
    border-radius: 15px;

    &-label {
      font-size: 14px;
      width: 91px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      margin-right: 12px;
    }

    &-card {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 200px;
      height: 70px;
      color: #ffffff;
      border-radius: 8px;
      padding: 0 10px;
      box-sizing: border-box;

      &-num {
        font-size: 26px;
        font-weight: 500;
        width: 39px;
        height: 39px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #ffffff;
        border-radius: 50%;
      }

      &-date {
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }

    &-line {
      flex: 1;
      max-width: 120px;
      border-top: 2px dotted #e5e5e5;
    }
  }

  .f-500 {
    font-weight: 500;
    margin-top: 7px;
    width: 110px;
  }

  .c-00A2FF {
    color: #00a2ff;
  }

  .c-04AF00 {
    color: #04af00;
  }

  .label-one {
    color: #2f5aff;
    background-color: #eff2ff;
  }

  .label-two {
    color: #ffb400;
    background-color: #fff4d8;
  }

  .label-three {
    color: #00bae2;
    background-color: #e7fbff;
  }

  .label-four {
    color: #f56c6c;
    background-color: #fff1f1;
  }

  .label-five {
    color: #00a2ff;
    background-color: #edf8ff;
  }

  .label-six {
    color: #04ae00;
    background-color: #e5fae4;
  }

  .label-seven {
    color: #7900ff;
    background-color: #f5ecff;
  }
</style>
