<template>
  <div v-show="open" class="knowledge-upload-wrap">
    <div class="knowledge-header-wrap">
      <div class="title">附件上传</div> 
      <div class="icon-wrap">
        <el-icon v-if="isListShow" @click="handleHiddenList"><SemiSelect /></el-icon>
        <el-icon v-else @click="handleHiddenList" ><Plus class="c-00" /></el-icon>
        <el-icon @click="handleClose" class="ml10"><CloseBold /></el-icon>
      </div>
    </div>

    <el-upload
      ref="uploadRef"
      :action="actionUrl"
      v-model:file-list="fileList"
      multiple
      :data="{relationId:commonInfo.fileId}"
      :before-upload="handleBeforeUpload"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :before-remove="beforeRemove"
      :on-exceed="handleExceed"
      :headers="headers"
      :on-success="handleSuccess"
    >
  </el-upload>
  </div>
  
</template>

<script setup>
import { getToken } from "@/utils/auth"
import { setFile } from "@/api/knowledge/library";

const { proxy } = getCurrentInstance()

const data = reactive({
  open: false,
  actionUrl: process.env.VITE_APP_BASE_API + '/system/oss/upload',
  headers: { Authorization: "Bearer " + getToken() },
  fileList: [],
  isListShow: true,
  fileSize: 1024
})
const {open,actionUrl,headers,fileList,isListShow} = toRefs(data)

const commonInfo = inject('commonInfo')

const emit = defineEmits(['close','success'])

const handleHiddenList = () => {
  let d = proxy.$refs.uploadRef.$el.children[1].style.display;
  if(d == 'none'){
    proxy.$refs.uploadRef.$el.children[1].style.display = 'block';
    data.isListShow = true
  }else{
    proxy.$refs.uploadRef.$el.children[1].style.display =  'none';
    data.isListShow = false
  }
}
const handleClose = () => {
  let uploading = data.fileList.some((item) => item.percentage != 100)
  if(uploading){
    let tip = '关闭窗口，将取消正在上传任务，是否继续?'
    proxy.$modal.confirm(tip).then( () => {
      data.fileList = []
    }).then(() => {
      data.open = false
    }).catch(() => {
      
    });
  }else{
    data.fileList = []
    nextTick(() => {
      data.open = false
    })
  }
  
}
watch(()=>data.fileList,(newVal)=>{
  data.open = true
  proxy.$refs.uploadRef.$el.children[1].style.display = 'block';
  data.isListShow = true
})
const handleBeforeUpload = (file) => {
  if (data.fileSize) {
    const isLt = file.size / 1024 / 1024 < data.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${data.fileSize} MB!`);
      return false;
    }
  }
}
const handlePreview = (uploadFile) => {

}
const handleRemove = (file, uploadFiles) => {

}
const beforeRemove = (uploadFile, uploadFiles) => {

}
const handleExceed = (files, uploadFiles) => {

}
const handleSuccess = (response, uploadFiles) => {
  if(response.code == 200){
    if(response.data && response.data.id){
      let pid = !response.data.relationId || response.data.relationId < 0 ? 0 : response.data.relationId
      let params = {
        fileId: response.data.id,
        libId: commonInfo.libraryId,
        pid: pid
      }
      setFile(params).then(response => {
        commonInfo.refreshCategory()
        emit('success')
        proxy.$modal.msgSuccess("上传文件成功")
      }).catch(error => {
        proxy.$modal.msgError("上传文件失败")
      });
    }
  }else{
    proxy.$modal.msgError(response.msg || '上传失败')
  }
}
</script>

<style lang="scss" scoped>
.knowledge-upload-wrap{
  width: 380px;
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 1000;
  font-size: 14px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  .knowledge-header-wrap{
    height: 40px;
    padding: 5px 15px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    display: flex;
    align-items: center;
    .title{
      font-weight: bold;
    }
    .icon-wrap{
      margin-left: auto;
      display: flex;
      align-items: center;
      .el-icon{
        cursor: pointer;
      }
    }
  }
  :deep(.el-upload){
    display: none;
  }
  :deep(.el-upload-list){
    height: 350px;
    padding: 10px;
    .el-upload-list__item{
      padding: 8px 0px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-weight: bold;
      color: #3E4967;
    }
  }
}
</style>