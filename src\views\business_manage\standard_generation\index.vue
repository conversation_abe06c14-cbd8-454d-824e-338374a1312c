<template>
  <div class="app-container">
    <div class="container">
      <div class="input-container">
        <div class="flex-1">
          <textarea v-model="query" class="textarea" placeholder="输入【产品说明】、【规格要求】、【技术参数】等内容，模型将根据内容生成标准范本"
            @keyup.enter="sendQuery"></textarea>
        </div>
        <div class="flex ml20">
          <el-button type="primary" class="button" @click="sendQuery" :disabled="isLoading">开始生成</el-button>
          <el-button class="button" @click="handleStop" :disabled="!isLoading">停止</el-button>
        </div>
      </div>
      <div class="output-main" v-if="renderedContent || loadingMsg">
        <div class="loading" v-if="isLoading">
          <el-icon class="load" :size="20">
            <Loading />
          </el-icon>
          <div class="ml10">{{ loadingMsg }}</div>
        </div>
        <div class="loading flex-sb flex-ai-center" v-if="!isLoading" @click="handleOpenThink">
          <div class="flex flex-ai-center">
            <el-icon v-if="isThinkOpen" :size="16">
              <ArrowRight />
            </el-icon>
            <el-icon v-if="!isThinkOpen" :size="16">
              <ArrowDown />
            </el-icon>
            <div class="ml10">内容生成完毕</div>
          </div>
          <el-button class="buttondown" @click.stop="downloadFile"><el-icon>
              <Download />
            </el-icon>下载md文件</el-button>
        </div>

        <div ref="outputDiv" class="output-container">
          <div class="think-content" v-if="isThinkOpen && thinkContent">
            <MarkdownView :content="thinkContent" />
          </div>
          <MarkdownView :content="renderedContent" />
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import MarkdownView from '@/views/components/business_manage/common/MarkdownView.vue'
import { ref, onMounted } from 'vue';
import { fetchGeneration } from '@/api/business_manage/standard_generation.js'
const { proxy } = getCurrentInstance();
// 响应式数据
const query = ref('');
const fileParam = ref('');
const isDownloadEnabled = ref(false);
const outputDiv = ref(null);
const renderedContent = ref('');
const isLoading = ref(false);
const loadingMsg = ref('');
const thinkContent = ref('');
const isThinkOpen = ref(true);
onMounted(() => {

});
let currentFetch = null;
// 发送查询请求
function sendQuery() {
  if (!query.value) {
    proxy.$modal.msgError("请输入查询内容");
    return;
  }
  let fileName = query.value;
  isLoading.value = true;
  renderedContent.value = '';
  isDownloadEnabled.value = false;
  loadingMsg.value = '';
  fileParam.value = '';
  thinkContent.value = '';
  isThinkOpen.value = true;
  currentFetch = fetchGeneration({ input: query.value }, (event) => {
    if (event.trim().startsWith('data:')) {
      const eventData = event.replace(/^data: ?/, '').trim();
      if (!eventData) {
        return;
      }
      const data = JSON.parse(eventData);
      if (data.content && data.type == 'progress') {
        if (data.content.startsWith('正在') || data.content.startsWith('整合')) {
          loadingMsg.value = data.content;
        } else {
          thinkContent.value += data.content
        }
      }
      if (data.content && data.type == 'final_content') {
        fileParam.value = fileName;
        isLoading.value = false;
        renderedContent.value = data.content;
        query.value = '';
        isThinkOpen.value = false;
      }
      if (outputDiv.value) {
        outputDiv.value.scrollTop = outputDiv.value.scrollHeight; // 滚动到底部
      }
    }
  }, (error) => {
    console.error('请求错误:', error);
    isLoading.value = false;
    renderedContent.value = "<p style='color:red;'>请求失败: " + error.message + "</p>";
  })
}

function handleStop() {
  if (currentFetch) {
    currentFetch.abort();
    currentFetch = null;
    isLoading.value = false;
  }
}
const handleOpenThink = () => {
  isThinkOpen.value = !isThinkOpen.value;
}

// 下载文件
function downloadFile() {
  let time = new Date().getTime();
  const fileName = fileParam.value || time;
  const blob = new Blob([renderedContent.value], { type: 'text/markdown' });
  const link = document.createElement('a');
  link.href = window.URL.createObjectURL(blob);
  link.download = fileName + '.md';
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(link.href);
  document.body.removeChild(link);
}
</script>

<style lang="scss" scoped>
.app-container {
  height: calc(100vh - 90px);
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: calc(100vw - 280px);
  max-width: 100%;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.input-container {
  display: flex;
  align-items: end;
}

.textarea {
  width: 100%;
  height: 80px;
  padding: 10px;
  border: 1px solid #dcdcdc;
  border-radius: 5px;
  font-size: 16px;
  box-sizing: border-box;
  line-height: 1.5;
  outline: none;
}

.textarea::placeholder {
  color: #999;
}

.output-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 15px 0;
  border-radius: 5px;
  min-height: 100px;
  width: 100%;
  box-sizing: border-box;
}

.output-container {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  text-align: left;
  overflow-y: auto;
  flex: 1;
  max-height: 70vh;
}

.loading {
  background-color: #fff;
  padding: 10px 0;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.load {
  animation: rotate 1s linear infinite;
}

.think-content {
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}
</style>