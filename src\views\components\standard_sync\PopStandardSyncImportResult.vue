<template>
  <div class="pop-container">
    <el-dialog
      :append-to-body="true"
      v-model="open"
      width="930px"
      title="更新结果"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="app-container-content">
        <div class="container-bar">
          <div class="bar-left">
            <span class="dot"></span>
            <span class="c-primary">
              本次更新：检测数据
              <span class="c-FFB400">{{ total }}</span>
              条
              <span class="ml20"></span>
              更新数据
              <span class="c-FFB400">{{ updateTotal }}</span>
              条
            </span>
            <div v-if="errorMsg.length > 0" class="m-red">提示：{{ errorMsg }}</div>
          </div>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
          class="mt15"
          :border="true"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
            <template #default="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="standardCode" label="标准号" min-width="200" show-overflow-tooltip />
          <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
          <el-table-column prop="content" label="更新内容" min-width="200" show-overflow-tooltip />
          <el-table-column prop="createTime" label="更新时间" min-width="180" show-overflow-tooltip />
        </el-table>
        <el-pagination
          class="mt20"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  const props = defineProps({
    open: Boolean,
    dataList: {
      type: Array,
      default: [],
    },
    total: {
      type: Number,
      default: 0,
    },
    updateTotal: {
      type: Number,
      default: 0,
    },
    errorMsg: {
      type: String,
      default: '',
    },
  });

  const { open, dataList, total, updateTotal, errorMsg } = toRefs(props);

  const data = reactive({
    loading: false,
    // 默认显示第几页
    currentPage: 1,
    // 总条数，根据接口获取数据长度(注意：这里不能为空)
    totalCount: props.updateTotal,
    // 个数选择器（可修改）
    pageSizes: [10, 20, 30, 40],
    // 默认每页显示的条数（可修改）
    pageSize: 10,
  });
  const { loading, currentPage, totalCount, pageSizes, pageSize } = toRefs(data);

  const emit = defineEmits(['update:open', 'success']);

  const close = () => {
    emit('update:open', false);
  };
  const handleSizeChange = val => {
    // 改变每页显示的条数
    data.pageSize = val;
    // 注意：在改变每页显示的条数时，要将页码显示到第一页
    data.currentPage = 1;
  };
  const handleCurrentChange = val => {
    data.currentPage = val;
  };
</script>

<style lang="scss" scoped>
  .bar-left {
    line-height: 40px;
    background: #edf1ff;
    border-radius: 5px;
    padding-left: 15px;
  }
  .dot {
    width: 7px;
    height: 7px;
    background: #2f5aff;
    border-radius: 50%;
    margin-right: 7px;
    display: inline-block;
  }
</style>
