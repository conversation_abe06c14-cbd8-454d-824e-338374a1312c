<template>
  <el-drawer title="详情" size="75%" append-to-body lock-scroll v-model="props.visible" @close="close">
    <div class="notice-content">
      <div class="h-title">基础信息</div>
      <div class="mt20 flex" style="flex-wrap: wrap">
        <div class="info-item">
          <div class="info-item-title">标准对象：</div>
          <div class="info-item-content">{{ form.standardObject }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标对象：</div>
          <div class="info-item-content">{{ form.indexObject }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标名称：</div>
          <div class="info-item-content">{{ form.indexName }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标内容：</div>
          <div class="info-item-content">{{ form.indexContent }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">计量单位：</div>
          <div class="info-item-content">{{ form.measuringUnit }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标注：</div>
          <div class="info-item-content">{{ form.indexNote }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">试验方法：</div>
          <div class="info-item-content">{{ form.experimentalPlan }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标组ID：</div>
          <div class="info-item-content">{{ form.experimentalGroupId }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">指标类型：</div>
          <div class="info-item-content">{{ form.indexType }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">表注：</div>
          <div class="info-item-content">{{ form.tableNote }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">所属标准：</div>
          <div class="info-item-content">{{ form.fromStandardCode }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">创建人：</div>
          <div class="info-item-content">{{ form.createBy }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-title">创建时间：</div>
          <div class="info-item-content">{{ form.createTime }}</div>
        </div>
      </div>
      <div class="h-title mt20">指标对象属性</div>
      <el-table class="mt20" :data="form.indexObjectAttribute" :border="true">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column prop="indexObjectName" label="指标对象属性名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexObjectValue" label="指标对象属性值" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexObjectType" label="指标对象属性类型" show-overflow-tooltip min-width="150" />
      </el-table>
      <div class="h-title mt20">指标影响因素</div>
      <el-table class="mt20" :data="form.indexInfluenceFactor" :border="true">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column prop="indexInfluenceFactorName" label="指标影响因素名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexInfluenceFactorValue" label="指标影响因素值" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexInfluenceFactorType" label="指标影响因素类型" show-overflow-tooltip min-width="150" />
      </el-table>
    </div>
  </el-drawer>
</template>

<script setup>
  import { getIndexLibDetail } from '@/api/business_manage/standard_indicators';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: Boolean,
    id: [String, Number],
  });

  const form = ref({});

  onMounted(() => {
    if (props.id) getDetail();
  });

  const getDetail = () => {
    getIndexLibDetail({ id: props.id })
      .then(res => {
        let data = res.data;
        data.indexObjectAttribute =
          data.indexObjectAttribute && data.indexObjectAttribute.length > 0 ? data.indexObjectAttribute : [];
        data.indexInfluenceFactor =
          data.indexInfluenceFactor && data.indexInfluenceFactor.length > 0 ? data.indexInfluenceFactor : [];
        form.value = data;
        setTimeout(() => {
          MathJax.typesetPromise();
        }, 300);
      })
      .finally(() => {});
  };

  const emit = defineEmits(['update:visible', 'success']);

  const close = () => {
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .info {
    &-item {
      width: 49%;
      display: flex;

      &:nth-child(n + 3) {
        margin-top: 15px;
      }

      &-title {
        width: 100px;
        color: #999;
        font-size: 14px;
        line-height: 25px;
        text-align: right;
      }

      &-content {
        flex: 1;
        color: #333;
        font-size: 14px;
        line-height: 25px;
        text-align: left;
      }
    }
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
