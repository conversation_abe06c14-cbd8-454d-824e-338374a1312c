<template>
  <div>
    <el-dialog v-model="dialogVisible" width="860" title="函文详情" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="notice-content">
        <div class="title f-18 c-33 fw-b">函文基本信息</div>
        <el-descriptions
          class="margin-top mt20"
          :column="2"
          border
        >
          <el-descriptions-item>
            <template #label>
              <div>
                文号
              </div>
            </template>
            {{ form.letterNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                名称
              </div>
            </template>
            {{ form.letterName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                分类
              </div>
            </template>
            {{ form.letterTypeName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                状态
              </div>
            </template>
            {{ form.letterStatusName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                发文单位
              </div>
            </template>
            {{ form.issueUnit || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                发文日期
              </div>
            </template>
            {{ form.issueDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                创建时间
              </div>
            </template>
            {{ form.createTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                创建人
              </div>
            </template>
            {{ form.createBy || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="title f-18 c-33 fw-b mt20 mb20">附件</div>
        <ele-upload-image
          v-if="form.letterFileList && form.letterFileList.length > 0"
          class="accept-content"
          :responseFn="handleResponse"
          :isShowUploadIcon="false"
          v-model:value="form.letterFileList"
        ></ele-upload-image>
        <span v-else>无</span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancel">关 闭</el-button>
        </div>
      </template>
      
    </el-dialog>
  </div>
  </template>
  
  <script setup>
  import EleUploadImage from "@/components/EleUploadImage"
  import {getStandardLetterDetail} from '@/api/standard_manage/letter_query.js'


  const {proxy} = getCurrentInstance()
  
  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number]
  })
  const {dialogVisible,id} = toRefs(props)
  
  const state = reactive({
    form: {},
  })
  
  const {form} = toRefs(state)

  onMounted(() => {
    if (id.value) getDetail();
  });

  const getDetail = () => {
    getStandardLetterDetail(id.value).then((res) => {
      let data = res.data;
      state.form = data;
      state.dataList = data.standardList;
    });
  };

  
  const emit = defineEmits(['update:dialogVisible','success'])
  const close = () => {
    emit('update:dialogVisible',false)
  }
  const cancel = () => {
    emit('update:dialogVisible',false)
  }
  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id,'url': response.data.url,'name':response.data.name}
  }
  
  
  </script>
  
  <style lang="scss" scoped>
  .ele-upload-image{
    display: block !important;
  }
  </style>