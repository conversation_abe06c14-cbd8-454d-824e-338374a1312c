<template>
  <div class="flex flex-ai-center flex-sb mt30">
    <div class="h-title">复审标准</div>
    <div class="flex flex-ai-center">
      <div v-if="deadlineForm.deadline" class="flex flex-ai-center">
        <div class="c-33 f-14">
          意见征集结束时间：{{ deadlineForm.deadline }}
          <template v-if="form.phase == 2 && deadlineForm.remainTime">
            （
            <span class="c-primary f-14">剩余：{{ deadlineForm.remainTime }}</span>
            ）
          </template>
        </div>
        <el-button
          v-if="form.phase == 2 && isAuth(form.authorizedUserIdList) && !props.isDetail"
          @click="handleSetEndTime"
          class="ml10"
        >
          <i class="iconfont icon-chakantiezishijian mr6 f-14"></i>
          修改结束时间
        </el-button>
      </div>
      <el-button
        v-if="form.phase == 2 && isAuth(form.authorizedUserIdList) && !deadlineForm.deadline && !props.isDetail"
        @click="handleSetEndTime"
        class="ml10"
      >
        <i class="iconfont icon-chakantiezishijian mr6 f-14"></i>
        设置结束时间
      </el-button>
      <div class="flex flex-sb flex-ai-center ml10">
        <el-input v-model="standardParams.keyword" placeholder="请输入标准号或标准名称">
          <template #append>
            <el-button @click="handleQuery" icon="Search" class="img-icon" />
          </template>
        </el-input>
        <el-button plain @click="handleClear" icon="Refresh" class="f-18 ml10"></el-button>
      </div>
    </div>
  </div>
  <el-table :data="standardTableData" :border="true" class="mt15">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column label="标准号" show-overflow-tooltip fixed min-width="200">
      <template #default="{ row }">
        <span @click="handleRowClick(row)" class="c-primary pointer">{{ row.standardCode }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
    <el-table-column label="标准状态" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="reviewBasisName" label="复审依据" show-overflow-tooltip min-width="200" />
  </el-table>
  <pagination
    v-show="standardTotal > 0"
    :total="standardTotal"
    v-model:page="standardParams.pageNum"
    v-model:limit="standardParams.pageSize"
    @pagination="getStandardData"
  />
  <div class="h-title mt30">
    征集意见
    <span>&nbsp;&nbsp;{{ selectRow.standardCode }}</span>
  </div>
  <el-table :data="opinionTableData" :border="true" class="mt15">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column prop="opinionTypeName" label="意见类型" show-overflow-tooltip min-width="150" />
    <el-table-column prop="opinionContent" label="意见" show-overflow-tooltip min-width="200" />
    <el-table-column label="附件" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <el-button
          v-if="row.feedbackFileList && row.feedbackFileList.length > 0"
          @click="handleImg(row.feedbackFileList[0])"
          link
          type="primary"
          style="text-decoration: inherit"
        >
          {{ row.feedbackFileList[0].name }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column prop="createBy" label="提交人" show-overflow-tooltip min-width="150" />
    <el-table-column prop="feedbackDept" label="所属部门" show-overflow-tooltip min-width="150" />
    <el-table-column prop="createTime" label="提交时间" show-overflow-tooltip min-width="200" />
    <el-table-column label="操作" fixed="right" min-width="120">
      <template #default="{ row }">
        <el-button
          v-if="row.feedbackFileList && row.feedbackFileList.length > 0"
          @click="handleDownload(row.feedbackFileList[0])"
          link
          type="primary"
        >
          下载附件
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="opinionTotal > 0"
    :total="opinionTotal"
    v-model:page="opinionParams.pageNum"
    v-model:limit="opinionParams.pageSize"
    @pagination="getOpinionData"
  />
  <edit-deadline-dialog
    v-if="editDeadlineVisible"
    v-model:visible="editDeadlineVisible"
    :id="props.id"
    :deadline="deadlineForm.deadline"
    @success="getDeadline"
  />
  <preview-file v-if="fileVisible" v-model:open="fileVisible" :url="fileUrl" />
</template>

<script setup>
  import { isAuth } from '@/utils/index';
  import { getOpinionDeadline, getReviewInfo, getStandardList, getOpinionLIst } from '@/api/standard_revision/review';
  import EditDeadlineDialog from '@/views/components/standard_revision/review/EditDeadlineDialog.vue';
  import PreviewFile from '@/components/PreviewFile';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [String, Number],
      required: true,
    },
    flowStatus: { type: Number },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const editDeadlineVisible = ref(false);
  const deadlineForm = ref({});
  const standardParams = ref({
    pageNum: 1,
    pageSize: 10,
    reviewId: props.id,
  });
  const form = ref({});
  const standardTableData = ref([]);
  const standardTotal = ref(0);
  const opinionParams = ref({
    pageNum: 1,
    pageSize: 10,
    joinId: props.id,
    joinType: 1,
  });
  const opinionTableData = ref([]);
  const opinionTotal = ref(0);
  const selectRow = ref({});
  const fileVisible = ref(false);
  const fileUrl = ref('');

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  getReviewInfo(props.id).then(res => {
    form.value = res.data;
  });


  const getDeadline = () => {
    getOpinionDeadline(props.id).then(res => {
      deadlineForm.value = res.data;
    });
  };

  getDeadline();

  const handleClear = () => {
    standardParams.value.keyword = '';
    handleQuery();
  };

  const handleQuery = () => {
    standardParams.value.pageNum = 1;
    getStandardData();
  };

  const getStandardData = () => {
    getStandardList(standardParams.value).then(res => {
      standardTableData.value = res.rows;
      standardTotal.value = res.total;
    });
  };

  getStandardData();

  const getOpinionData = () => {
    getOpinionLIst(opinionParams.value).then(res => {
      opinionTableData.value = res.rows;
      opinionTotal.value = res.total;
    });
  };

  getOpinionData();

  const handleSetEndTime = () => {
    editDeadlineVisible.value = true;
  };

  const handleRowClick = row => {
    selectRow.value = row;
    opinionParams.value.pageNum = 1;
    opinionParams.value.standardId = row.standardId;
    getOpinionData();
  };

  const handleImg = row => {
    fileUrl.value = row.url;
    fileVisible.value = true;
  };

  const handleDownload = row => {
    proxy.download('/system/oss/download/' + row.id, {}, row.name);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-input-group__append) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  .img-icon :deep(.el-icon) {
    color: #fff !important;
    font-size: 18px !important;
    position: relative !important;
    top: -2px !important;
  }

  :deep(.el-input-group__append .el-button:focus) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }
</style>
