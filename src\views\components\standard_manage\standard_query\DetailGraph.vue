<template>
  <div class="graph">
    <div>
      <div class="graph-title">标准图谱</div>
      <div @click="handleClick" class="graph-btn pointer">查看</div>
    </div>
  </div>
  <el-dialog
    width="800px"
    title="标准图谱"
    append-to-body
    v-model="visible"
    :lock-scroll="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div id="graph" class="_echart"></div>
  </el-dialog>
</template>

<script setup>
  import * as echarts from 'echarts';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    graphData: {
      required: true,
      type: Object,
    },
  });

  const visible = ref(false);

  let graphEcharts = echarts;
  let graph = undefined;

  const isShow = ref(false);

  onUnmounted(() => {
    graphEcharts.dispose;
  });

  const handleClick = () => {
    visible.value = true;
    proxy.$nextTick(() => {
      initChart();
    });
  };

  const initChart = () => {
    graph = graphEcharts.init(document.getElementById('graph'));
    setOptions(props.graphData);
  };

  const setOptions = data => {
    graph.setOption({
      tooltip: {
        show: false,
      },
      series: [
        {
          top: 20,
          left: 20,
          right: 20,
          bottom: 20,
          silent: true,
          symbolSize: 88,
          type: 'graph',
          roam: 'scale',
          layout: 'force',
          animationDuration: 1500,
          animationEasingUpdate: 'quinticInOut',
          emphasis: { disabled: true },
          itemStyle: { color: '#2F5AFF' },
          label: { show: true, fontSize: 18 },
          force: { repulsion: 500, edgeLength: [150, 170] },
          edgeSymbolSize: [10, 1],
          edgeSymbol: ['arrow', 'circle'],
          edgeLabel: { show: true, color: '#2F5AFF', fontSize: 14, formatter: '{c}' },
          data: data.data,
          links: data.links,
          lineStyle: { color: '#FFC600', opacity: 0.9, width: 1, curveness: 0 },
        },
      ],
    });
    setTimeout(() => {
      graph.resize();
    }, 100);
  };

  const handleClose = () => {
    visible.value = false;
  };
</script>

<style lang="scss" scoped>
  .graph {
    width: 100%;
    height: 120px;
    background-image: url('@/assets/images/standard/graph.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    overflow: hidden;

    &-title {
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
    }

    &-btn {
      width: 72px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background: #ffffff;
      border-radius: 14px;
      opacity: 0.78;
      font-weight: 600;
      font-size: 14px;
      color: #2f5aff;
      margin: 15px auto 0;
    }
  }

  ._echart {
    width: 100%;
    height: 500px;
  }
</style>
