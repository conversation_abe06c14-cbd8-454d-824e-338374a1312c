<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="项目编号:" prop="projectCode">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.projectCode" placeholder="请输入项目编号" />
        </el-form-item>
        <el-form-item label="项目名称:" prop="projectName">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="制修订:" prop="amend">
          <el-select v-model="queryParams.amend" placeholder="请选择制修订类型">
            <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人:" prop="projectManagerName">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.projectManagerName" placeholder="请输入负责人名称" />
        </el-form-item>
        <el-form-item label="阶段:" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择项目阶段">
            <el-option
              v-for="item in bxc_amend_project_approval_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="归档状态:" prop="archiveStatus">
          <el-select v-model="queryParams.archiveStatus" placeholder="请选择归档状态">
            <el-option v-for="item in bxc_archived_status" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期:">
          <el-date-picker
            v-model="createTime"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="" class="one-column">
          <el-button :loading="searchLoading" @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button :loading="searchLoading" plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button v-hasPermi="['standard_revision:manage:add']" @click="handleClick('add')" type="primary">
          <i class="iconfont icon-lixiang f-12 mr6"></i>
          新增立项
        </el-button>

        <el-button v-hasPermi="['standard_revision:manage:export']" @click="handleClick('export')">
          <i class="iconfont icon-daochu f-12 mr6"></i>
          导出
        </el-button>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" width="60" fixed>
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="projectCode" label="项目编号" show-overflow-tooltip min-width="200" fixed />
        <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip min-width="200" fixed />
        <el-table-column prop="amendName" label="制修订" show-overflow-tooltip min-width="120" />
        <el-table-column prop="projectManagerName" label="负责人" show-overflow-tooltip min-width="120" />
        <el-table-column prop="statusName" label="阶段" show-overflow-tooltip min-width="150" />
        <el-table-column label="归档状态" show-overflow-tooltip min-width="120">
          <template #default="{ row }">
            <span :class="row.archiveStatus == 0 ? 'status-red' : 'status-green'">{{ row.archiveStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="planStartTime" label="计划开始日期" show-overflow-tooltip min-width="150" />
        <el-table-column prop="planEndTime" label="计划完成日期" show-overflow-tooltip min-width="150" />
        <el-table-column prop="archiveDate" label="归档日期" show-overflow-tooltip min-width="150" />
        <el-table-column prop="createByName" label="创建人" show-overflow-tooltip min-width="120" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="180" />
        <el-table-column fixed="right" label="操作" min-width="180">
          <template #default="{ row }">
            <el-button @click.stop="handleDialog(row, 'detail')" link type="primary">查看</el-button>
            <template v-if="row.status < 7">
              <el-button
                v-if="isAuth(row.authorizedUserIdList)"
                v-hasPermi="['standard_revision:manage:manage']"
                @click.stop="handleDialog(row, 'manage')"
                link
                type="primary"
              >
                管理
              </el-button>
              <el-button
                v-if="isAuth(row.editAuthorizedUserIdList)"
                v-hasPermi="['standard_revision:manage:edit']"
                @click="handleClick('edit', row)"
                link
                type="primary"
              >
                编辑
              </el-button>
              <el-button
                v-if="isAuth(row.editAuthorizedUserIdList)"
                v-hasPermi="['standard_revision:manage:delete']"
                @click="handleClick('del', row)"
                link
                type="danger"
              >
                删除
              </el-button>
            </template>
            <el-button
              v-if="row.status == 7 && row.archiveStatus == 0"
              v-hasPermi="['standard_revision:manage:finish']"
              @click="handleClick('finish', row)"
              link
              type="primary"
            >
              归档
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <add-revision-dialog v-if="addRevisionVisible" v-model:visible="addRevisionVisible" :id="revisionId" @updateData="getData" />
    <manage-revision-dialog v-if="manageVisible" v-model:visible="manageVisible" v-model:id="revisionId" @updateData="getData" />
    <detail-revision-drawer v-if="detailVisible" v-model:visible="detailVisible" v-model:id="revisionId" />
  </div>
</template>

<script setup name="Manage">
  import { isAuth } from '@/utils/index';
  import { getRevisionList, delRevision, putArchive } from '@/api/standard_revision/manage';
  import AddRevisionDialog from '@/views/components/standard_revision/AddRevisionDialog.vue';
  import ManageRevisionDialog from '@/views/components/standard_revision/ManageRevisionDialog.vue';
  import DetailRevisionDrawer from '@/views/components/standard_revision/DetailRevisionDrawer.vue';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_amend, bxc_amend_project_approval_status, bxc_archived_status } = proxy.useDict(
    'bxc_standard_amend',
    'bxc_amend_project_approval_status',
    'bxc_archived_status'
  );

  const searchLoading = ref(false);
  const tableLoading = ref(false);
  const revisionId = ref(undefined);
  const addRevisionVisible = ref(false);
  const manageVisible = ref(false);
  const createTime = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const detailVisible = ref(false);

  const getData = val => {
    searchLoading.value = true;
    tableLoading.value = true;
    if (val) queryParams.value[val] = 1;
    queryParams.value.startCreateTime = createTime.value.length > 0 ? createTime.value[0] : '';
    queryParams.value.endCreateTime = createTime.value.length > 0 ? createTime.value[1] : '';
    getRevisionList(queryParams.value)
      .then(res => {
        let rows = res.rows;
        rows.forEach(item => {
          item.selected = false;
        });
        tableData.value = rows;
        total.value = res.total;
      })
      .finally(() => {
        searchLoading.value = false;
        tableLoading.value = false;
      });
  };

  const handleDialog = (row, type) => {
    revisionId.value = row.id;
    if (type == 'detail') {
      detailVisible.value = true;
    } else {
      manageVisible.value = true;
    }
  };

  const handleClear = () => {
    createTime.value = [];
    proxy.$refs.queryRef.resetFields();
    getData('pageNum');
  };

  const handleClick = (type, row) => {
    switch (type) {
      case 'add':
        revisionId.value = '';
        addRevisionVisible.value = true;
        break;
      case 'edit':
        revisionId.value = row.id;
        addRevisionVisible.value = true;
        break;
      case 'del':
        proxy
          .$confirm('确认删除项目编号为【' + row.projectCode + '】的制修订项目？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delRevision({ ids: [row.id] })
              .then(res => {
                proxy.$modal.msgSuccess('项目删除成功！');
              })
              .finally(() => {
                getData();
              });
          })
          .catch(() => {});
        break;
      case 'export':
        proxy.download(
          '/process/amendProjectApproval/export',
          {
            ...queryParams.value,
          },
          '标准制修订项目数据.xlsx'
        );
        break;
      case 'finish':
        proxy
          .$confirm('确认归档当前制修订标准信息？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            putArchive(row.id)
              .then(() => {
                proxy.$modal.msgSuccess('归档成功！');
              })
              .finally(() => {
                getData();
              });
          })
          .catch(err => {
            console.error(err);
          });
        break;
      default:
        break;
    }
  };

  getData();
</script>

<style lang="scss" scoped></style>
