<template>
  <div class="pop-container">
    <el-dialog :modal-append-to-body="false" v-model="open" width="480px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      
      <el-form
        :model="form"
        label-position='top'
        ref="formRef"
        :rules="formRules"
        @submit.native.prevent
      >
        <el-form-item v-if="opType == 0" label="父级目录" prop="pid">
          <el-tree-select
            style="width: 100%;"
            v-model="form.pid"
            :data="categoryList"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="请选择父级目录"
            :default-expand-all="true"
            check-strictly
          /> 
        </el-form-item>
        <el-form-item label="目录名称" prop="name">
          <el-input
            maxlength="30"
            v-model="form.name"
            placeholder="请输入目录名称"
            @keyup.enter="save"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getStandardSystemDetail,addStandardSystem,updateStandardSystem } from '@/api/standard_manage/standard_system'

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  menuList: {
    type: Array,
    default: []
  },
  id: [String, Number],
  opType: {
    type: Number,
    default: 0 // 0:新增,1:重命名
  },
});
const { open, opType } = toRefs(props)

const categoryList = computed(() => {
  return [{id: 0,name: '主目录',children: props.menuList}]
})
const data = reactive({
  title: '新增体系目录',
  loading: false,
  form: {
    pid: undefined,
    id: undefined,
    name: ''
  },
  formRules: {
    name: [
      { required: true, trigger: "blur", message: "目录名称不能为空" }
    ]
  }
})

const {title,loading,form,formRules} = toRefs(data)

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true;
      if(data.form.id){
        updateStandardSystem(data.form).then(response => {
          data.loading = false;
          emit('update:open',false);
          emit('success');
          proxy.$modal.msgSuccess("重命名成功");
        }).catch(error => {
          data.loading = false;
        });
      }else{
        addStandardSystem(data.form).then(response => {
          data.loading = false;
          emit('update:open',false);
          emit('success');
          proxy.$modal.msgSuccess("新增成功");
        }).catch(error => {
          data.loading = false;
        });
      }
    }
  });
}
const getData = () => {
  data.loading = true;
  getStandardSystemDetail(props.id).then((response) => {
    if(response.data){
      data.form = response.data
      data.loading = false
    }
    
  }).catch(() => {
    data.loading = false
  });
}
if(props.opType == 0) {
  data.form.pid = props.id || 0
}else{
  data.form.id = props.id
  data.title = '重命名体系目录'
  getData()
}
</script>

<style lang="scss" scoped>

</style>