<template>
  <el-dialog
    append-to-body
    title="选择标准"
    width="1200px"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-sb">
      <div class="dialog-left">
        <el-form :model="queryParams" ref="queryRef" label-width="auto" class="flex">
          <el-form-item label="" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入标准号或标准名称">
              <template #append>
                <el-button @click="handleQuery" icon="Search" class="img-icon" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="" class="w-auto">
            <el-button plain @click="handleClear" icon="Refresh" class="f-18"></el-button>
          </el-form-item>
        </el-form>
        <el-table
          v-loading="tableLoading"
          ref="tableRef"
          :data="tableData"
          :border="true"
          row-key="standardId"
          @select="handlerSelect"
          @selection-change="handleSelectionChange"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column type="selection" :reserve-selection="true" />
          <el-table-column label="序号" fixed="left" width="70">
            <template #default="{ $index }">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip fixed="left" min-width="150" />
          <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="180" />
          <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="120" />
          <el-table-column label="标准状态" show-overflow-tooltip min-width="120">
            <template #default="{ row }">
              <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <div v-if="props.selectMode == 'checkbox'" class="dialog-right">
        <div class="dialog-right-title">已选标准（{{ selectedRows.length }}）</div>
        <div class="scroller-bar-style">
          <div v-for="(item, index) in selectedRows" :key="index" class="dialog-right-item">
            <div class="dialog-right-item-index">{{ index + 1 }}</div>
            <div class="dialog-right-item-content">
              <div class="overflow-ellipsis">{{ item.standardCode }}</div>
              <div class="overflow-ellipsis">{{ item.standardName }}</div>
            </div>
            <el-icon @click="handleUncheck(item)" class="dialog-right-item-icon"><Delete class="f-20 c-FF0000" /></el-icon>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="btnLoading" type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getStandardList } from '@/api/standard_manage/standard_query';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    /**
     * 列表接口参数（非必传）
     * @param [{k: '', v: ''}]
     */
    params: {
      type: Array,
      default: () => {
        return [];
      },
    },
    /**
     * 选择方式（必传）
     * 单选:radio
     * 多选:checkbox
     */
    selectMode: {
      type: String,
      default: 'checkbox',
    },
    /**选中的数据（回显选中状态）*/
    selectTable: {
      type: Array,
      default: () => {
        return [];
      },
    },
  });

  const tableRef = ref();
  const tableLoading = ref(false);
  const btnLoading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const selectedRows = ref([]);

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  const getList = () => {
    tableLoading.value = true;
    if (props.params.length > 0) {
      props.params.forEach(item => {
        queryParams.value[item.k] = item.v;
      });
    }
    getStandardList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
        proxy.$nextTick(() => {
          if (props.selectTable && props.selectTable.length > 0) {
            let ids = props.selectTable.map(item => item.standardId);
            console.log(ids);

            tableData.value.forEach(item => {
              if (item.standardId && ids.includes(item.standardId)) {
                tableRef.value.toggleRowSelection(item, true);
              }
            });
          }
        });
      })
      .finally(() => {
        tableLoading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    handleQuery();
  };

  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const handleSelectionChange = selection => {
    if (props.selectMode == 'checkbox') {
      selectedRows.value = selection;
    } else {
      selectedRows.value = selection.length > 1 ? selection.slice(1) : selection;
    }
  };

  const handlerSelect = (selection, row) => {
    if (props.selectMode == 'radio' && selection.length == 2) {
      tableRef.value.clearSelection();
      tableRef.value.toggleRowSelection(row, true);
    }
  };

  const handleUncheck = row => {
    const selectData = selectedRows.value;
    tableRef.value.clearSelection();
    selectData.forEach(item => {
      tableRef.value.toggleRowSelection(item, item.standardId != row.standardId ? true : false);
    });

    proxy.$nextTick(() => {
      getList();
    });
  };

  const handleConfirm = () => {
    btnLoading.value = true;
    if (selectedRows.value && selectedRows.value.length > 100) {
      proxy.$modal.msgWarning('最多选择100条');
      btnLoading.value = false;
    } else if (selectedRows.value.length > 0) {
      selectedRows.value.forEach(item => {
        props.selectTable.forEach(selectItem => {
          if (item.standardId == selectItem.standardId && selectItem.reviewBasis) {
            item.reviewBasis = selectItem.reviewBasis;
          }
        });
      });
      emit('chooseData', selectedRows.value);
      handleClose();
    } else {
      proxy.$modal.msgWarning('请选择标准');
      btnLoading.value = false;
    }
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'chooseData']);

  getList();
</script>

<style lang="scss">
  .hideSelectAll .el-table th.el-table__cell:nth-child(1) .cell {
    display: none !important;
  }
</style>
<style lang="scss" scoped>
  .dialog {
    &-left {
      width: 0;
      flex: 1;
    }

    &-right {
      width: 280px;
      border: 1px solid #e3e3e3;
      margin-left: 30px;
      // min-height: calc(100vh - 500px);
      // max-height: calc(100vh - 200px);

      &-title {
        height: 50px;
        padding: 0 20px !important;
        box-sizing: border-box;
        line-height: 50px;
        font-size: 16px;
        font-weight: 600;
        color: $primary-color;
        border-bottom: 1px solid #e3e3e3;
      }

      &-item {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e3e3e3;

        &-index {
          width: 15%;
          font-size: 16px;
        }

        &-content {
          width: 70%;
          font-size: 14px;
        }

        &-icon {
          width: 15%;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
  }

  .pagination-container {
    padding-bottom: 0 !important;
  }

  .scroller-bar-style {
    max-height: 530px;
    padding: 0 18px;
    box-sizing: border-box;
    overflow-y: scroll;
  }

  :deep(.el-input-group__append) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  :deep(.el-button:focus:not(.el-button:hover)) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  .img-icon :deep(.el-icon) {
    color: #fff !important;
    font-size: 18px !important;
    position: relative !important;
    top: -2px !important;
  }

  :deep(.el-form-item) {
    margin-right: 10px !important;
  }

  :deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
  }
</style>
