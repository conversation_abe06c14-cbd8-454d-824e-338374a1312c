<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="标准号" prop="standardCode">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.standardCode" placeholder="请输入标准号" />
        </el-form-item>
        <el-form-item label="标准名称" prop="standardName">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.standardName" placeholder="请输入标准名称" />
        </el-form-item>
        <el-form-item label="标准类型" prop="standardType">
          <el-select v-model="queryParams.standardType" placeholder="请选择标准类型">
            <el-option v-for="item in bxc_standard_type" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准状态" prop="standardStatus">
          <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态">
            <el-option v-for="item in bxc_standard_status" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="CCS分类号" prop="standardTypeCodeGb">
          <el-cascader
            v-model="queryParams.standardTypeCodeGb"
            :options="CCSOptions"
            :props="CCSProps"
            clearable
            filterable
            placeholder="请选择CCS分类号"
          >
            <template #default="{ node }">
              <span>{{ node.value }} &nbsp; {{ node.label }}</span>
            </template>
          </el-cascader>
        </el-form-item>
        <el-form-item label="ICS分类号" prop="standardTypeCodeIso">
          <el-cascader
            v-model="queryParams.standardTypeCodeIso"
            :options="ICSOptions"
            :props="ICSProps"
            clearable
            filterable
            placeholder="请选择ICS分类号"
          >
            <template #default="{ node }">
              <span>{{ node.value }} &nbsp; {{ node.label }}</span>
            </template>
          </el-cascader>
        </el-form-item>
        <el-form-item label="">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button
          v-hasPermi="['standard_analysis:pending_analysis:analysis']"
          @click="handleClick('analysis')"
          :disabled="!selectedRows.length > 0"
          type="primary"
          class="iconfont icon-jiexi"
        >
          解析
        </el-button>
      </div>
      <el-table
        ref="tableRef"
        v-loading="tableLoading"
        :data="tableData"
        :border="true"
        row-key="id"
        @selection-change="handleSelectionChange"
        class="mt15"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="selection" :reserve-selection="true" />
        <el-table-column label="序号" fixed min-width="80">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" fixed label="标准号" min-width="200" show-overflow-tooltip />
        <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
        <el-table-column label="标准状态" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="getStandardStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="standardTypeCodeGb" label="CCS分类" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardTypeCodeIso" label="ICS分类" min-width="180" show-overflow-tooltip />
        <el-table-column label="解析状态" min-width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="getAnalysisStatusColor(row.analysisStatus)">{{ row.analysisStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="standardTextPages" label="文件页数" min-width="120" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" min-width="100">
          <template #default="{ row }">
            <el-button @click="handleClick('detail', row)" type="primary" link>查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <detail-drawer v-if="drawerVisible" v-model:visible="drawerVisible" :standardId="standardId" :standardType="standardType" />
  </div>
</template>

<script setup>
  import { getStandardType } from '@/api/standard_manage/standard_query';
  import { getStandardList, addAnalysis } from '@/api/standard_analysis/pending_analysis';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_type, bxc_standard_status } = proxy.useDict('bxc_standard_type', 'bxc_standard_status');

  const tableRef = ref();
  const tableLoading = ref(false);
  const CCSOptions = ref([]);
  const ICSOptions = ref([]);
  const CCSProps = reactive({
    checkStrictly: true,
    emitPath: false,
    label: 'name',
    value: 'code',
  });
  const ICSProps = reactive({
    checkStrictly: true,
    emitPath: false,
    label: 'nameCn',
    value: 'code',
  });
  const selectedRows = ref([]);
  const standardId = ref('');
  const standardType = ref(undefined);
  const drawerVisible = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    analysisStatus: 0,
    isHave: 1,
  });
  const tableData = ref([]);
  const total = ref(0);

  const getStandardStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-intro-blue';
        break;
      case 1:
        return 'status-intro-green';
        break;
      case 3:
        return 'status-intro-gray';
        break;
      case 4:
        return 'status-intro-red';
        break;
      default:
        break;
    }
  };

  const getAnalysisStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-red';
        break;
      case 1:
        return 'status-blue';
        break;
      case 2:
        return 'status-purple';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-yellow';
        break;
      case 5:
        return 'status-green';
        break;
      default:
        break;
    }
  };

  const getSelectList = () => {
    getStandardType({ pageSize: 0, type: 0 }).then(res => {
      CCSOptions.value = res.data;
    });
    getStandardType({ pageSize: 0, type: 1 }).then(res => {
      ICSOptions.value = res.data;
    });
  };

  const getData = val => {
    tableLoading.value = true;
    if (val) queryParams.value[val] = 1;
    getStandardList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        tableLoading.value = false;
      });
  };

  const handleClick = (type, row) => {
    switch (type) {
      case 'analysis':
        proxy
          .$confirm('确认将当前选中的 ' + selectedRows.value.length + ' 个标准，加入解析器？', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            let arr = [...new Set(selectedRows.value.map(item => item.id))];
            addAnalysis(arr).then(res => {
              tableRef.value.clearSelection();
              proxy.$modal.msgSuccess('添加成功！');
              getData();
            });
          })
          .catch(err => {});
        break;
      case 'detail':
        standardId.value = row.standardId;
        standardType.value = row.standardType;
        drawerVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleSelectionChange = selection => {
    selectedRows.value = selection;
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    queryParams.value.analysisStatus = 0;
    queryParams.value.isHave = 1;
    getData('pageNum');
  };

  getSelectList();
  getData();
</script>

<style lang="scss" scoped>
  .icon-jiexi:before {
    margin-right: 6px;
  }
</style>
