import request from '@/utils/request';

export const getReviewList = data => {
  return request({
    url: '/process/projectReview/list',
    method: 'get',
    params: data,
  });
};

export const putArchive = data => {
  return request({
    url: '/process/projectReview/archive/' + data,
    method: 'put',
  });
};

export const putTerminate = data => {
  return request({
    url: '/process/projectReview/terminate/' + data,
    method: 'put',
  });
};

export const delReview = data => {
  return request({
    url: '/process/projectReview/remove',
    method: 'delete',
    data: data,
  });
};

export const addReview = data => {
  return request({
    url: '/process/projectReview/save',
    method: 'post',
    data: data,
  });
};

export const editReview = data => {
  return request({
    url: '/process/projectReview/update',
    method: 'put',
    data: data,
  });
};

export const approveReview = data => {
  return request({
    url: '/process/projectReview/saveApprove',
    method: 'post',
    data: data,
  });
};

export const editApproveReview = data => {
  return request({
    url: '/process/projectReview/updateApprove',
    method: 'put',
    data: data,
  });
};

export const getReviewDetail = data => {
  return request({
    url: '/process/projectReview/' + data,
    method: 'get',
  });
};

export const getReviewInfo = data => {
  return request({
    url: '/process/projectReview/getProjectInfo/' + data,
    method: 'get',
  });
};

export const getStandardList = data => {
  return request({
    url: '/process/reviewStandard/list',
    method: 'get',
    params: data,
  });
};

export const getExpertList = data => {
  return request({
    url: '/process/reviewExpert/list',
    method: 'get',
    params: data,
  });
};

export const delExpert = data => {
  return request({
    url: '/process/reviewExpert/remove',
    method: 'delete',
    data: data,
  });
};

export const addExpert = data => {
  return request({
    url: '/process/reviewExpert',
    method: 'post',
    data: data,
  });
};

export const setTeamLeader = data => {
  return request({
    url: '/process/reviewExpert/setTeamLeader/' + data,
    method: 'put',
  });
};

export const componentPersonnel = data => {
  return request({
    url: '/process/projectReview/reviewTeamComplete/' + data,
    method: 'put',
  });
};

export const getOpinionLIst = data => {
  return request({
    url: '/process/solicitOpinionFeedback/list',
    method: 'get',
    params: data,
  });
};

export const setDeadline = data => {
  return request({
    url: '/process/projectReview/setDeadline',
    method: 'post',
    data: data,
  });
};

export const updateDeadline = data => {
  return request({
    url: '/process/projectReview/updateDeadline',
    method: 'post',
    data: data,
  });
};

export const getOpinionDeadline = data => {
  return request({
    url: '/process/projectReview/getDeadline/' + data,
    method: 'get',
  });
};

export const getRecheckList = data => {
  return request({
    url: '/process/reviewConclusion/listAll',
    method: 'get',
    params: data,
  });
};

export const delRecheck = data => {
  return request({
    url: '/process/reviewConclusion/remove',
    method: 'delete',
    data: data,
  });
};

export const addRecheck = data => {
  return request({
    url: '/process/reviewStandard/addConclusion',
    method: 'post',
    data: data,
  });
};

export const putRecheck = data => {
  return request({
    url: '/process/reviewStandard/editConclusion',
    method: 'put',
    data: data,
  });
};

export const putFeedback = data => {
  return request({
    url: '/process/solicitOpinionFeedback',
    method: 'put',
    data: data,
  });
};

export const reviewConclusion = data => {
  return request({
    url: '/process/reviewConclusion',
    method: 'post',
    data: data,
  });
};

export const reviewFinish = data => {
  return request({
    url: '/process/projectReview/complete/' + data,
    method: 'put',
  });
};
