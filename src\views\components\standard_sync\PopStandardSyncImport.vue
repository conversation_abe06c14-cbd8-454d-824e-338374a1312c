<template>
  <div class="pop-container">
    <el-dialog :modal-append-to-body="false" v-model="open" width="460px" title="导入更新" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="flex flex-column">
        <el-button
          class="mt10 h50 f-16"
          type="primary"
          @click="handleDownloadTemplate"
          ><i class="iconfont icon-xiazai mr5 f-20"></i>下载导入模板
        </el-button>
        <div class="mt10 f-14" style="color:red;">请按导入模板正确填写更新数据</div>
        <el-upload
          style="width: 100%;"
          ref="uploadRef"
          v-model:file-list="fileList"
          :limit="1"
          :before-upload="handleBeforeUpload"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :on-error="handleFileError"
          :auto-upload="true"
          :show-file-list="false"
        >
          <div style="width: 100%;" class="el-upload__text">
            <el-button
              style="width: 100%;"
              class="mt30 h50 f-16 bgc-primary"
              type="primary"
              ><i class="iconfont icon-daoru mr5 f-16"></i>导入更新数据
            </el-button>
          </div>
          <template #tip>
            <div class="el-upload__tip mb20 mt10 f-14" style="color:red">提示：仅支持上传xlsx,xls格式文件，最多上传1项，单个文件不能超过5MB</div>
          </template>
        </el-upload>
      </div>

      <!-- 导入更新结果弹框 -->
      <pop-standard-sync-import-result v-if="openResult" v-model:open="openResult" :dataList="dataList" :total="total" :updateTotal="updateTotal" :errorMsg="errorMsg" />
    </el-dialog>
  </div>
</template>

<script setup>
import PopStandardSyncImportResult from '@/views/components/standard_sync/PopStandardSyncImportResult'
import { getToken } from '@/utils/auth'
import { ElLoading } from 'element-plus'

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  id: [String, Number],
});
const { open } = toRefs(props)

const data = reactive({
  openResult: false,
  dataList: [],
  total: 0,
  updateTotal: 0,
  loading: false,
  upload: {
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的数据
    // updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: "Bearer " + getToken() },
    // 上传的地址
    url: process.env.VITE_APP_BASE_API + "/process/standardOperationLog/importData"
  },
  fileSize: 5,
  fileType: ['xlsx', 'xls'],
  fileList: [],
  errorMsg: ''
})
const {
  openResult,
  dataList,
  total,
  updateTotal,
  loading,
  upload,
  fileSize,
  fileType,
  fileList,
  errorMsg
} = toRefs(data)

let downloadLoadingInstance;

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const handleDownloadTemplate = () => {
  window.location.href='/file/标准导入更新模板.xlsx'
}
// 上传前校检格式和大小
const handleBeforeUpload = (file) =>{
  let isValid = false;
  if (data.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isValid = data.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isValid = file.type.indexOf("image") > -1;
  }
  if (!isValid) {
    proxy.$modal.msgError(
      `文件格式不正确, 请上传${data.fileType.join("/")}格式文件!`
    );
    return false;
  }
  if (data.fileSize) {
    const isLt = file.size / 1024 / 1024 < data.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${data.fileSize} MB!`);
      return false;
    }
  }
  return true;
}
// 文件上传中处理
const handleFileUploadProgress = (event, file, fileList) => {
  data.upload.isUploading = true;
  data.loading = true;
  downloadLoadingInstance = ElLoading.service({ text: "正在上传数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
}
// 文件上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  data.upload.open = false;
  data.upload.isUploading = false;
  data.loading = false;
  proxy.$refs.uploadRef.clearFiles();
  downloadLoadingInstance.close();
  const { code,data: dataT,msg } = response 

  if(code == 200){
    data.openResult = true
    data.total = dataT.total
    data.dataList = dataT.list
    data.updateTotal = dataT.updateTotal
    data.errorMsg = dataT.errorMsg || ''

    emit('update:open',false);
    emit('success');
  }else{
    proxy.$modal.msgError('上传失败,请重试！')
  }
  
}
const handleFileError = (error,uploadFile,uploadFiles) => {
  downloadLoadingInstance.close();
}

</script>

<style lang="scss" scoped>
:deep(.el-upload--text) {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}
</style>