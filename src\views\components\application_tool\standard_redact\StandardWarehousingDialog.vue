<template>
  <el-dialog
    width="700px"
    title="数字标准结构化入库"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="card">
      <el-icon class="f-16 c-primary mr6"><InfoFilled /></el-icon>
      数字标准与本地标准库中标准关联，并将数字标准以结构化形式存入关联标准结构化数据中！
    </div>
    <el-form ref="queryRef" :model="form" :rules="rules" class="mt20">
      <el-form-item label="关联标准" prop="label">
        <el-input
          @click="handleChoose"
          readonly
          v-model="form.label"
          suffix-icon="ArrowDown"
          placeholder="请选择关联标准"
          style="width: 400px"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">入库</el-button>
    </template>
    <choose-standard
      v-if="standardVisible"
      v-model:visible="standardVisible"
      selectMode="radio"
      :params="[{ k: 'isAnalysis', v: 0 }]"
      :selectTable="form.standardList"
      @chooseData="chooseData"
    />
  </el-dialog>
</template>

<script setup>
  import { getStorageStandardInfo, standardWrite } from '@/api/application_tool/standard_redact';
  import ChooseStandard from '@/views/components/standard_revision/review/ChooseStandard.vue';

  const { proxy } = getCurrentInstance();
  const emit = defineEmits(['update:visible', 'updateData']);

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  onMounted(() => {
    if (props.id) {
      getStorageStandardInfo(props.id).then(res => {
        let data = res.data;
        if (data?.standardCode) {
          form.value.standardList = [data];
          form.value.label = data.standardCode + ' | ' + data.standardName;
        }
      });
    }
  });

  const standardVisible = ref(false);
  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    label: [{ required: true, message: '请选择关联标准', trigger: 'change' }],
  });

  const handleChoose = () => {
    standardVisible.value = true;
  };

  const chooseData = val => {
    form.value.standardList = val;
    form.value.label = val[0].standardCode + ' | ' + val[0].standardName;
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        standardWrite({ standardId: form.value.standardList[0].standardId, id: props.id })
          .then(() => {
            proxy.$message.success('入库成功！');
            emit('updateData');
            handleClose();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .card {
    display: flex;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    background-color: #f3f6fd;
    font-size: 14px;
    color: #333;
  }
</style>
