<template>
  <div class="pop-container">
    <el-dialog v-model="dialogVisible" width="1300" title="查新记录详情" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      
      <div class="app-container-content pd0">
        <div class="container-bar fresh-bar">
          <div class="bar-left">
            <div class="c-primary desc">查新标准数：<span class="c-FFB400">{{statInfo.count || '0'}}</span> 条；检索到标准数：<span class="c-FFB400">{{statInfo.onCount || '0'}}</span> 条；未检索到标准数：<span class="c-FFB400">{{statInfo.nonCount || '0'}}</span> 条；即将实施标准：<span class="c-FFB400">{{statInfo.tobeCount || '0'}}</span> 条；现行标准：<span class="c-FFB400">{{statInfo.activeCount || '0'}}</span> 条；废止标准：<span class="c-FFB400">{{statInfo.abolishCount || '0'}}</span> 条；被替代标准：<span class="c-FFB400">{{statInfo.beReplacedCount || '0'}}</span> 条</div>
          </div>
          <div class="bar-right">
            <el-button
              type="primary"
              @click="handleExport"
              icon="Download"
              >下载查新结果
            </el-button>
          </div>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList"
          class="mt10"
          :border="true"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column 
            type="index"
            align="center" 
            label="序号" 
            fixed="left"  
            min-width="55"
          >
            <template #default="scope">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="standardCode"
            label="标准号"
            min-width="200"
            fixed="left"
            show-overflow-tooltip
          />
          <el-table-column
            prop="standardName"
            label="标准名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            prop="searchResultName"
            label="查询结果"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span :class="scope.row.searchResult == '1' ? 'm-red' : ''">{{scope.row.searchResultName}}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="standardStatusName"
            label="标准状态"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="executeDate"
            label="实施日期"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="repealDate"
            label="废止日期"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            prop="beReplacedStandardCode"
            label="被替代标准号"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="beReplacedStandardName"
            label="被替代标准名称"
            min-width="260"
            show-overflow-tooltip
          />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancel">关 闭</el-button>
        </div>
      </template>
      
    </el-dialog>
  </div>
  </template>
  
  <script setup>
  import { getStandardFreshList } from '@/api/application_tool/standard_fresh'

  const {proxy} = getCurrentInstance()
  
  const props = defineProps({
    dialogVisible: Boolean,
    batchNumber: [String, Number]
  })
  const {dialogVisible,batchNumber} = toRefs(props)
  
  const data = reactive({
    loading: false,
    dataList:[],
    statInfo:{},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      batchNumber: props.batchNumber
    },
    total:0
  })
  
  const {loading,dataList,statInfo,queryParams,total} = toRefs(data)

  const getList = () => {
    data.loading = true;
    getStandardFreshList(data.queryParams).then((response) => {
      if(response.rows){
        data.dataList = response.rows
        data.total = response.total
        data.statInfo = response.bean
      }
      data.loading = false
    }).catch(() => {
      data.loading = false
    });
  }
  const handleExport = () => {
    proxy.download("process/noveltySearch/downloadNoveltySearch", {
      batchNumber: data.queryParams.batchNumber
    }, `标准查新_${new Date().getTime()}.xlsx`);
  }
  getList();
  const emit = defineEmits(['update:dialogVisible'])
  const close = () => {
    emit('update:dialogVisible',false)
  }
  const cancel = () => {
    emit('update:dialogVisible',false)
  }
  
  </script>
  
  <style lang="scss" scoped>
  .fresh-bar {
    .bar-left{
      flex: 3;
      
      .desc{
        position: relative;
        padding-left: 15px;
        height: 40px;
        line-height: 40px;
        &::before{
          content: '';
          width: 7px;
          height: 7px;
          background: #2F5AFF;
          border-radius: 50%;
          position: absolute;
          top: 40%;
          left: 0;
        }
      }
      
    }
    .bar-right{
      flex: 1;
    }
  }
  .pd0{
    padding: 0;
  }

  </style>