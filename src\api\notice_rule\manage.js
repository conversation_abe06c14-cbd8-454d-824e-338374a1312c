import request from '@/utils/request'

// 新增公告法规目录结构
export const addNoticeCatalog = (data) => {
  return request({
    url: '/process/noticeLawsIndex',
    method: 'post',
    data
  })
}

//公告法规目录列表
export const getNoticeCatalogList = (data) => {
  return request({
    url: '/process/noticeLawsIndex/list',
    method: 'get',
    data
  })
}

//公告法规目录详情
export const getNoticeCatalogDetail = (id) => {
  return request({
    url: '/process/noticeLawsIndex/' + id,
    method: 'get'
  })
}

//公告法规目录编辑
export const editNoticeCatalog = (data) => {
  return request({
    url: '/process/noticeLawsIndex',
    method: 'put',
    data
  })
}

//公告法规目录删除
export const delNoticeCatalog = (id) => {
  return request({
    url: '/process/noticeLawsIndex/delete/' + id,
    method: 'delete'
  })
}

//公告新增
export const addNoticeLaws = (data) => {
  return request({
    url: '/process/noticeLaws',
    method: 'post',
    data
  })
}

//公告列表
export const getNoticeManageList = (data) => {
  return request({
    url: '/process/noticeLaws/manageList',
    method: 'get',
    params: data
  })
}

//公告详情
export const getNoticeManageDetail = (id) => {
  return request({
    url: '/process/noticeLaws/findOne/' + id,
    method: 'get'
  })
}

//修改公告法规
export const editNoticeManage = (data) => {
  return request({
    url: '/process/noticeLaws',
    method: 'put',
    data
  })
}

//发布
export const noticePublish = (data) => {
  return request({
    url: '/process/noticeLaws/publish',
    method: 'put',
    data
  })
}

//取消发布
export const noticePublishCancel = (data) => {
  return request({
    url: '/process/noticeLaws/publishCancel',
    method: 'put',
    data
  })
}


//公告法规目录删除
export const delNotice = (data) => {
  return request({
    url: '/process/noticeLaws',
    method: 'delete',
    data
  })
}

//置顶
export const noticePutTop = (data) => {
  return request({
    url: '/process/noticeLaws/putTop',
    method: 'put',
    data
  })
}

//取消置顶
export const noticePutTopCancel = (data) => {
  return request({
    url: '/process/noticeLaws/putTopCancel',
    method: 'put',
    data
  })
}

//人员树
export function userTreeSelect() {
  return request({
    url: '/process/noticeLaws/userTree',
    method: 'get'
  })
}