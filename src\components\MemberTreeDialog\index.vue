<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择"
    :before-close="handleClose"
    width="650px"
    append-to-body
  >
    <div class="content">
      <div class="content-left">
        <div class="content-top flex-center">
          <el-input
            v-model="inputValue"
            placeholder="请输入关键字"
            suffix-icon="Search"
            @input="handleSearch"
          />
        </div>
        <div class="content-left-tree">
          <el-scrollbar height="390px">
            <el-tree
              ref="treeRef"
              :data="treeList"
              show-checkbox
              node-key="id"
              highlight-current
              :props="defaultProps"
              :filter-node-method="filterNode"
              @check-change="handleChange"
            />
          </el-scrollbar>
        </div>
      </div>
      <div class="content-right">
        <div class="content-top flex flex-sb flex-ai-center">
          <div>已选：{{ chooseList.length || 0 }}个员工</div>
          <el-button
            type="primary"
            plain
            color="#2F5AFF"
            size="small"
            @click="handleClear"
            >清空</el-button
          >
        </div>
        <div>
          <div class="content-left-tree">
            <el-scrollbar height="390px">
              <b-tags
                @handleUpdate="handleUpdate"
                v-if="chooseList.length > 0"
                :addBtn="false"
                v-model:tags="chooseList"
              />
              <empty v-else />
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" color="#2F5AFF" @click="handleConfirm">
          保存
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import BTags from "@/components/BTags";
import { memberTree } from "@/api/common";

const { proxy } = getCurrentInstance();

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  tags: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const { dialogVisible, tags } = toRefs(props);

const defaultProps = {
  children: "children",
  label: "name",
};

const data = reactive({
  inputValue: "",
  treeList: [],
  chooseList: [],
});
const { inputValue, treeList, chooseList } = toRefs(data);

onMounted(() => {
  getMemberTree();
});

const getMemberTree = () => {
  memberTree().then((res) => {
    let data = res.data;
    handleData(data);
    treeList.value = data;
    nextTick(() => {
      if (tags.value && tags.value.length > 0) {
        chooseList.value = tags.value;
        handleUpdate(chooseList.value);
      }
    });
  });
};

const handleData = (data) => {
  data.forEach((item) => {
    if (item.type == 0) {
      item.disabled = false;
      return;
    }
    if (item.type == 1 && item.children && item.children.length > 0) {
      item.disabled = false;
      handleData(item.children);
    } else {
      item.disabled = true;
    }
  });
};

const handleSearch = () => {
  proxy.$refs.treeRef.filter(inputValue.value);
};

// 过滤节点内容
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const handleChange = () => {
  nextTick(() => {
    chooseList.value = proxy.$refs.treeRef.getCheckedNodes(true);
  });
};

const handleUpdate = (data) => {
  proxy.$refs.treeRef.setCheckedNodes(data);
};

const handleClear = () => {
  chooseList.value = [];
  tags.value = [];
  handleUpdate([]);
};

const handleConfirm = () => {
  emit("handleChoose", chooseList.value);
  emit("update:dialogVisible", false);
};

const handleClose = () => {
  emit("handleChoose", tags.value);
  emit("update:dialogVisible", false);
};

const emit = defineEmits([
  "handleChoose",
  "update:dialogVisible",
]);
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 20px 20px 0 !important;
}
.content {
  display: flex;
  border: 1px solid #d2d9e9;
  &-top {
    height: 55px;
    padding: 10px 15px;
    box-sizing: border-box;
    border-bottom: 1px solid #d2d9e9;
  }
  &-left {
    width: 275px;
    border-right: 1px solid #e5e8ef;
    &-tree {
      min-height: 320px;
      padding: 15px 20px;
      box-sizing: border-box;
    }
  }
  &-right {
    flex: 1;
  }
}
:deep(.el-input__wrapper) {
  background-color: #F3F6FD !important;
}
.el-input :deep(.el-input__icon) {
  font-size: 18px;
}
</style>