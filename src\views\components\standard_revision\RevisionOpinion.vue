<template>
  <div>
    <el-button
      v-if="props.activeTab == props.activeStep && !props.isDetail && isAuth(form.authorizedUserIdList)"
      @click="handleClick('start')"
      type="primary"
    >
      <i class="iconfont icon-qidong f-14 mr6"></i>
      启动下一阶段【标准审查】
    </el-button>
    <el-tabs v-model="activeName" @tab-change="handleTab" class="mt20">
      <el-tab-pane label="意见稿" name="draft">
        <revision-draft-table ref="draftRef" :id="props.id" :isDetail="props.isDetail" />
      </el-tab-pane>
      <el-tab-pane label="征求意见" name="feedback">
        <revision-feedback-table
          ref="feedbackRef"
          :id="props.id"
          :activeStep="props.activeStep"
          :activeTab="props.activeTab"
          :isDetail="props.isDetail"
        />
      </el-tab-pane>
      <el-tab-pane label="记录文件" name="file">
        <revision-file-table
          ref="fileRef"
          :id="props.id"
          :activeTab="props.activeTab"
          :activeStep="props.activeStep"
          :isDetail="props.isDetail"
        />
      </el-tab-pane>
      <el-tab-pane v-if="activeName == 'draft' && !props.isDetail" :disabled="true">
        <template #label>
          <div v-if="props.activeTab == props.activeStep" class="flex flex-column">
            <el-button @click="handleClick('publish')" icon="Plus">发布征求意见稿</el-button>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane v-if="activeName == 'file' && !props.isDetail" :disabled="true">
        <template #label>
          <ele-upload-image
            v-if="props.activeTab == props.activeStep"
            :fileSize="10"
            :multiple="true"
            :customization="true"
            @success="handleTab('file')"
            :data="{ relationId: props.id, moduleType: props.activeStep, moduleFileType: 1 }"
            :uploadUrl="'/process/amendProjectApproval/upload'"
            :fileType="['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
          >
            <el-button>
              <span class="iconfont icon-shangchuan f-14 mr6"></span>
              上传记录文件
            </el-button>
          </ele-upload-image>
        </template>
      </el-tab-pane>
      <el-tab-pane v-if="activeName == 'feedback' || props.isDetail" :disabled="true"></el-tab-pane>
    </el-tabs>

    <publish-draft-dialog
      v-if="publishDraftVisible"
      :id="props.id"
      v-model:visible="publishDraftVisible"
      @success="handleTab('draft')"
    />
    <edit-deadline-dialog
      v-if="editDeadlineVisible"
      v-model:visible="editDeadlineVisible"
      :defaultForm="defaultForm"
      @updateDeadline="getData"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
  </div>
</template>

<script setup>
  import { isAuth } from '@/utils/index';
  import { amendProjectApproval, startNextPhase } from '@/api/standard_revision/manage';
  import PublishDraftDialog from '@/views/components/standard_revision/PublishDraftDialog.vue';
  import RevisionFileTable from '@/views/components/standard_revision/RevisionFileTable.vue';
  import RevisionDraftTable from '@/views/components/standard_revision/RevisionDraftTable.vue';
  import RevisionFeedbackTable from '@/views/components/standard_revision/RevisionFeedbackTable.vue';
  import EditDeadlineDialog from '@/views/components/standard_revision/EditDeadlineDialog.vue';
  import EleUploadImage from '@/components/EleUploadImage';
  import PreviewFile from '@/components/PreviewFile';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    activeTab: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const fileRef = ref(null);
  const draftRef = ref(null);
  const feedbackRef = ref(null);
  const loading = ref(true);
  const activeName = ref('draft');
  const form = ref({});
  const openFile = ref(false);
  const currentUrl = ref('');
  const defaultForm = ref({});
  const publishDraftVisible = ref(false);
  const editDeadlineVisible = ref(false);

  const getData = () => {
    loading.value = true;
    amendProjectApproval(props.id)
      .then(res => {
        form.value = res.data;
        if (form.value.status) emit('update:activeStep', Number(form.value.status));
      })
      .finally(() => {
        loading.value = false;
      });
  };

  function handleClick(type) {
    switch (type) {
      case 'start':
        let arr = draftRef.value.tableData || [];
        if (arr.length > 0 && arr[0].solicitStatus == 0) {
          proxy.$modal.msgWarning('当前存在正在征求中意见稿，不可进入下一阶段！');
        } else {
          proxy
            .$confirm('确认将项目【' + form.value.projectName + '】的制修定阶段设置为标准审查？', '阶段启动', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
            .then(() => {
              startNextPhase(props.id).then(res => {
                if (!res.msg) {
                  proxy.$modal.msgSuccess('设置成功！');
                  emit('update:activeStep', 4);
                  emit('update:activeTab', 4);
                } else {
                  draftRef.value.getData();
                }
              });
            })
            .catch(() => {});
        }
        break;
      case 'publish':
        let list = draftRef.value.tableData || [];
        if (list.length > 0 && list[0].solicitStatus == 0) {
          proxy.$modal.msgWarning('当前有征集中意见稿！');
        } else {
          publishDraftVisible.value = true;
        }
        break;
      case 'setTime':
        defaultForm.value = {
          id: props.id,
          deadline: form.value.deadline,
        };
        editDeadlineVisible.value = true;
        break;
      default:
        break;
    }
  }

  const handleTab = e => {
    switch (e) {
      case 'file':
        fileRef.value.getData();
        break;
      case 'draft':
        draftRef.value.getData();
        break;
      case 'feedback':
        feedbackRef.value.getData();
        break;
      default:
        break;
    }
  };

  getData();

  const emit = defineEmits(['update:activeStep', 'update:activeTab']);
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__nav) {
    width: 100%;
    position: relative;
  }

  :deep(.el-tabs__item:last-child) {
    padding: 0 !important;
    position: absolute !important;
    right: 0 !important;
  }

  :deep(.el-tabs__header) {
    margin-bottom: 25px !important;
  }
</style>
