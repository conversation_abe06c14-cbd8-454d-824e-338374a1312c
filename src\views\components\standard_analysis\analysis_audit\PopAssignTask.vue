<template>
  <div class="pop-container">
    <el-dialog :append-to-body="true" v-model="open" width="500px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="tip-box">
        <el-icon color="#0272FF" size="18"><InfoFilled /></el-icon>
        <span class="ml10">当前选中分派审核标准共计：{{props.standardIds.length || 0}}条</span>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="app-dialog mt-10">
        <el-form-item label="审核人" prop="userId">
          <el-input
            v-model="form.userName"
            placeholder="请选择任务分派审核人员"
            @click="handleClick"
            readOnly
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="close">关闭</el-button>
          <el-button @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">提交</span>
            <span v-else>提交中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
    <member-tree-dialog
      v-if="memberDialog"
      :isMulti="false"
      v-model:dialogVisible="memberDialog"
      v-model:tags="selectedList"
      @handleChoose="handleChoose"
    />
  </div>
</template>

<script setup>
import { setAssign } from "@/api/standard_analysis/analysis_audit";
import MemberTreeDialog from "@/components/MemberTreeDialog";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  standardIds: {
    type: Array,
    default: ()=>[]
  }
});
const { open } = toRefs(props)

const data = reactive({
  title: '任务分派',
  loading: false,
  memberDialog: false,
  selectedList: []
})

const {title,loading,memberDialog,selectedList} = toRefs(data)

const form = ref({
  userId: undefined,
  userName: ''
});
const rules = ref({
  userId: [{ required: true, message: '请选择任务分配审核人员', trigger: 'change' }],
});

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}


const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true
      let params = {
        userId: form.value.userId,
        standardIds: props.standardIds
      }
      setAssign(params).then(response => {
        data.loading = false;
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("任务分派成功");
      }).catch(error => {
        data.loading = false;
      });
    }
  });
}
const handleClick = () => {
  memberDialog.value = true
}
const handleChoose = (rows) => {
  if(rows && rows.length > 0){
    data.selectedList = rows
    form.value.userId = rows[0].id
    form.value.userName = rows[0].name
  }else{
    data.selectedList = []
    form.value.userId = undefined
    form.value.userName = ''
  }
};
</script>

<style lang="scss" scoped>
.tip-box {
  padding: 15px 20px;
  background: #EFF6FF;
  border-radius: 5px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
:deep(.el-form-item){
  margin-right: 0px !important;
}
</style>