<template>
  <el-drawer title="详情" size="80%" append-to-body lock-scroll v-model="props.dialogVisible" :before-close="handleClose">
    <div class="h-title">基础信息</div>
    <el-descriptions class="mt15" :column="2" :border="true">
      <el-descriptions-item label="试验单编号">{{ form.testSheetCode || '-' }}</el-descriptions-item>
      <el-descriptions-item label="样品名称">{{ form.sampleName || '-' }}</el-descriptions-item>
      <el-descriptions-item label="样品型号">{{ form.sampleModel || '-' }}</el-descriptions-item>
      <el-descriptions-item label="样品数量">{{ form.sampleNum || '-' }}</el-descriptions-item>
      <el-descriptions-item label="委托单位">{{ form.entrustedUnit || '-' }}</el-descriptions-item>
      <el-descriptions-item label="联系人">{{ form.contactPerson || '-' }}</el-descriptions-item>
      <el-descriptions-item label="联系方式">{{ form.contactPhone || '-' }}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{ form.createBy || '-' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间" :span="2">{{ form.createTime || '-' }}</el-descriptions-item>
      <el-descriptions-item label="委托单位意见" :span="2">{{ form.entrustedOpinion || '-' }}</el-descriptions-item>
    </el-descriptions>
    <div class="h-title mt30">试验项目</div>
    <el-table :data="form.testProjectList" :border="true" class="mt15">
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" type="index" width="60" fixed />
      <el-table-column prop="indexName" label="检验项目" show-overflow-tooltip min-width="180" />
      <el-table-column prop="experimentalPlan" label="试验方法" show-overflow-tooltip min-width="180" />
      <el-table-column prop="testConditions" label="试验条件" show-overflow-tooltip min-width="180" />
      <el-table-column prop="indexContent" label="试验要求" show-overflow-tooltip min-width="150" />
      <el-table-column prop="fromStandardCode" label="所属标准" show-overflow-tooltip min-width="150" />
    </el-table>
  </el-drawer>
</template>

<script setup>
  import { getExperimentDetail } from '@/api/business_manage/test_sheet';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number],
  });

  const form = ref({});

  getExperimentDetail(props.id).then(res => {
    form.value = res.data;
  });

  const handleClose = () => {
    emit('update:dialogVisible', false);
  };

  const emit = defineEmits(['update:dialogVisible']);
</script>

<style lang="scss" scoped></style>
