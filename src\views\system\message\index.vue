<template>
  <div class="wrap">
    <div class="seeting">
      <div class="set-item flex flex-ai-center flex-sb" v-for="(item,index) in messageList" :key="index">
        <div>
          <div class ="mb10 f-18 f-bold c-33">{{ item.configName }}</div>
          <div class="mb15 f-14 c-99">{{ item.remark }}</div>
        </div>
        <el-switch @change="handleChange(item)" :loading="loading" size="large" active-text="开启" inactive-text="关闭"	v-model="item.configFlag" />
      </div>
    </div>
  </div>
</template>


<script setup>
import {getMessageConfigs} from "@/api/message/message"
import { updateConfig } from "@/api/system/config";
const { proxy } = getCurrentInstance()
const state = reactive({
  params:{},
  messageList:[],
  loading:false
});

const {params,messageList,loading} = toRefs(state)

//获取参数
const getConfig = () => {
  state.loading = true
  getMessageConfigs().then(res => {
    let data = res.data;
    data.forEach(item => {
      item.configFlag = item.configValue == 1 ? true:false;
    })
    messageList.value = data;
    state.loading = false
  }).catch(() => {
    state.loading = false
  });
}

//修改
const handleChange = (item) => {
  state.loading = true
  state.params = {
    configKey:item.configKey,
    configName:item.configName,
    configValue:item.configFlag == true? 1 : 0,
    configId:item.configId
  }
  updateConfig(state.params).then(res => {
    proxy.$modal.msgSuccess('修改成功')
    state.loading = false
  }).catch(() => {
    item.configFlag = !item.configFlag
    item.configValue = item.configFlag ? '1' : '0'
    state.loading = false
  });
}
getConfig()
</script>

<style lang="scss" scoped>
.wrap{
  padding: 20px;
}
.seeting{
  box-sizing: border-box;
  height: 85vh;
  border-radius: 15px;
  background-color: #fff;
  padding: 10px 0;
}
.set-item{
  padding: 20px 30px;
  border-bottom: 1px solid #E8E8E8;
}
.mb15{
  margin-bottom: 15px;
}
</style>