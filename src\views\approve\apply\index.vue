<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="流程名称:" prop="procDefName">
          <el-input
            v-model="queryParams.procDefName"
            placeholder="请输入流程名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="审批状态:" prop="processStatus">
          <el-select
            v-model="queryParams.processStatus"
            placeholder="请选择审批状态"
            clearable
          >
            <el-option
              v-for="dict in flowable_process_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发起时间:">
          <el-date-picker
            v-model="processDateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectProcessDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="完成时间:">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="container-bar">
        <div class="bar-right">
        </div>
      </div>
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column 
          type="index"
          align="center" 
          label="序号" 
          fixed="left"  
          min-width="55"
        >
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="procDefName"
          label="流程名称"
          min-width="200"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processCreateTime"
          label="发起时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processStatusName"
          label="审批状态"
          min-width="150"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :class="`c-${scope.row.processStatus}`">{{scope.row.processStatusName}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="taskName"
          label="当前审批节点"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processFinishTime"
          label="完成时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="duration"
          label="流程耗时"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button
              v-if="scope.row.processStatus == 'running'"
              @click.stop="handleRevoke(scope.row)"
              v-hasPermi="['approve:apply:revoke']"
              link
              size="small"
              class="f-14 c-primary"
            >
              撤回
            </el-button>
            <el-button
              v-if="scope.row.processStatus == 'terminated' || scope.row.processStatus == 'canceled'"
              @click.stop="handleRestart(scope.row)"
              v-hasPermi="['approve:apply:restart']"
              link
              size="small"
              class="f-14 c-primary"
            >
              重启
            </el-button>
            <el-button
              @click.stop="handleDetail(scope.row)"
              v-hasPermi="['approve:apply:detail']"
              link
              size="small"
              class="f-14 c-primary"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <!-- 申请详情弹框 -->
    <pop-apply-detail v-if="open" v-model:open="open" :id="currentId"/>
    <!-- 重启弹框 -->
    <add-revision-dialog
      v-if="openRestart"
      v-model:visible="openRestart"
      :id="currentId"
      :isRestart="true"
      @updateData="getList"
     />
  </div>
</template>

<script setup>
import { getApplyList,revokeApply } from '@/api/approve/apply'
import PopApplyDetail from '@/views/components/approve/PopApplyDetail'
import AddRevisionDialog from '@/views/components/standard_revision/AddRevisionDialog.vue';

const { proxy } = getCurrentInstance()

const {flowable_process_status} = proxy.useDict('flowable_process_status')

const data = reactive({
  loading: false,
  open: false,
  openRestart: false,
  processDateRange: [],
  dateRange: [],
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    procDefName: undefined,
    processStatus: undefined,
    processStartTime: undefined,
    processEndTime: undefined,
    processFinishStartTime: undefined,
    processFinishEndTime: undefined,
  },
  currentId: undefined
});
const {
  loading,
  open,
  openRestart,
  dateRange,
  processDateRange,
  total,
  dataList,
  queryParams,
  currentId
} = toRefs(data);

const resetQuery = () => {
  data.dateRange = [];
  data.processDateRange = [];
  proxy.resetForm("queryFormRef");
  selectDate();
  selectProcessDate();
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.queryParams.pageNum = 1;
  getList();
}

const getList = () => {
  data.loading = true;
  getApplyList(data.queryParams).then((response) => {
    if(response.rows){
      data.dataList = response.rows
      data.total = response.total
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const selectDate = () => {
  if (data.dateRange && data.dateRange.length > 0) {
    data.queryParams.processFinishStartTime = data.dateRange[0];
    data.queryParams.processFinishEndTime = data.dateRange[1];
  } else {
    data.queryParams.processFinishStartTime = undefined;
    data.queryParams.processFinishEndTime = undefined;
  }
}
const selectProcessDate = () => {
  if (data.processDateRange && data.processDateRange.length > 0) {
    data.queryParams.processStartTime = data.processDateRange[0];
    data.queryParams.processEndTime = data.processDateRange[1];
  } else {
    data.queryParams.processStartTime = undefined;
    data.queryParams.processEndTime = undefined;
  }
}
const handleDetail = (row) => {
  data.currentId = row.procInsId
  data.open = true
}
const handleRevoke = (row) => {
  let tip = '确认撤回选中的审批流程？'
  proxy.$modal.confirm(tip,'提示').then(function () {
    return revokeApply({procInsId: row.procInsId});
  }).then(() => {
    proxy.$modal.msgSuccess("申请撤回成功");
    getList()
  }).catch(() => {
    
  });
}
const handleRestart = (row) => {
  data.currentId = row.procInsId
  data.openRestart = true
}
getList()

</script>

<style lang="scss" scoped>

</style>