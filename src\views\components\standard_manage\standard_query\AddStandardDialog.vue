<template>
  <el-dialog
    width="930px"
    :title="isEdit ? '编辑标准' : '标准入库'"
    append-to-body
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline">
      <el-form-item label="标准号" prop="standardCode">
        <el-input :disabled="props.isEdit" v-model.trim="form.standardCode" placeholder="请输入完整的标准号">
          <template #append>
            <el-button @click="handleSearch" class="iconfont icon-chakan f-16" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="标准名称" prop="standardName">
        <el-input v-model.trim="form.standardName" maxlength="100" placeholder="请输入标准名称" />
      </el-form-item>
      <template v-if="[0, 1, 2, 3, 4, 8, 9].includes(Number(props.standardType))">
        <el-form-item label="英文名称" prop="standardNameEn">
          <el-input v-model.trim="form.standardNameEn" maxlength="250" placeholder="请输入标准英文名称" />
        </el-form-item>
      </template>
      <template v-if="props.standardType == 5 || props.standardType == 6">
        <el-form-item label="中文名称" prop="standardNameEn">
          <el-input v-model.trim="form.standardNameEn" maxlength="100" placeholder="请输入标准中文名称" />
        </el-form-item>
      </template>
      <el-form-item label="标准状态" prop="standardStatus">
        <el-select clearable v-model="form.standardStatus" placeholder="请选择标准状态">
          <el-option v-for="item in bxc_standard_status" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <template v-if="[0, 1, 2, 3, 4].includes(Number(props.standardType))">
        <el-form-item label="制修订" prop="amend">
          <el-select clearable v-model="form.amend" placeholder="请选择标准制修订类型">
            <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="标准性质" prop="standardAttr">
          <el-select clearable v-model="form.standardAttr" placeholder="请选择标准性质">
            <el-option v-for="item in bxc_standard_attr" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="发布日期" prop="publishDate">
        <el-date-picker
          clearable
          v-model="form.publishDate"
          type="date"
          placeholder="请选择标准发布日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="实施日期" prop="executeDate">
        <el-date-picker
          v-model="form.executeDate"
          type="date"
          placeholder="请选择标准实施日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      <template v-if="props.standardType == 5 || props.standardType == 6">
        <el-form-item label="发布组织" prop="ctName">
          <el-input v-model.trim="form.ctName" maxlength="100" placeholder="请输入标准发布组织名称" />
        </el-form-item>
        <el-form-item label="标准语言" prop="languageType">
          <el-input v-model.trim="form.languageType" maxlength="100" placeholder="请输入标准发布语言" />
        </el-form-item>
      </template>
      <el-form-item label="CCS分类号" prop="standardTypeCodeGb">
        <el-cascader
          v-model="form.standardTypeCodeGb"
          :options="CCSOptions"
          :show-all-levels="false"
          :props="{ label: 'name', value: 'code', emitPath: false }"
          clearable
          filterable
          placeholder="请搜索/选择CCS分类号"
        >
          <template #default="{ node }">
            <span>{{ node.value }} &nbsp; {{ node.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item label="ICS分类号" prop="standardTypeCodeIso">
        <el-cascader
          v-model="form.standardTypeCodeIso"
          :options="ICSOptions"
          :show-all-levels="false"
          :props="{ label: 'nameCn', value: 'code', emitPath: false }"
          clearable
          filterable
          placeholder="请搜索/选择ICS分类号"
          >
          <template #default="{ node }">
            <span>{{ node.value }} &nbsp; {{ node.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item label="废止日期" prop="repealDate">
        <el-date-picker
          v-model="form.repealDate"
          type="date"
          placeholder="请选择标准废止日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      <template v-if="[0, 1, 2, 3, 4].includes(Number(props.standardType))">
        <el-form-item label="标准类别" prop="standardCategory">
          <el-select clearable v-model="form.standardCategory" filterable placeholder="请选择标准类别">
            <el-option v-for="item in bxc_standard_category" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="被替代标准号" prop="beReplacedStandardCode">
        <el-input v-model.trim="form.beReplacedStandardCode" placeholder="请输入被替代标准号" />
      </el-form-item>
      <el-form-item label="被替代标准名称" prop="beReplacedStandardName">
        <el-input v-model.trim="form.beReplacedStandardName" placeholder="请输入被替代标准名称" />
      </el-form-item>
      <el-form-item prop="allReplaceStandardCode">
        <template #label>
          <span>替代标准号</span>
          <el-tooltip content="当有多个替代标准信息时，使用中文顿号(、)分割" placement="top-start">
            <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input v-model.trim="form.allReplaceStandardCode" placeholder="请输入全部替代标准号" />
      </el-form-item>
      <template v-if="[0, 1, 2, 3, 4].includes(Number(props.standardType))">
        <el-form-item label="公告号" prop="noticeCode">
          <el-input v-model.trim="form.noticeCode" maxlength="100" placeholder="请输入标准发布公告号" />
        </el-form-item>
        <el-form-item label="是否条文废止" prop="isArticleRepeal">
          <el-select clearable v-model="form.isArticleRepeal" placeholder="请选择标准是否条文废止">
            <el-option v-for="item in yes_no" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <template v-if="[0, 1, 2, 3, 4, 8, 9].includes(Number(props.standardType))">
        <el-form-item label="归口单位" prop="registryUnit">
          <el-input v-model.trim="form.registryUnit" maxlength="200" placeholder="请输入标准归口单位" />
        </el-form-item>
      </template>
      <template v-if="props.standardType == 0">
        <el-form-item label="执行单位" prop="applyUnit">
          <el-input v-model.trim="form.applyUnit" maxlength="200" placeholder="请输入标准执行单位" />
        </el-form-item>
        <el-form-item label="主管部门" prop="manageDept">
          <el-input v-model.trim="form.manageDept" maxlength="100" placeholder="请输入标准主管部门" />
        </el-form-item>
      </template>
      <template v-if="props.standardType == 1 || props.standardType == 2">
        <el-form-item label="批准发布部门" prop="confirmPublishDept">
          <el-input v-model.trim="form.confirmPublishDept" maxlength="200" placeholder="请输入标准批准发布部门" />
        </el-form-item>
        <template v-if="props.standardType == 1">
          <el-form-item label="行业领域" prop="industryCategory">
            <el-select clearable filterable v-model="form.industryCategory" placeholder="请选择标准行业领域">
              <el-option v-for="item in bxc_industry_category" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="行业分类" prop="industryClassification">
            <el-input v-model.trim="form.industryClassification" maxlength="30" placeholder="请输入标准行业分类" />
          </el-form-item>
        </template>
        <template v-if="props.standardType == 2">
          <el-form-item label="发布地区" prop="address">
            <el-input v-model.trim="form.address" maxlength="100" placeholder="请输入标准发布地区" />
          </el-form-item>
        </template>
        <el-form-item label="备案号" prop="filingsNumber">
          <el-input v-model.trim="form.filingsNumber" maxlength="50" placeholder="请输入标准备案号" />
        </el-form-item>
        <el-form-item label="备案日期" prop="filingsDate">
          <el-date-picker
            clearable
            v-model="form.filingsDate"
            type="date"
            placeholder="请选择标准备案日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </template>
      <template v-if="props.standardType == 3">
        <el-form-item label="团体名称" prop="associationName">
          <el-input v-model.trim="form.associationName" maxlength="100" placeholder="请输入标准团体名称" />
        </el-form-item>
        <el-form-item label="是否包含专利" prop="isPatentInfo">
          <el-select clearable placeholder="请选择标准是否包含专利" v-model="form.isPatentInfo">
            <el-option v-for="item in yes_no" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <template v-if="props.standardType == 4 || props.standardType == 8 || props.standardType == 9">
        <el-form-item label="发布单位" prop="applyUnit">
          <el-input v-model.trim="form.applyUnit" maxlength="100" placeholder="请输入标准发布单位名称" />
        </el-form-item>
      </template>
      <el-form-item label="标准文件" prop="standardTextFilesList" class="one-column">
        <div>
          <ele-upload-image
            v-model:value="form.standardTextFilesList"
            :fileSize="200"
            :multiple="false"
            :fileType="['pdf']"
            :responseFn="handleResponse"
            :uploadUrl="'/system/oss/uploadOcr'"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：pdf；文件大小不超过200MB
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="quoteStandardCode" class="one-column">
        <template #label>
          <span>引用标准</span>
          <el-tooltip content="当有多个引用标准信息时，使用中文顿号(、)分割" placement="top-start">
            <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input
          v-model="form.quoteStandardCode"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入引用标准信息"
        />
      </el-form-item>
      <template v-if="[0, 1, 2, 3, 4, 8, 9].includes(Number(props.standardType))">
        <el-form-item prop="draftUnits" class="one-column">
          <template #label>
            <span>起草单位</span>
            <el-tooltip content="当有多个起草单位信息时，使用中文顿号(、)分割" placement="top-start">
              <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
            </el-tooltip>
          </template>
          <el-input
            v-model="form.draftUnits"
            :rows="5"
            :show-word-limit="true"
            maxlength="1000"
            type="textarea"
            placeholder="请输入标准起草单位"
          />
        </el-form-item>
        <el-form-item prop="drafters" class="one-column">
          <template #label>
            <span>起草人</span>
            <el-tooltip content="当有多个起草人信息时，使用中文顿号(、)分割" placement="top-start">
              <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
            </el-tooltip>
          </template>
          <el-input
            v-model="form.drafters"
            :rows="5"
            :show-word-limit="true"
            maxlength="1000"
            type="textarea"
            placeholder="请输入标准起草人"
          />
        </el-form-item>
      </template>
      <template v-if="[0, 1, 2, 3, 4].includes(Number(props.standardType))">
        <el-form-item v-if="form.isArticleRepeal == 1" label="条文废止说明" prop="articleRepealContent" class="one-column">
          <el-input
            v-model="form.articleRepealContent"
            :rows="5"
            :show-word-limit="true"
            maxlength="2000"
            type="textarea"
            placeholder="请输入标准条文废止说明"
          />
        </el-form-item>
      </template>
      <el-form-item prop="adoptStandardCode" class="one-column">
        <template #label>
          <span>采用标准</span>
          <el-tooltip content="当有多个采用标准信息时，使用中文顿号(、)分割" placement="top-start">
            <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input
          v-model="form.adoptStandardCode"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入采用标准信息"
        />
      </el-form-item>
      <el-form-item prop="beQuoteStandardCode" class="one-column">
        <template #label>
          <span>被引用状况</span>
          <el-tooltip content="当有多个被引用状况信息时，使用中文顿号(、)分割" placement="top-start">
            <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input
          v-model="form.beQuoteStandardCode"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入被引用状况"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getStandardType, getQueryStandardDetail } from '@/api/standard_manage/standard_query';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();
  const { yes_no, bxc_standard_attr, bxc_standard_amend, bxc_standard_status, bxc_standard_category, bxc_industry_category } =
    proxy.useDict(
      'yes_no',
      'bxc_standard_attr',
      'bxc_standard_amend',
      'bxc_standard_status',
      'bxc_standard_category',
      'bxc_industry_category'
    );

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    // 标准类型
    standardType: {
      type: [String, Number],
      default: '0',
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  });

  const loading = ref(false);
  const CCSOptions = ref([]);
  const ICSOptions = ref([]);
  const form = ref({});
  const rules = reactive({
    standardCode: [{ required: true, message: '请输入标准号', trigger: 'blur' }],
    standardName: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    associationName: [{ required: true, message: '请输入团体名称', trigger: 'blur' }],
    standardStatus: [{ required: true, message: '请选择标准状态', trigger: 'change' }],
    amend: [{ required: true, message: '请选择制修订', trigger: 'change' }],
    standardAttr: [{ required: true, message: '请选择标准性质', trigger: 'change' }],
  });

  getStandardType({ pageSize: 0, type: 0 }).then(res => {
    CCSOptions.value = res.data;
  });

  getStandardType({ pageSize: 0, type: 1 }).then(res => {
    ICSOptions.value = res.data;
  });

  const handleSearch = () => {
    getQueryStandardDetail({ standardCode: form.value.standardCode, standardType: props.standardType }).then(res => {
      form.value = { ...form.value, ...res.data };
    });
  };

  const handleResponse = (response, file, fileList) => {
    form.value.standardTextPages = response.data.pages;
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        if (!props.isEdit) form.value.standardType = props.standardType;
        emit('updateData', form.value);
        handleClose();
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
    emit('update:standardType', undefined);
  };

  const setComponentsForm = data => {
    form.value = data;
  };

  const emit = defineEmits(['update:visible', 'update:standardType', 'updateData']);

  defineExpose({
    setComponentsForm,
  });
</script>

<style lang="scss" scoped></style>
