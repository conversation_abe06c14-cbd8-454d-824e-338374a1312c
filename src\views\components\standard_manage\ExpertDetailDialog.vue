<template>
  <div>
    <el-dialog v-model="dialogVisible" width="60%" title="专家详情" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="notice-content">
        <el-descriptions
          class="margin-top mt20"
          :column="2"
          border
        >
          <el-descriptions-item>
            <template #label>
              <div>
                姓名
              </div>
            </template>
            {{ form.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                性别
              </div>
            </template>
            {{ form.sexName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                联系方式
              </div>
            </template>
            {{ form.contactNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                是否为外聘
              </div>
            </template>
            {{ form.isExternalExpertName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                工作单位
              </div>
            </template>
            {{ form.workUnit || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                职务
              </div>
            </template>
            {{ form.job || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                企业地址
              </div>
            </template>
            {{ form.enterpriseAddress || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                所在区域
              </div>
            </template>
            {{ form.regionName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                委员会名称
              </div>
            </template>
            {{ form.committeeName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                委员会职务
              </div>
            </template>
            {{ form.committeeJob || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                学历
              </div>
            </template>
            {{ form.educationName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div>
                出生年月
              </div>
            </template>
            {{ form.birthDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template #label>
              <div>
                擅长描述
              </div>
            </template>
            {{ form.proficientDesc || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancel">关 闭</el-button>
        </div>
      </template>
      
    </el-dialog>
  </div>
  </template>
  
  <script setup>
  import EleUploadImage from "@/components/EleUploadImage"
  import {getStcExpertDetail} from '@/api/standard_manage/standard_expert.js'


  const {proxy} = getCurrentInstance()
  
  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number]
  })
  const {dialogVisible,id} = toRefs(props)
  
  const state = reactive({
    loading: false,
    form: {},
    dataList:[]
  })
  
  const {title,loading,form,dataList} = toRefs(state)

  onMounted(() => {
    if (id.value) getDetail();
  });
  const getDetail = () => {
    getStcExpertDetail(id.value).then((res) => {
      let data = res.data;
      state.form = data;
      state.dataList = data.standardList;
    });
  };

  
  const emit = defineEmits(['update:dialogVisible','success'])
  const close = () => {
    emit('update:dialogVisible',false)
  }
  const cancel = () => {
    emit('update:dialogVisible',false)
  }
  </script>
  
  <style lang="scss" scoped>
  
  .ele-upload-image{
    display: block !important;
  }

  </style>