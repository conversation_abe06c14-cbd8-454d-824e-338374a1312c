import request from '@/utils/request'

//标准同步

// 标准同步列表
export const getStandardSyncList = (params) => {
  return request({
    url: '/process/standardOperationLog/list',
    method: 'get',
    params
  })
}
// 获取已操作导入更新人员
export const getOpPersonList = (params) => {
  return request({
    url: '/process/standardOperationLog/getOperatingPersonnels',
    method: 'get',
    params
  })
}
// 导入更新数据
export const radarImportStandardSync = () => {
  return request({
    url: '/process/standardOperationLog/automaticData',
    method: 'get',
    timeout: 300000 // 5分钟
  })
}