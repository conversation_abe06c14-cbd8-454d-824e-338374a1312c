<template>
  <div class="app-container flex">
    <div class="info mr20">
      <div class="flex flex-jc-end">
        <el-button
          v-if="userName != 'admin'"
          plain
          icon="edit"
          @click="edit"
          v-hasPermi="['personal:edit']"
          >编辑</el-button
        >
        <el-button
          plain
          icon="Unlock"
          @click="editPassword"
          v-hasPermi="['personal:editPassword']"
          >修改密码</el-button
        >
      </div>
      <div class="mt40">
        <div class="text-center">
          <user-avatar :user="state.user" />
          <div class="name mt15 fw-b f-18 flex flex-center">
            {{ user.nickName }}
            <img class="sex-img" v-if="user.sex == 1" src="@/assets/images/personal/my-miss.png" alt="">
            <img class="sex-img" v-if="user.sex == 0" src="@/assets/images/personal/my-mr.png" alt="">
          </div>
        </div>
        <div class="base-info mt40 f-16">
          <div class="flex flex-ai-center mb20">
            <img src="@/assets/images/personal/my-job.png" alt="">
            <div class="c-99">职位：</div>
            <div class="flex-1 c-33 overflow-ellipsis">{{ form.postGroup || '-' }}</div>
          </div>
          <div class="flex flex-ai-center mb20">
            <img src="@/assets/images/personal/my-department.png" alt="">
            <div class="c-99">所属部门：</div>
            <div class="flex-1 c-33 overflow-ellipsis" v-if="user.dept">{{ user.dept.deptName || '-' }}</div>
          </div>
          <div class="flex flex-ai-center mb20">
            <img src="@/assets/images/personal/my-phone.png" alt="">
            <div class="c-99">手机号码：</div>
            <div class="flex-1 c-33 overflow-ellipsis">{{ user.phonenumber || '-' }}</div>
          </div>
          <div class="flex flex-ai-center mb20">
            <img src="@/assets/images/personal/my-email.png" alt="">
            <div class="c-99">邮箱地址：</div>
            <div class="flex-1 c-33 overflow-ellipsis">{{ user.email || '-' }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="summary">
      <el-tabs v-model="activeTab" @tab-click="handleClick">
        <el-tab-pane label="我的反馈" name="feedback">
          <feedback v-if="tabFresh.feedback"/>
        </el-tab-pane>
        <el-tab-pane label="我的收藏" name="collect">
          <collect v-if="tabFresh.collect" />
        </el-tab-pane>
        <el-tab-pane label="我的下载" name="download">
          <download v-if="tabFresh.download" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <info-edit-dialog v-if="editDialog" v-model:dialogVisible="editDialog" @getUser="getUser" />

    <pass-word-edit-dialog v-if="passwordEditDialog" v-model:dialogVisible="passwordEditDialog" />
  </div>
</template>


<script setup>
import UserAvatar from '@/views/components/personal/personal_center/UserAvatar'
import Download from '@/views/components/personal/personal_center/Download'
import Collect from '@/views/components/personal/personal_center/Collect'
import Feedback from '@/views/components/personal/personal_center/Feedback'
import InfoEditDialog from '@/views/components/personal/personal_center/InfoEditDialog'
import PassWordEditDialog from '@/views/components/personal/personal_center/PasswordEditDialog'
import { getUserProfile } from '@/api/system/user'
import useUserStore from '@/store/modules/user'


import { toRefs } from 'vue'



const userStore = useUserStore()
const activeTab = ref("feedback");

const state = reactive({
  editDialog:false,
  passwordEditDialog:false,
  form: {},
  user:{},
  tabFresh:{
    feedback:true,
    collect:false,
    download:false,
    renew:false
  },
  userName:userStore.name
  
});

const {editDialog,form,user,tabFresh,passwordEditDialog,userName} = toRefs(state)

const handleClick = (tab) => {
  Object.keys(tabFresh.value).forEach((key) => {
    if(tab.paneName == key){
      state.tabFresh[key] = true;
    }else{
      state.tabFresh[key] = false;
    }
  })
  
}

const edit = () => {
  state.editDialog = true;
}

const editPassword = () => {
  state.passwordEditDialog = true;
}

function getUser() {
  getUserProfile().then(res => {
    state.user = res.data.user;
    state.form = res.data;
  })
};

getUser();
</script>

<style lang="scss" scoped>
.app-container{
  .info{
    box-sizing: border-box;
    flex: 0 0 24%;
    max-width:24%;
    // height: calc(100vh - 130px);
    padding: 30px;
    background-color: #fff;
    border-radius: 15px;
    .base-info{
      img{
        height: 26px;
        margin-right: 15px;
      }
    }
  }
  .summary{
    box-sizing: border-box;
    flex: 0 0 75%;
    max-width:75%;
    // height: calc(100vh - 130px);
    padding: 25px 30px;
    background-color: #fff;
    border-radius: 15px;  
  }
}
.sex-img{
  height: 20px;
  margin-left: 10px;
}

:deep(.info .el-button){
  color: #2F5AFF !important;
  border-color: #2F5AFF !important;
  border-radius: 5px;
}
</style>