<template>
  <el-dialog
    width="1000px"
    v-model="visible"
    append-to-body
    title="批量入库结果"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="standard-title">
      <span class="standard-title-circle"></span>
      <span class="c-primary">
        本次批量导入标准数据
        <span class="c-FFB400">{{ total }}</span>
        条，成功入库标准数据
        <span class="c-FFB400">{{ successTotal }}</span>
        条，未成功入库标准数据
        <span class="c-FFB400">{{ failureTotal }}</span>
        条
      </span>
      <div v-if="errorMsg.length > 0" class="m-red">提示：{{ errorMsg }}</div>
    </div>
    <div class="f-14 flex flex-sb flex-ai-center mt10 plr10">
      <span class="c-33 f-bold">导入失败数据表</span>
      <span @click="handleDownload" class="c-primary pointer">下载导入失败数据</span>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
      class="mt10 mb20"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column type="index" :align="'center'" label="序号" fixed="left" width="60">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="standardCode" label="标准号" fixed="left" min-width="180" show-overflow-tooltip />
      <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
      <el-table-column v-if="switchValue == 0" prop="standardTypeName" label="标准类型" show-overflow-tooltip />
      <el-table-column v-if="switchValue == 0" prop="standardStatusName" label="标准状态" show-overflow-tooltip />
      <el-table-column label="失败说明" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="c-FF0000">{{ row.failureReason }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="failureTotal > 0" class="mb10 flex flex-jc-end">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        :total="failureTotal"
      />
    </div>
  </el-dialog>
</template>

<script setup>
  import { json2excel } from '@/utils/formatExcel.js';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    dataList: {
      type: Array,
      default: [],
    },
    total: {
      type: Number,
      default: 0,
    },
    successTotal: {
      type: Number,
      default: 0,
    },
    failureTotal: {
      type: Number,
      default: 0,
    },
    switchValue: {
      type: [String, Number],
      default: '0',
    },
    errorMsg: {
      type: String,
      default: '',
    },
  });

  const { visible, dataList, total, successTotal, failureTotal, switchValue, errorMsg } = toRefs(props);

  const data = reactive({
    loading: false,
    // 默认显示第几页
    currentPage: 1,
    // 总条数，根据接口获取数据长度(注意：这里不能为空)
    totalCount: props.total,
    // 个数选择器（可修改）
    pageSizes: [10, 20, 30, 40],
    // 默认每页显示的条数（可修改）
    pageSize: 10,
  });
  const { loading, currentPage, totalCount, pageSizes, pageSize } = toRefs(data);

  const emit = defineEmits(['update:visible', 'success']);

  const handleSizeChange = val => {
    // 改变每页显示的条数
    data.pageSize = val;
    // 注意：在改变每页显示的条数时，要将页码显示到第一页
    data.currentPage = 1;
  };

  const handleCurrentChange = val => {
    data.currentPage = val;
  };

  const handleDownload = () => {
    if (switchValue.value == 0) {
      let GData = [];
      let HData = [];
      let DData = [];
      let TData = [];
      let QData = [];
      let InternationalData = [];
      let ForeignData = [];
      dataList.value.forEach(item => {
        switch (item.standardType) {
          case '0':
            GData.push(item);
            break;
          case '1':
            HData.push(item);
            break;
          case '2':
            DData.push(item);
            break;
          case '3':
            TData.push(item);
            break;
          case '4':
            QData.push(item);
            break;
          case '5':
            InternationalData.push(item);
            break;
          case '6':
            ForeignData.push(item);
            break;
          default:
            break;
        }
      });
      let excelDatas = [
        {
          tHeader: [
            '标准号',
            '标准名称',
            '英文名称',
            '标准状态',
            '标准性质',
            '发布日期',
            '实施日期',
            '废止日期',
            '替代标准号',
            '制修订',
            '被替代标准号',
            '被替代标准名称',
            '标准类别',
            'CCS分类号',
            'ICS分类号',
            '归口单位',
            '执行单位',
            '主管部门',
            '是否条文废止',
            '条文废止说明',
            '公告号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '起草单位',
            '起草人',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'standardAttrName',
            'publishDate',
            'executeDate',
            'repealDate',
            'allReplaceStandardCode',
            'amendName',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'standardCategoryName',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'registryUnit',
            'applyUnit',
            'manageDept',
            'isArticleRepealName',
            'articleRepealContent',
            'noticeCode',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'draftUnits',
            'drafters',
            'failureReason',
          ],
          tableDatas: GData,
          sheetName: '国家标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '英文名称',
            '标准状态',
            '标准性质',
            '发布日期',
            '实施日期',
            '废止日期',
            '替代标准号',
            '制修订',
            '被替代标准号',
            '被替代标准名称',
            '备案号',
            '备案日期',
            'CCS分类号',
            'ICS分类号',
            '归口单位',
            '批准发布部门',
            '标准类别',
            '行业领域',
            '行业分类',
            '是否条文废止',
            '条文废止说明',
            '公告号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '主要起草单位',
            '主要起草人',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'standardAttrName',
            'publishDate',
            'executeDate',
            'repealDate',
            'allReplaceStandardCode',
            'amendName',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'filingsNumber',
            'filingsDate',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'technologyRegistry',
            'confirmPublishDept',
            'standardCategoryName',
            'industryCategory',
            'industryClassification',
            'isArticleRepealName',
            'articleRepealContent',
            'noticeCode',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'draftUnits',
            'drafters',
            'failureReason',
          ],
          tableDatas: HData,
          sheetName: '行业标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '英文名称',
            '标准状态',
            '标准性质',
            '发布日期',
            '实施日期',
            '废止日期',
            '标准类别',
            '替代标准号',
            '制修订',
            '被替代标准号',
            '被替代标准名称',
            '备案号',
            '备案日期',
            'CCS分类号',
            'ICS分类号',
            '归口单位',
            '批准发布部门',
            '发布地区',
            '是否条文废止',
            '条文废止说明',
            '公告号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '主要起草单位',
            '主要起草人',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'standardAttrName',
            'publishDate',
            'executeDate',
            'repealDate',
            'standardCategoryName',
            'allReplaceStandardCode',
            'amendName',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'filingsNumber',
            'filingsDate',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'technologyRegistry',
            'confirmPublishDept',
            'address',
            'isArticleRepealName',
            'articleRepealContent',
            'noticeCode',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'draftUnits',
            'drafters',
            'failureReason',
          ],
          tableDatas: DData,
          sheetName: '地方标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '英文名称',
            '标准状态',
            '标准性质',
            '发布日期',
            '实施日期',
            '废止日期',
            '标准类别',
            '替代标准号',
            '制修订',
            '被替代标准号',
            '被替代标准名称',
            '团体名称',
            '是否包含专利',
            'CCS分类号',
            'ICS分类号',
            '归口单位',
            '是否条文废止',
            '条文废止说明',
            '公告号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '主要起草单位',
            '主要起草人',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatus',
            'standardAttrName',
            'publishDate',
            'executeDate',
            'repealDate',
            'standardCategoryName',
            'allReplaceStandardCode',
            'amendName',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'associationName',
            'isPatentInfoName',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'registryUnit',
            'isArticleRepealName',
            'articleRepealContent',
            'noticeCode',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'draftUnits',
            'drafters',
            'failureReason',
          ],
          tableDatas: TData,
          sheetName: '团体标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '英文名称',
            '标准状态',
            '标准性质',
            '发布日期',
            '实施日期',
            'CCS分类号',
            'ICS分类号',
            '废止日期',
            '标准类别',
            '替代标准号',
            '制修订',
            '被替代标准号',
            '被替代标准名称',
            '归口单位',
            '发布单位',
            '是否条文废止',
            '条文废止说明',
            '公告号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '主要起草单位',
            '主要起草人',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'standardAttrName',
            'publishDate',
            'executeDate',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'repealDate',
            'standardCategoryName',
            'allReplaceStandardCode',
            'amendName',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'registryUnit',
            'publishingUnit',
            'isArticleRepealName',
            'articleRepealContent',
            'noticeCode',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'draftUnits',
            'drafters',
            'failureReason',
          ],
          tableDatas: QData,
          sheetName: '企业标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '中文名称',
            '标准状态',
            '发布日期',
            '实施日期',
            '废止日期',
            '替代标准号',
            '被替代标准号',
            '被替代标准名称',
            '发布组织',
            '标准语言',
            'CCS分类号',
            'ICS分类号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'publishDate',
            'executeDate',
            'repealDate',
            'allReplaceStandardCode',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'publishOrg',
            'standardLanguage',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'failureReason',
          ],
          tableDatas: InternationalData,
          sheetName: '国际标准',
        },
        {
          tHeader: [
            '标准号',
            '标准名称',
            '中文名称',
            '标准状态',
            '发布日期',
            '实施日期',
            '废止日期',
            '替代标准号',
            '被替代标准号',
            '被替代标准名称',
            '发布组织',
            '标准语言',
            'CCS分类号',
            'ICS分类号',
            '采用标准',
            '被引用状况',
            '引用标准',
            '失败说明',
          ],
          filterVal: [
            'standardCode',
            'standardName',
            'standardNameEn',
            'standardStatusName',
            'publishDate',
            'executeDate',
            'repealDate',
            'allReplaceStandardCode',
            'beReplacedStandardCode',
            'beReplacedStandardName',
            'publishOrg',
            'standardLanguage',
            'standardTypeCodeGbName',
            'standardTypeCodeIsoName',
            'adoptStandardCode',
            'beQuoteStandardCode',
            'quoteStandardCode',
            'failureReason',
          ],
          tableDatas: ForeignData,
          sheetName: '国外标准',
        },
      ];
      let multiHeader = [
        [
          '导入说明：\n1、所有列中，标注红色字体的表示该项信息为必须信息；\n2、【标准状态】项，选项值为：即将实施、现行有效、废止、被替代；\n3、【标准性质】项，选项值为：强制性、推荐性、指导性；\n4、【发布日期】【实施日期】项，日期格式值为：年-月-日，示例：2023-05-01；\n5、【全部替代标准号】填写替代标准的标准号，有多个时，以英文逗号分割；\n请勿删除“导入说明”数据行',
        ],
      ];
      json2excel(excelDatas, multiHeader, '入库失败数据', true, 'xlsx');
    } else {
      let excelDatas = [
        {
          tHeader: ['标准号', '标准名称', '失败说明'],
          filterVal: ['standardCode', 'standardName', 'failureReason'],
          tableDatas: dataList.value,
          sheetName: 'Sheet1',
        },
      ];
      let multiHeader = [['导入说明：所有列中的【标准号】为必须信息；']];
      json2excel(excelDatas, multiHeader, '入库失败数据', true, 'xlsx');
    }
  };

  const handleClose = () => {
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .standard {
    &-title {
      font-size: 14px;
      line-height: 40px;
      background: #edf1ff;
      border-radius: 5px;
      padding: 0 15px;

      &-circle {
        width: 7px;
        height: 7px;
        background: #2f5aff;
        border-radius: 50%;
        margin-right: 7px;
        display: inline-block;
      }
    }
  }

  .plr10 {
    padding: 0 10px;
  }
</style>
