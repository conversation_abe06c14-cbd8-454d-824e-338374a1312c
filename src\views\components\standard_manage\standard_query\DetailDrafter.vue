<template>
  <div class="plr25">
    <div class="flex flex-sb flex-ai-center">
      <div class="h-title">起草人</div>
      <div v-if="form.draftersList && form.draftersList.length > 20" class="flex flex-column flex-sa flex-ai-center pointer">
        <span @click="handClick" class="iconfont f-8" :class="portion ? 'icon-xiala' : 'icon-shangla'"></span>
      </div>
    </div>
    <div v-if="form.draftersList && form.draftersList.length > 0" class="drafter">
      <div v-for="item in list" :key="item.id" class="drafter-label">
        <!-- <img :src="drafterImage" class="drafter-label-icon" alt="" /> -->
        <span class="iconfont icon-renyuan f-20 c-primary"></span>
        <div class="tool-tip ml10" v-showToolTip>
          <el-tooltip placement="top" :content="item">
            <div class="tool-tip-ellipsis">{{ item }}</div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div v-else class="normal-emtpy">暂无信息</div>
  </div>
</template>

<script setup>
  // const drafterImage = new URL('@/assets/images/db/drafter.png', import.meta.url).href;

  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const portion = ref(true);

  let list = computed(() => {
    return portion.value ? form.value.draftersList.slice(0, 20) : form.value.draftersList;
  });

  const handClick = () => {
    portion.value = !portion.value;
  };
</script>

<style lang="scss" scoped>
  .drafter {
    display: flex;
    flex-wrap: wrap;

    &-label {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #333333;
      padding-right: 10px;
      width: 10%;
      box-sizing: border-box;
      border-radius: 5px;
      margin-top: 20px;

      &-icon {
        width: 30px;
        height: 30px;
      }
    }
  }
</style>
