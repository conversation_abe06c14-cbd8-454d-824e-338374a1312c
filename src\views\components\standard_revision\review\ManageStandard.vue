<template>
  <el-button
    v-if="form.phase == 3 && isAuth(form.authorizedUserIdList) && conclusionTableData.length == 0 && !props.isDetail"
    @click="handleConclusionFile"
    type="primary"
    class="mt30"
  >
    <i class="iconfont icon-guanli mr6"></i>
    上传结论附件
  </el-button>
  <el-button
    v-if="
      form.phase == 3 &&
      isAuth(form.completeProjectUserIdList) &&
      conclusionTableData &&
      conclusionTableData.length > 0 &&
      !props.isDetail
    "
    @click="handleFinish"
    type="primary"
    class="mt30"
  >
    <i class="iconfont icon-guanli mr6"></i>
    完结项目
  </el-button>
  <template v-if="conclusionTableData && conclusionTableData.length > 0">
    <div class="flex flex-sb flex-ai-center mt30">
      <div class="h-title">复审结论</div>
      <el-button
        v-if="
          form.phase == 3 &&
          isAuth(form.authorizedUserIdList) &&
          conclusionTableData &&
          conclusionTableData.length < 9 &&
          !props.isDetail
        "
        @click="handleConclusionFile"
      >
        <i class="iconfont icon-shangchuan f-14 mr6"></i>
        上传
      </el-button>
    </div>
    <el-table :data="conclusionTableData" :border="true" class="mt15">
      <el-table-column label="序号" type="index" fixed width="55" />
      <el-table-column label="复审结论" show-overflow-tooltip min-width="200" fixed>
        <template #default="{ row }">
          <el-button
            v-if="row.reviewConclusionFileList && row.reviewConclusionFileList.length > 0"
            @click="handleImg(row.reviewConclusionFileList[0])"
            link
            type="primary"
          >
            {{ row.reviewConclusionFileList[0].name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传时间" show-overflow-tooltip min-width="200" />
      <el-table-column prop="createBy" label="上传人" show-overflow-tooltip min-width="150" />
      <el-table-column v-if="form.phase == 3" label="操作" fixed="right" min-width="120">
        <template #default="{ row }">
          <el-button
            v-if="row.reviewConclusionFileList && row.reviewConclusionFileList.length > 0"
            @click="handleDownload(row.reviewConclusionFileList[0])"
            link
            type="primary"
          >
            下载
          </el-button>
          <el-button
            v-if="conclusionTableData && conclusionTableData.length > 1 && isAuth(form.authorizedUserIdList) && !props.isDetail"
            @click="handledelete(row)"
            link
            type="danger"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </template>
  <div class="flex flex-sb flex-ai-center mt30">
    <div class="h-title">复审标准</div>
    <div class="flex flex-ai-center">
      <div class="flex">
        <el-input v-model="standardParams.keyword" placeholder="请输入标准号或标准名称">
          <template #append>
            <el-button @click="handleQuery" icon="Search" class="img-icon" />
          </template>
        </el-input>
        <el-button plain @click="handleClear" icon="Refresh" class="f-18 ml10"></el-button>
      </div>
      <el-button
        v-if="form.phase == 3 && isAuth(form.authorizedUserIdList) && conclusionTableData.length == 0"
        @click="handleExport"
        class="ml10"
      >
        <i class="iconfont icon-daochu f-14 mr6"></i>
        导出
      </el-button>
    </div>
  </div>
  <el-table :data="standardTableData" :border="true" class="mt15">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column label="标准号" show-overflow-tooltip fixed min-width="200">
      <template #default="{ row }">
        <el-button @click="handleRowClick(row)" link type="primary">{{ row.standardCode }}</el-button>
      </template>
    </el-table-column>
    <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
    <el-table-column label="标准文件" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <suffix-icon
          v-if="row.standardTextFilesList && row.standardTextFilesList.length > 0"
          @click="handlePreview(row.standardTextFilesList[0])"
          class="mr5 f-18 pointer"
          :fileName="'.pdf'"
          :fileType="1"
        ></suffix-icon>
      </template>
    </el-table-column>
    <el-table-column label="标准状态" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="reviewBasisName" label="复审依据" show-overflow-tooltip min-width="200" />
    <el-table-column label="复审结论" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <span :class="getConclusionStatusColor(row.reviewConclusion)">{{ row.reviewConclusionName }}</span>
      </template>
    </el-table-column>
    <el-table-column
      v-if="form.phase == 3 && conclusionTableData.length == 0 && !props.isDetail"
      label="操作"
      fixed="right"
      min-width="120"
    >
      <template #default="{ row }">
        <el-button v-if="!row.reviewConclusionName" @click="handleConclusion(row)" link type="primary">结论</el-button>
        <el-button v-else @click="handleEditConclusion(row)" link type="primary">修改</el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="standardTotal > 0"
    :total="standardTotal"
    v-model:page="standardParams.pageNum"
    v-model:limit="standardParams.pageSize"
    @pagination="getStandardData"
  />
  <div class="h-title mt20">
    征集意见
    <span>&nbsp;&nbsp;{{ selectRow.standardCode }}</span>
  </div>
  <el-table :data="opinionTableData" :border="true" class="mt15">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column prop="opinionTypeName" label="意见类型" show-overflow-tooltip fixed min-width="150" />
    <el-table-column prop="opinionContent" label="意见" show-overflow-tooltip min-width="200" />
    <el-table-column label="附件" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <el-button
          v-if="row.feedbackFileList && row.feedbackFileList.length > 0"
          @click="handleImg(row.feedbackFileList[0])"
          link
          type="primary"
          style="text-decoration: inherit"
        >
          {{ row.feedbackFileList[0].name }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column prop="createBy" label="提交人" show-overflow-tooltip min-width="150" />
    <el-table-column prop="feedbackDept" label="所属部门" show-overflow-tooltip min-width="150" />
    <el-table-column label="处理结果" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <span :class="getResultStatusColor(row.processResult)">{{ row.processResultName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="processUser" label="处理人" show-overflow-tooltip min-width="150" />
    <el-table-column prop="processTime" label="处理时间" show-overflow-tooltip min-width="200" />
    <el-table-column prop="processContent" label="说明" show-overflow-tooltip min-width="200" />
    <el-table-column prop="createTime" label="提交时间" show-overflow-tooltip min-width="200" />
    <el-table-column v-if="form.phase == 3" label="操作" fixed="right" min-width="150">
      <template #default="{ row }">
        <el-button
          v-if="row.feedbackFileList && row.feedbackFileList.length > 0"
          @click="handleDownload(row.feedbackFileList[0])"
          link
          type="primary"
        >
          下载附件
        </el-button>
        <el-button
          v-if="conclusionTableData.length == 0 && row.processResult == 0 && !props.isDetail"
          @click="handleFeedback(row)"
          link
          type="primary"
        >
          处理
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="opinionTotal > 0"
    :total="opinionTotal"
    v-model:page="opinionParams.pageNum"
    v-model:limit="opinionParams.pageSize"
    @pagination="getOpinionData"
  />
  <conclusion-file v-if="conclusionVisible" v-model:visible="conclusionVisible" :id="props.id" @success="handleSuccess" />
  <add-conclusion-dialog
    v-if="addConclusionVisible"
    v-model:visible="addConclusionVisible"
    :defaultForm="conclusionFrom"
    @success="getStandardData"
  />
  <feedback-dialog
    v-if="feedbackVisible"
    v-model:visible="feedbackVisible"
    :defaultForm="feedbackForm"
    :standardId="selectRow.standardId"
    @success="getOpinionData"
  />
  <preview-file v-if="fileVisible" v-model:open="fileVisible" :url="fileUrl" />
</template>

<script setup>
  import { base64Encode } from '@/utils/base64';
  import { isAuth } from '@/utils/index';
  import {
    getReviewInfo,
    getStandardList,
    getOpinionLIst,
    getRecheckList,
    delRecheck,
    reviewFinish,
  } from '@/api/standard_revision/review';
  import SuffixIcon from '@/components/SuffixIcon/SuffixIcon';
  import PreviewFile from '@/components/PreviewFile';
  import ConclusionFile from '@/views/components/standard_revision/review/ConclusionFile.vue';
  import AddConclusionDialog from '@/views/components/standard_revision/review/AddConclusionDialog.vue';
  import FeedbackDialog from '@/views/components/standard_revision/review/FeedbackDialog.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [String, Number],
      required: true,
    },
    flowStatus: { type: Number },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const form = ref({});
  const conclusionTableData = ref([]);
  const standardParams = ref({
    pageNum: 1,
    pageSize: 10,
    reviewId: props.id,
  });
  const standardTableData = ref([]);
  const addConclusionVisible = ref(false);
  const standardTotal = ref(0);
  const opinionParams = ref({
    pageNum: 1,
    pageSize: 10,
    joinId: props.id,
    joinType: 1,
  });
  const opinionTableData = ref([]);
  const opinionTotal = ref(0);
  const conclusionFrom = ref({});
  const conclusionVisible = ref(false);
  const selectRow = ref({});
  const fileVisible = ref(false);
  const fileUrl = ref('');
  const feedbackForm = ref({});
  const feedbackVisible = ref(false);

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  const getConclusionStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-green';
        break;
      case 1:
        return 'status-gray';
        break;
      case 2:
        return 'status-blue';
        break;
      default:
        break;
    }
  };

  const getResultStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 2:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  getReviewInfo(props.id).then(res => {
    form.value = res.data;
  });

  const getRecheckdata = () => {
    getRecheckList({ reviewId: props.id }).then(res => {
      conclusionTableData.value = res.data;
    });
  };

  getRecheckdata();

  const handleClear = () => {
    standardParams.value.keyword = '';
    handleQuery();
  };

  const handleQuery = () => {
    standardParams.value.pageNum = 1;
    getStandardData();
  };

  const getStandardData = () => {
    getStandardList(standardParams.value).then(res => {
      standardTableData.value = res.rows;
      standardTotal.value = res.total;
    });
  };

  getStandardData();

  const getOpinionData = () => {
    getOpinionLIst(opinionParams.value).then(res => {
      opinionTableData.value = res.rows;
      opinionTotal.value = res.total;
    });
  };

  getOpinionData();

  const handleConclusionFile = row => {
    conclusionVisible.value = true;
  };

  const handleFinish = () => {
    proxy
      .$confirm('确定完结复审项目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        reviewFinish(props.id).then(() => {
          proxy.$modal.msgSuccess('设置成功！');
          emit('close')
        });
      })
      .catch(() => {});
  };

  const handleRowClick = row => {
    selectRow.value = row;
    opinionParams.value.pageNum = 1;
    opinionParams.value.standardId = row.standardId;
    getOpinionData();
  };

  const handleImg = row => {
    fileUrl.value = row.url;
    fileVisible.value = true;
  };

  const handleExport = row => {
    proxy.download('/process/reviewStandard/export', { reviewId: props.id }, `复审标准_${new Date().getTime()}.xlsx`);
  };

  const handlePreview = row => {
    window.open(`${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(base64Encode(row.url))}`, '_blank');
  };

  const handleConclusion = row => {
    conclusionFrom.value = {
      isEdit: false,
      id: row.id,
      standardCode: row.standardCode,
      standardName: row.standardName,
    };
    addConclusionVisible.value = true;
  };

  const handleEditConclusion = row => {
    conclusionFrom.value = {
      isEdit: true,
      id: row.id,
      standardCode: row.standardCode,
      standardName: row.standardName,
      reviewConclusion: row.reviewConclusion,
    };
    addConclusionVisible.value = true;
  };

  const handleDownload = row => {
    proxy.download('/system/oss/download/' + row.id, {}, row.name);
  };

  const handledelete = row => {
    delRecheck({ ids: [row.id] }).then(() => {
      proxy.$modal.msgSuccess('删除成功！');
      getRecheckdata();
    });
  };

  const handleFeedback = row => {
    feedbackForm.value = { ...row };
    feedbackForm.value.processResult = '1';
    feedbackVisible.value = true;
  };

  const handleSuccess = () => {
    getRecheckdata();
    getReviewInfo(props.id);
  };

  const emit = defineEmits(['close']);
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-input-group__append) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  .img-icon :deep(.el-icon) {
    color: #fff !important;
    font-size: 18px !important;
    position: relative !important;
    top: -2px !important;
  }

  :deep(.el-input-group__append .el-button:focus) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }
</style>
