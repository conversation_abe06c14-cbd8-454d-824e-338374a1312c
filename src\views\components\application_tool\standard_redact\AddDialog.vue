<template>
  <el-dialog
    width="930px"
    title="标准编写"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div v-if="!props.id" class="tabs flex flex-sa flex-ai-center mb20">
      <div
        @click="handleCut(item)"
        v-for="item in tabList"
        :key="item.value"
        class="tabs-item pointer"
        :class="activeIndex == item.value ? 'tabs-active' : ''"
        @close="handleClose"
      >
        <div>{{ item.tag }}</div>
        <div>{{ item.name }}</div>
      </div>
    </div>
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline">
      <el-form-item prop="standardName">
        <template #label>
          <span>标准名称</span>
          <el-tooltip content="若为第几部分或两行显示,使用回车换行" placement="top-start">
            <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input v-model="form.standardName" :rows="3" type="textarea" maxlength="100" placeholder="请输入标准名称" />
      </el-form-item>
      <el-form-item label="标准英文名称" prop="standardNameEn">
        <el-input v-model="form.standardNameEn" :rows="3" type="textarea" maxlength="500" placeholder="请输入标准英文名称" />
      </el-form-item>
      <el-form-item label="制修订" prop="amend" class="half-column">
        <el-select v-model="form.amend" placeholder="请选择标准制修订类型">
          <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.standardType == 0" label="标准性质" prop="standardAttr" class="half-column">
        <el-select @change="handleStandardAttrChange" v-model="form.standardAttr" placeholder="请选择标准性质">
          <el-option
            v-show="item.value != 2"
            v-for="item in bxc_standard_attr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.standardType == 1" label="行业领域" prop="industryCategoryCode" class="half-column">
        <el-select
          @change="handleIndustryCategoryChange"
          filterable
          v-model="form.industryCategoryCode"
          placeholder="请选择标准行业领域"
        >
          <el-option v-for="item in industryCategoryOptions" :key="item.code" :value="item.code" :label="item.name">
            {{ item.code }} - {{ item.name }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.standardType == 2" label="所属地区" prop="addressCode" class="half-column">
        <el-cascader
          ref="addressRef"
          @change="handleAddressChange"
          v-model="form.addressCode"
          :options="regionOptions"
          :props="regionProps"
          :show-all-levels="false"
          placeholder="请选择标准所属地区"
        >
          <template #default="{ node, data }">{{ data.code }} - {{ data.name }}</template>
        </el-cascader>
      </el-form-item>
      <el-form-item v-if="form.standardType == 3" label="所属团体" prop="associationCode" class="half-column">
        <el-select-v2
          @change="handleAssociationChange"
          v-model="form.associationCode"
          :options="associationCodeOptions"
          name="groupName"
          value-key="socialOrganizationCode"
          filterable
          placeholder="请选择/搜索标准所属团体"
          style="width: 100%"
        >
          <template #default="{ item }">{{ item.socialOrganizationCode }} - {{ item.groupName }}</template>
        </el-select-v2>
      </el-form-item>
      <div class="flex flex-ai-center half-column">
        <el-form-item label="标准号" prop="standardCodePrefix" class="one-third">
          <el-input v-model="form.standardCodePrefix" maxlength="10" placeholder="请输入" class="text-center" />
        </el-form-item>
        &nbsp;&nbsp;
        <div class="one-third-line">-</div>
        &nbsp;&nbsp;
        <el-form-item label=" " prop="standardCodeSequenceNumber" :hide-required-asterisk="true" class="one-third hide_required">
          <el-input v-model="form.standardCodeSequenceNumber" maxlength="15" placeholder="请输入顺序号" class="text-center" />
        </el-form-item>
        &nbsp;&nbsp;
        <div class="one-third-line">-</div>
        &nbsp;&nbsp;
        <el-form-item label=" " prop="standardCodeReleaseYear" class="one-third hide_required">
          <el-date-picker
            v-model="form.standardCodeReleaseYear"
            :clearable="false"
            type="year"
            format="YYYY"
            value-format="YYYY"
            placeholder="请选择年"
            class="text-center"
          />
        </el-form-item>
      </div>
      <el-form-item v-if="form.amend == 1" label="修订标准号" prop="revisionStandardCode">
        <el-input v-model="form.revisionStandardCode" maxlength="50" placeholder="请输入修订标准号" />
      </el-form-item>
      <el-form-item prop=" " class="one-column">
        <template #label>
          <div class="flex flex-sb flex-ai-center">
            <el-tooltip content="发布单位最多可添加3个" placement="top-start">
              <div class="flex flex-ai-center">
                <span :style="{ color: 'var(--el-color-danger)' }">*</span>
                <div class="ml4">发布单位</div>
              </div>
            </el-tooltip>
            <div v-if="form.applyUnitList.length < 3" @click="handleAddUnit" class="c-primary pointer f-14">
              <i class="iconfont icon-zengjia f-12"></i>
              添加发布单位
            </div>
          </div>
        </template>
        <div class="card" ref="cardRef">
          <div v-for="(item, index) in form.applyUnitList" :key="item" class="card-item">
            <div class="flex flex-ai-center">
              <i class="iconfont icon-mulu mr10 f18 drag-handle"></i>
              <el-form-item :prop="'applyUnitList.' + index + '.name'" :rules="rules.name" class="one-column">
                <el-input v-model="item.name" maxlength="30" placeholder="请输入标准发布单位名称" class="w-380" />
              </el-form-item>
            </div>
            <i
              v-if="form.applyUnitList.length > 1"
              @click="handleRemoveUnit(index)"
              class="iconfont icon-remove-1-copy status-red pointer"
            ></i>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="CCS分类号" prop="standardTypeCodeGb">
        <el-cascader
          ref="ccsRef"
          @change="handleCCSChange"
          v-model="form.standardTypeCodeGb"
          :options="CCSOptions"
          :show-all-levels="false"
          :props="ccsProps"
          clearable
          filterable
          placeholder="请搜索/选择CCS分类号"
        >
          <template #default="{ node }">
            <span>{{ node.value }} &nbsp; {{ node.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item label="ICS分类号" prop="standardTypeCodeIso">
        <el-cascader
          ref="icsRef"
          @change="handleICSChange"
          v-model="form.standardTypeCodeIso"
          :options="ICSOptions"
          :show-all-levels="false"
          :props="icsProps"
          clearable
          filterable
          placeholder="请搜索/选择ICS分类号"
        >
          <template #default="{ node }">
            <span>{{ node.value }} &nbsp; {{ node.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item label="发布日期" prop="publishDate">
        <el-date-picker
          clearable
          v-model="form.publishDate"
          type="date"
          placeholder="请选择标准发布日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="实施日期" prop="executeDate">
        <el-date-picker
          v-model="form.executeDate"
          type="date"
          placeholder="请选择标准实施日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          clearable
        />
      </el-form-item>
      <el-form-item label="一致性国际文件" prop="consistentInternationalDocument">
        <el-input v-model="form.consistentInternationalDocument" maxlength="100" placeholder="请输入一致性国际文件名称" />
      </el-form-item>
      <el-form-item label="一致性程度" prop="consistencyLevel">
        <el-select clearable v-model="form.consistencyLevel" placeholder="请选择一致性程度">
          <el-option v-for="item in bxc_consistency_level" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="文稿版本" prop="manuscriptVersion">
        <el-select v-model="form.manuscriptVersion" placeholder="请选择文稿版本">
          <el-option v-for="item in bxc_manuscript_version" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getIndustryCategoryList,
    getGroupMemberList,
    addStandardRedact,
    editStandardRedact,
    detailStandardRedact,
  } from '@/api/application_tool/standard_redact';
  import { getDistrictList } from '@/api/common.js';
  import { getStandardType, getQueryStandardDetail } from '@/api/standard_manage/standard_query';
  import Sortable from 'sortablejs';

  const emit = defineEmits(['update:visible', 'updateData']);
  const { proxy } = getCurrentInstance();
  const { bxc_standard_attr, bxc_standard_amend, bxc_industry_category, bxc_consistency_level, bxc_manuscript_version } =
    proxy.useDict(
      'bxc_standard_attr',
      'bxc_standard_amend',
      'bxc_industry_category',
      'bxc_consistency_level',
      'bxc_manuscript_version'
    );

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [String, Number],
    },
  });

  const cardRef = ref(null);
  const loading = ref(false);
  const industryCategoryOptions = ref([]);
  const CCSOptions = ref([]);
  const ccsProps = reactive({ emitPath: false, checkStrictly: true, label: 'name', value: 'code' });
  const ICSOptions = ref([]);
  const icsProps = reactive({ emitPath: false, checkStrictly: true, label: 'nameCn', value: 'code' });
  const regionOptions = ref([]);
  const regionProps = reactive({ emitPath: false, checkStrictly: true, label: 'name', value: 'code', children: 'childRegion' });
  const associationCodeOptions = ref([]);
  const activeIndex = ref(0);
  const tabList = reactive([
    { name: '国家标准', tag: 'GB', value: 0 },
    { name: '行业标准', tag: 'HB', value: 1 },
    { name: '地方标准', tag: 'DB', value: 2 },
    { name: '团体标准', tag: 'TB', value: 3 },
    { name: '企业标准', tag: 'QB', value: 4 },
  ]);
  const form = ref({
    standardType: 0,
    amend: '0',
    standardAttr: '1',
    standardCodePrefix: 'GB/T',
    applyUnitList: [{ name: '国家市场监督管理总局' }, { name: '国家标准化管理委员会' }],
    standardCodeReleaseYear: new Date().getFullYear().toString(),
    manuscriptVersion: '0',
  });
  const rules = ref({
    standardName: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    amend: [{ required: true, message: '请选择制修订', trigger: 'change' }],
    standardAttr: [{ required: true, message: '请选择标准性质', trigger: 'change' }],
    industryCategoryCode: [{ required: true, message: '请选择标准行业领域', trigger: 'change' }],
    addressCode: [{ required: true, message: '请选择标准所属地区', trigger: 'change' }],
    associationCode: [{ required: true, message: '请选择/搜索标准所属团体', trigger: 'change' }],
    standardCodePrefix: [{ required: true, message: '请输入', trigger: 'blur' }],
    standardCodeSequenceNumber: [{ required: true, message: '请输入顺序号', trigger: 'blur' }],
    standardCodeReleaseYear: [{ required: true, message: '请选择年份', trigger: 'change' }],
    name: [{ required: true, message: '请输入标准发布单位名称', trigger: 'blur' }],
    revisionStandardCode: [{ required: true, message: '请输入修订标准号', trigger: 'blur' }],
    manuscriptVersion: [{ required: true, message: '请选择文稿版本', trigger: 'change' }],
  });

  onMounted(() => {
    initSortable();
    getIndustryCategoryList().then(res => {
      industryCategoryOptions.value = res.data;
    });
    getDistrictList().then(res => {
      regionOptions.value = res.data;
    });
    getStandardType({ pageSize: 0, type: 0 }).then(res => {
      CCSOptions.value = res.data;
    });
    getStandardType({ pageSize: 0, type: 1 }).then(res => {
      ICSOptions.value = res.data;
    });
    getGroupMemberList().then(res => {
      associationCodeOptions.value = res.data;
    });
    if (props.id) {
      detailStandardRedact(props.id).then(res => {
        form.value = res.data;
        activeIndex.value = res.data.standardType;
      });
    }
  });

  const initSortable = () => {
    nextTick(() => {
      if (cardRef.value) {
        new Sortable(cardRef.value, {
          sort: true,
          handle: '.drag-handle',
          animation: 300,
          delay: 0,
          onEnd: function (evt) {
            const oldIndex = evt.oldIndex;
            const newIndex = evt.newIndex;
            if (oldIndex !== newIndex) {
              const movedItem = form.value.applyUnitList.splice(oldIndex, 1)[0];
              form.value.applyUnitList.splice(newIndex, 0, movedItem);
              form.value.applyUnitList = [...form.value.applyUnitList];
            }
          },
        });
      }
    });
  };

  const handleCut = item => {
    proxy.$refs.queryRef.resetFields();
    activeIndex.value = item.value;
    form.value.standardType = item.value;
    form.value.standardCodePrefix = '';
    form.value.standardTypeCodeGbName = '';
    form.value.standardTypeCodeIsoName = '';
    form.value.applyUnitList = [{ name: '' }];
    form.value.standardCodeReleaseYear = new Date().getFullYear().toString();
    if (item.value == 0) {
      form.value.amend = '0';
      form.value.standardAttr = '1';
      form.value.standardCodePrefix = 'GB/T';
    }
    if (item.value == 4) {
      form.value.standardCodePrefix = 'Q/';
      form.value.standardCodeReleaseYear = '';
    }
    form.value.applyUnitList =
      item.value == 0 ? [{ name: '国家市场监督管理总局' }, { name: '国家标准化管理委员会' }] : [{ name: '' }];
  };

  const handleStandardAttrChange = val => {
    form.value.standardCodePrefix = val == 0 ? 'GB' : 'GB/T';
  };

  const handleIndustryCategoryChange = val => {
    form.value.standardCodePrefix = val + '/T';
    const selectedObject = industryCategoryOptions.value.find(item => item.code == val);
    form.value.applyUnitList[0].name = selectedObject.approvalDepartment;
  };

  const handleAddressChange = val => {
    form.value.standardCodePrefix = 'DB' + val + '/T';
    nextTick(() => {
      form.value.applyUnitList[0].name = proxy.$refs.addressRef.presentText + '市场监督管理局';
    });
  };

  const handleAssociationChange = val => {
    form.value.standardCodePrefix = 'T/' + val;
    const selectedObject = associationCodeOptions.value.find(item => item.socialOrganizationCode == val);
    form.value.applyUnitList[0].name = selectedObject.groupName;
  };

  const handleCCSChange = val => {
    form.value.standardTypeCodeGbName = proxy.$refs.ccsRef.presentText;
  };

  const handleICSChange = val => {
    form.value.standardTypeCodeIsoName = proxy.$refs.icsRef.presentText;
  };

  const handleAddUnit = () => {
    form.value.applyUnitList.push({ name: '' });
  };

  const handleRemoveUnit = index => {
    form.value.applyUnitList.splice(index, 1);
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        if (props.id) {
          editStandardRedact({ ...form.value, id: props.id })
            .then(() => {
              proxy.$message.success('保存成功！');
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addStandardRedact(form.value)
            .then(() => {
              proxy.$message.success('标准信息创建成功！');
              emit('updateData');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  .tabs {
    width: 100%;
    background-color: #f3f6fd;
    border-radius: 5px;
    padding: 7px 90px;
    box-sizing: border-box;

    &-item {
      font-size: 16px;
      color: #333;
      text-align: center;
      padding: 10px 20px;
      box-sizing: border-box;
    }

    &-active {
      color: #fff;
      font-weight: 600;
      background-color: $primary-color;
      border-radius: 5px;
    }
  }

  .one-third {
    flex: 0 0 15% !important;
    margin-right: 0 !important;

    &-line {
      position: relative;
      top: 12px;
    }
  }

  .card {
    flex: 1;
    background-color: #f8f9fb;
    padding: 20px 20px 10px;
    box-sizing: border-box;

    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;

      &:not(:first-child) {
        margin-top: 5px;
      }
    }
  }

  .w-380 {
    width: 380px !important;
  }

  .icon-mulu,
  .icon-remove-1-copy {
    position: relative;
    top: -5px;
  }

  .ml4 {
    margin-left: 4px;
  }

  .half-column {
    .el-form-item {
      flex: 1 !important;
    }
  }

  .drag-handle {
    cursor: move;
  }

  .sortable-chosen {
    background-color: transparent !important;
  }

  .sortable-ghost {
    background-color: transparent !important;
    opacity: 0.5;
  }

  .hide_required :deep(.el-form-item__label:before) {
    content: '' !important;
  }

  // .text-center :deep(.el-input__inner) {
  //   text-align: center !important;
  // }

  :deep(.text-center .el-input__inner) {
    text-align: center !important;
  }
</style>
