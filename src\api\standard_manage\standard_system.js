import request from '@/utils/request'

//标准体系

// 查询标准体系树
export const getStandardSystemTree = () => {
  return request({
    url: '/process/standardSystem/tree',
    method: 'get'
  })
}
// 新增标准体系
export const addStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem',
    method: 'post',
    data
  })
}
// 重命名标准体系
export const updateStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem',
    method: 'put',
    data
  })
}
// 删除标准体系
export const deleteStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem',
    method: 'delete',
    data
  })
}
// 查询标准体系分页数据
export const getStandardSystemList = (params) => {
  return request({
    url: '/process/standardSystem/getStandardWithSystemPage',
    method: 'get',
    params
  })
}
// 获取标准体系详细信息
export const getStandardSystemDetail = (id) => {
  return request({
    url: '/process/standardSystem/' + id,
    method: 'get'
  })
}
// 移动标准体系
export const moveStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem/moveStandardSystem',
    method: 'put',
    data
  })
}
// 移出 - 根据标准ids从标准体系移出
export const removeStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem/removeOut',
    method: 'delete',
    data
  })
}
// 导入标准
export const importStandardSystem = (data) => {
  return request({
    url: '/process/standardSystem/importStandard',
    method: 'post',
    data
  })
}