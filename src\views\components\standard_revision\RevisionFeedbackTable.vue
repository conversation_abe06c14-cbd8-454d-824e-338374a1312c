<template>
  <div>
    <el-form :model="queryParams" ref="queryRef" :inline="true" @submit.prevent>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="" prop="feedbackStatus">
            <el-select v-model="queryParams.feedbackStatus" placeholder="请选择反馈状态">
              <el-option v-for="item in bxc_opinion_feedback_status" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" prop="opinionType">
            <el-select v-model="queryParams.opinionType" placeholder="请选择反馈意见类别">
              <el-option v-for="item in bxc_opinion_type" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" prop="processResult">
            <el-select v-model="queryParams.processResult" placeholder="请选择处理结果">
              <el-option v-for="item in bxc_opinion_process_result" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="">
            <el-date-picker
              v-model="dateTime"
              :clearable="false"
              type="daterange"
              range-separator="-"
              start-placeholder="反馈日期"
              end-placeholder="反馈日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-table v-loading="loading" :data="tableData" :border="true">
      <el-table-column label="序号" width="60" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="feedbackUser" label="征求人" show-overflow-tooltip min-width="150" fixed />
      <el-table-column prop="opinionTypeName" label="意见类别" show-overflow-tooltip min-width="150" />
      <el-table-column prop="feedbackTime" label="反馈时间" show-overflow-tooltip min-width="180" />
      <el-table-column label="处理结果" show-overflow-tooltip min-width="150">
        <template #default="{ row }">
          <div :class="getStatusColor(row.processResult)">{{ row.processResultName }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="发稿时间" show-overflow-tooltip min-width="150" />
      <el-table-column label="操作" min-width="100" fixed="right">
        <template #default="{ row }">
          <template v-if="row.feedbackStatus == 1">
            <el-button @click.stop="handleTableBtn(row, 'look')" link type="primary">查看</el-button>
            <el-button
              v-if="row.processResult == 0 && props.activeStep == props.activeTab && !props.isDetail"
              @click.stop="handleTableBtn(row, 'handle')"
              type="primary"
              link
            >
              处理
            </el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
    <handle-feedback-dialog
      v-if="handleFeedbackVisible"
      :id="chooseId"
      v-model:visible="handleFeedbackVisible"
      @success="getData"
    />
    <feedback-detail-dialog v-if="feedbackDetailVisible" :id="chooseId" v-model:visible="feedbackDetailVisible" />
  </div>
</template>

<script setup>
  import { solicitOpinionFeedback } from '@/api/standard_revision/manage';
  import HandleFeedbackDialog from '@/views/components/standard_revision/HandleFeedbackDialog.vue';
  import FeedbackDetailDialog from '@/views/components/standard_revision/FeedbackDetailDialog.vue';

  const { proxy } = getCurrentInstance();

  const { bxc_opinion_type, bxc_opinion_process_result, bxc_opinion_feedback_status } = proxy.useDict(
    'bxc_opinion_type',
    'bxc_opinion_process_result',
    'bxc_opinion_feedback_status'
  );

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    activeTab: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const handleFeedbackVisible = ref(false);
  const feedbackDetailVisible = ref(false);
  const loading = ref(false);
  const dateTime = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    feedbackStatus: '1',
    joinId: props.id,
    joinType: 0
  });
  const total = ref(0);
  const tableData = ref([]);
  const chooseId = ref('');

  const getData = data => {
    loading.value = true;
    if (data) queryParams.value.pageNum = 1;
    queryParams.value.feedbackStartTime = dateTime.value.length > 0 ? dateTime.value[0] : '';
    queryParams.value.feedbackEndTime = dateTime.value.length > 0 ? dateTime.value[1] : '';
    solicitOpinionFeedback(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const getStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-yellow';
        break;
      case 1:
        return 'status-green';
        break;
      case 2:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  const handleTableBtn = (row, type) => {
    switch (type) {
      case 'look':
        chooseId.value = row.id;
        feedbackDetailVisible.value = true;
        break;
      case 'handle':
        chooseId.value = row.id;
        handleFeedbackVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleClear = () => {
    dateTime.value = [];
    proxy.$refs.queryRef.resetFields();
    queryParams.value.feedbackStatus = '1';
    getData('pageNum');
  };

  getData();

  defineExpose({
    getData,
  });
</script>

<style lang="scss" scoped>
  .el-form-item__content,
  .el-form-item {
    width: 100% !important;

    :deep(.el-input),
    .el-select,
    .el-cascader {
      width: 100% !important;
    }
  }
</style>
