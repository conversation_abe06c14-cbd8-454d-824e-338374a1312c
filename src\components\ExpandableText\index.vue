<template>
  <div
    class="expandable-text"
    :class="{ expanded: isExpanded }"
    @click="toggleExpand"
  >
    <div v-html="text"></div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import useComparisonStore from '@/store/modules/comparison'

// 组件属性
const props = defineProps({
  text: {
    type: String,
    required: true,
  },
});

// 是否展开
const isExpanded = ref(false);

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};
onMounted(()=>{
  useComparisonStore()._mathjax()
})
</script>

<style lang="scss" scoped>
.expandable-text {
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 限制为一行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}

.expandable-text.expanded {
  -webkit-line-clamp: unset; /* 取消行数限制 */
  white-space: normal; /* 允许换行 */
}
</style>