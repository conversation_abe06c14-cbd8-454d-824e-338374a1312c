<template>
  <div v-if="commonInfo.currentCategory.fileType == 0" class="folder-num">{{otherInfo.folderTotal}}个文件夹，{{otherInfo.fileTotal}}个文档</div>
  <div v-if="list && list.length > 0" :loading="loading" class="doc-list-wrap" v-loading="loading">
    <div v-for="item in list" :key="item.id" class="doc-list-item">
      <div class="item-icon mr10">
        <suffix-icon class="mr5 f-18" :fileName="item.fileName" :fileType="item.fileType"></suffix-icon>
      </div>
      <div @click.prevent="goDetail(item)" class="item-title">{{item.fileName || '-'}}</div>
      <div class="item-date">创建于{{item.createDate || '-'}}</div>
      <div class="item-star">
        <i
          v-if="item.fileType == 1" 
          class="iconfont icon-shoucang1 f-18 pointer"
          :class="item.isFollow == 1 ? 'c-FFC600' : 'c-E5'"
          @click="handleStar(item)"
        ></i>
        <i 
          v-if="item.fileType == 1" 
          class="iconfont icon-xiazai c-primary f-20 ml20 pointer"
          @click="handleDownload(item)"
        ></i>
      </div>
      <div v-if="!commonInfo.isHome" class="item-more">
        <knowledge-op-menu v-hasPermi="['knowledge:main:more']" :item="item" from="mainDocList" @renameSuccess="getData" @deleteSuccess="getData"></knowledge-op-menu>
      </div>
    </div>
  </div>
  <el-empty v-else></el-empty>
</template>

<script setup>
import {getLibraryFiles,followFile} from '@/api/knowledge/library'
import KnowledgeOpMenu from '@/views/components/knowledge/main/KnowledgeOpMenu'
import SuffixIcon from '@/components/SuffixIcon/SuffixIcon'

const {proxy} = getCurrentInstance()
const props = defineProps({
  type: String
})
const commonInfo = inject('commonInfo')

const data = reactive({
  loading: false,
  list: [],
  total: 0,
  otherInfo: {},
})

const {loading,list,total,otherInfo} = toRefs(data)
const emit = defineEmits(['itemClick'])

const goDetail = (item) => {
  emit('itemClick',item)
}

const handleStar = (item) => {
  let params = {
    recordId: item.id,
    recordType: '2',
    isCollect: item.isFollow == '1' ? '0' : '1'
  }
  followFile(params).then(response => {
    proxy.$modal.msgSuccess(item.isFollow == 0 ? "收藏成功" : '取消收藏成功');
    getData()
  }).catch(error => {
    // proxy.$modal.msgError(item.isFollow == 0 ? "收藏失败" : '取消收藏失败');
  });
}
const handleDownload = (item) => {
  let params = {
    recordId: item.id,
    recordType: '1',
  }
  proxy.download("/process/userDownloadInfo/download", params, item.fileName,{timeout: 24*3600*1000});
}

const getData = () => {
  data.loading = true;
  let queryParams = {
    pageNum: 1,
    pageSize: -1,//-1:获取全部
    fileId: commonInfo.fileId,
    fileType: commonInfo.fileId == 0 ? 1 : 2,// 文件类型（0文件夹，1文件，2全部类型）
    libId: commonInfo.libraryId,
    dataType: props.type, // 0:全部,1:我的关注
  }
  getLibraryFiles(queryParams).then((response) => {
    data.list = response.rows
    data.otherInfo = response.other
    data.total = response.total
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}

watch(()=>commonInfo.fileId,(newVal,oldVal) => {
  if(newVal >= 0 ){
    getData()
  }
},{ deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.folder-num{
  padding: 32px 0 8px;
  color: #3E4967;
  font-size: 14px;
  border-bottom: 1px solid #E5E8EF;
}
.doc-list-wrap{
  .doc-list-item{
    height: 40px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #E5E8EF;
    font-size: 14px;
    &:hover{
      background: #E9F0FE;
      cursor: pointer;
      .item-title{
        color: #3377FF;
      }
    }
    .item-icon{
      display: flex;
      align-items: center;
    }
    .item-title{
      color: #3E4967;
      flex: 1;
      margin-right: 30px;
      white-space: nowrap;
      overflow: hidden;//文本超出隐藏
      text-overflow: ellipsis;//文本超出省略号替代
    }
    .item-date{
      margin-left: auto;
      color: #8F9BB3;
      margin-right: 40px;
    }
    .item-star{
      width: 80px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .item-more{
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }
}
</style>