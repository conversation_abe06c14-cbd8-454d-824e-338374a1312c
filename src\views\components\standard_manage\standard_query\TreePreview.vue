<template>
  <div class="pop-container">
    <el-dialog
      :modal-append-to-body="false"
      v-model="props.open"
      width="80%"
      :title="title"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="standard-analysis-wrap">
        <div class="standard-analysis-left scroller-bar-style">
          <el-tree
            v-loading="menuLoading"
            ref="treeRef"
            empty-text="暂无数据"
            node-key="id"
            :data="categoryList"
            :highlight-current="true"
            :auto-expand-parent="true"
            :expand-on-click-node="false"
            :default-expanded-keys="highlightedNodes"
            :props="{ children: 'children', label: 'name' }"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span class="custom-label">
                  <span v-if="node.level == 1" class="iconfont icon-lgcengji f-18 c-primary mr5"></span>
                  <span v-showToolTip="['tree']">
                    <el-tooltip placement="bottom-start" :content="node.label">
                      <span :class="isHighlighted(data) ? 'highlighted' : ''">{{ node.label }}</span>
                    </el-tooltip>
                  </span>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
        <div class="standard-analysis-right">
          <div
            style="width: 100%; height: calc(100vh - 235px); overflow: auto"
            class="scroller-bar-style"
            v-html="data.treeHtml"
          ></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
  import useComparisonStore from '@/store/modules/comparison';
  import { getAnalysisDetail, getHighLightAnalysisIndex } from '@/api/standard_manage/standard_query';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    open: Boolean,
    standardItem: {
      type: Object,
      default: {},
    },
    id: [String, Number],
    content: {
      type: String,
    },
  });

  const data = reactive({
    title: '标准解析',
    menuLoading: false,
    categoryList: [],
    treeHtml: '',
    highlightedNodes: [],
  });

  const { title, menuLoading, categoryList, treeHtml, highlightedNodes } = toRefs(data);

  data.title = '标准解析 - ' + props.standardItem.standardCode + ' | ' + props.standardItem.standardName;

  const isHighlighted = data => {
    return highlightedNodes.value.some(n => n === data.id);
  };

  const getCategoryTree = () => {
    data.menuLoading = true;
    getAnalysisDetail(props.standardItem.standardId)
      .then(response => {
        categoryList.value = response.data.tree || [];
        treeHtml.value = response.data.text || '';
        if (categoryList.value && categoryList.value.length > 0) {
          getHighLightAnalysisIndex({
            standardId: props.standardItem.standardId,
            content: props.content || props.standardItem.content,
          }).then(res => {
            highlightedNodes.value = res.data;
          });
        }
        if (props.id) {
          proxy.$nextTick(() => {
            proxy.$refs.treeRef.setCurrentKey(props.id);
            handleNodeClick({ id: props.id });
          });
        }
        useComparisonStore()._mathjax();
      })
      .finally(() => {
        data.menuLoading = false;
      });
  };

  const handleNodeClick = item => {
    let nId = 't-' + item.id;
    document.getElementById(nId)?.scrollIntoView({
      behavior: 'smooth',
    });
  };

  const emit = defineEmits(['update:open', 'success']);

  const close = () => {
    emit('update:open', false);
  };

  getCategoryTree();
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 0px !important;
    overflow-y: auto !important;
  }
  .standard-analysis-wrap {
    display: flex;
    height: calc(100vh - 200px);

    .standard-analysis-left {
      width: 400px;
      flex-shrink: 0;
      background: #ffffff;
      overflow-y: auto;
      padding: 20px 20px 0px 0px;
      box-sizing: border-box;
      border-right: 2px solid #e8e8e8;
      border-bottom: 1px solid #e8e8e8;

      :deep(.el-tree-node__content) {
        height: 40px !important;

        &:hover {
          background: #e9f0fe;
          border-radius: 5px;
          color: $primary-color;
          font-weight: bold;
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: 600;
          padding-right: 8px;
          height: 40px;
          overflow: hidden;

          &:hover {
            font-weight: bold !important;
          }

          .custom-label {
            flex: 1;
            white-space: nowrap;
            overflow: hidden; //文本超出隐藏
            text-overflow: ellipsis;
          }
        }
      }

      :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
        color: $primary-color;
      }
    }

    .standard-analysis-right {
      padding: 20px;
      flex: 1;
      overflow: hidden;
      height: 100%;
      box-sizing: border-box;
      border-bottom: 1px solid #e8e8e8;
      .empty-wrap {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .highlighted {
    color: #ff0000;
  }

  :deep(.el-tree-node__children .el-tree-node__content .custom-tree-node) {
    font-size: 14px !important;
    font-weight: 400 !important;
  }
</style>
