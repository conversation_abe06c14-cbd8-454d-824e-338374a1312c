<template>
  <el-dialog
    width="400px"
    title="文件名称设置"
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="queryRef" :model="form" :label-position="'top'" :rules="rules" @submit.prevent>
      <el-form-item label="文件名称" prop="updateFileName">
        <el-input v-model.trim="form.updateFileName" maxlength="150" placeholder="请输入文件名称"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { updateFileName } from '@/api/standard_revision/manage';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    defaultForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    updateFileName: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  });

  onMounted(() => {
    form.value = { ...props.defaultForm };
  });

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        updateFileName(form.value)
          .then(res => {
            emit('updateFile');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateFile']);
</script>

<style lang="scss" scoped></style>
