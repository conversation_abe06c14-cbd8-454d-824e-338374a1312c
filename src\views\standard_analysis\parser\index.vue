<template>
  <div class="app-container">
    <div class="app-container-content">
      <div class="flex flex-sb flex-ai-center">
        <div class="flex flex-ai-center">
          <div class="spot"></div>
          <div class="f-14 c-33">列队中标准 <span class="c-primary f-16">{{ counts.waitCount || 0 }}</span></div>
          <div class="f-14 c-33">丨解析失败标准 <span class="c-primary f-16">{{ counts.failCount || 0 }}</span></div>
        </div>
        <div>
          <el-button
            v-hasPermi="['standard_analysis:parser:removeAll']"
            @click="handleAllDelete"
            :disabled="allDisabled"
            plain
            class="iconfont icon-overview-recycle"
          >
            <span class="ml5">全部移出</span>
          </el-button>
          <el-button plain @click="handleClear" icon="Refresh">刷新</el-button>
          
          <el-button
            v-if="PFlag == 0"
            v-hasPermi="['standard_analysis:parser:start']"
            @click="handleStart"
            class="iconfont icon-qidong start-btn"
          >
            <span class="ml5">启动</span>
          </el-button>
          <el-button
            v-else
            v-hasPermi="['standard_analysis:parser:end']"
            @click="handlePause"
            class="iconfont icon-zanting zanting-btn"
          >
            <span class="ml5">停止</span>
          </el-button>
          <el-button
            v-hasPermi="['standard_analysis:parser:import']"
            @click="handleImport"
            type="primary"
            class="iconfont icon-daoru"
          >
            <span class="ml5">导入解析数据</span>
          </el-button>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        :border="true"
        class="mt15"
        @selection-change="handleSelectionChange"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="selection" align="center" width="55" />
        <el-table-column label="序号" width="70" fixed>
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" min-width="180" fixed="left" label="标准号" show-overflow-tooltip />
        <el-table-column prop="standardName" min-width="180" label="标准名称" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" min-width="150" label="标准类型" show-overflow-tooltip />
        <el-table-column label="标准状态" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="getStatusColor(row.standardStatus)">
              {{ row.standardStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="解析状态" min-width="150" show-overflow-tooltip>
          <template #default="{row}">
            <div :class="getAnalysisStatusColor(row.analysisStatus)">{{ row.analysisStatusName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="解析进度" min-width="150" show-overflow-tooltip>
          <template #default="{row}">
            <div :class="getAnalysisScheduleColor(row.analysisSchedule)">
              {{ row.analysisScheduleName }}
              <el-popover
                v-if="row.analysisSchedule == 1"
                placement="top-start"
                trigger="hover"
                :width="200"
                effect="dark"
                :content="row.analysisResult"
              >
                <template #reference>
                  <el-icon class="c-99"><InfoFilled /></el-icon>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createName" min-width="150" label="操作人" show-overflow-tooltip />
        <el-table-column prop="createTime" label="添加时间" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              @click.stop="handleDetail(scope.row)"
              link
              class="c-primary"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleRemove(scope.row)"
              link
              class="c-primary"
              v-hasPermi="['standard_analysis:parser:remove']"
            >
              移出
            </el-button>
            <el-button
              v-if="scope.row.analysisStatus == 1 && scope.row.analysisSchedule == 1"
              @click.stop="handleParse(scope.row)"
              link
              class="c-primary"
              v-hasPermi="['standard_analysis:parser:reAnalysis']"
            >
              重新解析
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <upload-dialog v-if="uploadVisible" v-model:visible="uploadVisible" @updateData="updateData" />
    <result-dialog v-if="resultVisible" v-model:visible="resultVisible" :rowData="resultData" @updateData="getList" />
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="standardId"
      :standardType="standardType"
    />
  </div>
</template>

<script setup>
  import { parseJobList,parseJobStop,parseJobStart,parseJobReparse,parseJobMoveOut,parseJobStatus } from '@/api/standard_analysis/parser'
  import UploadDialog from '@/views/components/standard_analysis/parser/UploadDialog'
  import ResultDialog from '@/views/components/standard_analysis/parser/ResultDialog'
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer'

  const { proxy } = getCurrentInstance();

  const data = reactive({
    dialogVisible:false,
    uploadVisible:false,
    resultVisible:false,
    currentId:null,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    tableData: [],
    total: 0,
    loading:false,
  });

  const {
    dialogVisible,
    uploadVisible,
    resultVisible,
    currentId,
    loading,
    queryParams,
    tableData,
    total,
  } = toRefs(data);
  const params = ref([])
  const PFlag = ref(0)
  const counts = ref({})
  const drawerVisible = ref(false);
  const rowData = ref({})
  const resultData = ref({})
  const selectedList = ref([])
  const allDisabled = ref(true)
  const standardId = ref('');
  const standardType = ref(undefined);

  const getList = val => {
    loading.value = true;
    parseJobStatus().then(res => {
      PFlag.value = res.data
    })
    parseJobList(queryParams.value)
      .then(res => {
        let rows = res.rows;
        tableData.value = rows || [];
        counts.value = res.bean || {}
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleStart = () => {
    proxy.$confirm('确认启动解析器？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      parseJobStart().then(() => {
        proxy.$modal.msgSuccess('启动成功');
        getList();
      });
    })
    .catch(() => {});
  }

  const handlePause = () => {
    proxy.$confirm('确认停止解析器？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      parseJobStop(params).then(() => {
        proxy.$modal.msgSuccess('停止成功');
        getList();
      });
    })
    .catch(() => {});
  }

  const handleImport = () => {
    uploadVisible.value = true;
  }

  const handleRemove = (row) => {
    proxy.$confirm('确认将当前标准移出解析队列？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      let idList = []
      idList.push(row.id)
      parseJobMoveOut(idList).then(() => {
        proxy.$modal.msgSuccess('移出成功');
        getList();
      });
    })
    .catch(() => {});
  }

  const handleSelectionChange = (list) => {
    if(list && list.length > 0){
      allDisabled.value = false
    }else{
      allDisabled.value = true
    }
    selectedList.value = list;
  };

  const handleAllDelete = () => {
    let idList = selectedList.value.map(item => item.id)
    proxy.$confirm('确认将当前所选 '+ idList.length + ' 条解析标准移出解除列队？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      parseJobMoveOut(idList).then(() => {
        proxy.$modal.msgSuccess('移出成功');
        getList();
      });
    })
    .catch(() => {});
  }

  const handleDetail = (row) => {
    standardId.value = row.standardId;
    standardType.value = row.standardType;
    drawerVisible.value = true;
  }

  const handleParse = (row) => {
    proxy.$confirm('确认将当前标准重新加入解析队列？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      parseJobReparse(row.id).then(() => {
        proxy.$modal.msgSuccess('添加成功');
        getList();
      });
    })
    .catch(() => {});
  }

  const handleClear = () => {
    getList('pageNum');
  };

  const getAnalysisStatusColor = (status) => {
    switch (status) {
      case '0':
        return 'c-FF0000';
      case '1':
        return 'c-primary';
      case '2':
        return 'c-A200FF';
      case '3':
        return 'c-888888';
      case '4':
        return 'c-FFAE00';
      case '5':
        return 'c-0DB700';
      default:
        return 'c-0DB700';
    }
  }

  const getAnalysisScheduleColor = (status) => {
    switch (status) {
      case '0':
        return 'c-primary';
      case '1':
        return 'c-FF0000';
      default:
        return 'c-FF0000';
    }
  }

  const getStatusColor = (data) => {
    switch (Number(data)) {
      case 0:
        return 'status-intro-blue';
        break;
      case 1:
        return 'status-intro-green';
        break;
      case 3:
        return 'status-intro-gray';
        break;
      case 4:
        return 'status-intro-red';
        break;
      default:
        break;
    }
  };

  const updateData = (val) => {
    resultVisible.value = true
    resultData.value = val
  }

  getList();
</script>

<style lang="scss" scoped>
.icon-shujutongbu:before {
  margin-right: 6px;
}
.start-btn{
  color: #3FC325;
  background-color: #D9FFD1;
  border: none;
}
.zanting-btn{
  color: #FF0000;
  background-color: #FFD1D1;
  border: none;
}
.spot{
  width: 7px;
  height: 7px;
  border-radius: 50%;
  margin-right: 7px;
  background-color: $primary-color;
}
.c-A200FF{
  color: #A200FF;
}
.c-888888{
  color: #888888;
}
.c-FFAE00{
  color: #FFAE00;
}
.c-0DB700{
  color: #0DB700;
}
</style>
