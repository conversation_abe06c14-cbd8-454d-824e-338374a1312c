import request from '@/utils/request'

// 查询解析标准列表
export const getStandardList = (params) => {
  return request({
    url: '/compare/analysisList',
    method: 'get',
    params
  })
}
// 查询推荐比对标准列表
export const getRecommendStandardList = (standardId) => {
  return request({
    url: '/compare/queryRecommendList/'+standardId,
    method: 'get'
  })
}
// 标准对比
export const getStandardCompare = (params) => {
  return request({
    url: '/compare/standardCompare',
    method: 'get',
    params,
    timeout: 60*1000
  })
}
// 解析详情-树结构
export const getStandardTree = (params) => {
  return request({
    url: '/process/common/standard/getAnalysisIndexDetail',
    method: 'get',
    params
  })
}
// 解析详情-根据索引ID解析当前及以下（包括目次、正文、pdf地址）
export const getStandardTreeContentById = (params) => {
  return request({
    url: '/process/common/standard/getAnalysisDetailByIndex',
    method: 'get',
    params
  })
}
// 解析详情-全部（包括目次、正文、pdf地址）
export const getStandardTreeContent = (standardId) => {
  return request({
    url: '/process/common/standard/getAnalysisDetail/'+ standardId,
    method: 'get'
  })
}