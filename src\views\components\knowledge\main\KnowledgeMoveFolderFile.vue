<template>
  <div class="pop-container">
    <el-dialog :modal-append-to-body="false" v-model="open" width="450px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div v-if="step == 1" class="library-container">
        <el-input maxlength="50" v-model="name" placeholder="搜索知识库名称" />
        <div class="move-tip mt10">我参与的知识库</div>
        <ul class="libary-wrap">
          <li v-for="item in libraryList" :key="item.id" @click="getCategory(item)" class="library-item">
            <div class="nav-img">
              <img :src="item.cover" alt="">
            </div>
            <div class="nav-content">
              <div class="nav-title">{{item.name || '-'}}</div>
              <div class="nav-desc">{{item.describe || '-'}}</div>
            </div>
            <div class="nav-arrow"><el-icon><ArrowRightBold /></el-icon></div>
          </li>
        </ul>
      </div>
      <div v-if="step == 2" class="category-tree-container">
        <el-input maxlength="50" v-model="fileName" placeholder="搜索文件夹名称" />
        <div class="move-tip mt10">我选择的知识库：{{currentLibraryItem.name}}</div>
        <el-tree
          ref="treeCateRef"
          :data="treeList"
          show-checkbox
          default-expand-all
          node-key="id"
          empty-text="无可选文件夹"
          highlight-current
          :props="defaultProps"
          :check-strictly="true"
          @check="treeCheck"
          :check-on-click-node="true"
          :filter-node-method="filterNode"
        />
        <div class="mt10">移动至{{moveFileName()}}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="step == 2" type="primary" @click="back">返回</el-button>
          <el-button @click="close">取 消</el-button>
          <el-button v-if="step != 1" @click.prevent="save" type="primary" :loading="loading">
            <span v-if="!loading">移动</span>
            <span v-else>移动中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>

</template>

<script setup>
import { moveFolderOrFile,getLibraryAll,getMoveLibraryTree } from "@/api/knowledge/library";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  fileId: [String,Number],
});
const { open,fileId } = toRefs(props)

const commonInfo = inject('commonInfo')

const data = reactive({
  title: '移动至',
  loading: false,
  libraryList: [],
  tLibraryList: [],
  treeList: [],
  defaultProps:{
    children: 'childrenList',
    label: 'fileName',
  },
  name: '',
  fileName: '',
  step: 1,
  currentLibraryItem: {},
  currentCategoryItem: {}
})
const { title,loading,step,libraryList,treeList,defaultProps,name,fileName,currentLibraryItem } = toRefs(data)

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  if(!data.currentLibraryItem.id){
    proxy.$modal.msgError('请选择知识库')
    return
  }
  let params = {
    fromId: props.fileId,
    libId: data.currentLibraryItem.id,
    toId: data.currentCategoryItem.id ? data.currentCategoryItem.id : 0
  }
  moveFolderOrFile(params).then(response => {
    data.loading = false;
    emit('update:open',false);
    emit('success');
    proxy.$modal.msgSuccess("移动成功");
  }).catch(error => {
    data.loading = false;
    proxy.$modal.msgError("移动失败");
  });
}

const getLibrary = () => {
  data.loading = true;
  getLibraryAll().then((response) => {
    data.tLibraryList = response.data
    data.libraryList = data.tLibraryList
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const moveFileName = () => {
  return data.currentCategoryItem.id ? data.currentCategoryItem.fileName : data.currentLibraryItem.name
}
const getCategory = (item) => {
  data.currentLibraryItem = item
  data.loading = true;
  let params = {
    libId: item.id,
    pid: props.fileId
  }
  getMoveLibraryTree(params).then((response) => {
    data.treeList = response.data
    data.loading = false
    data.step = 2
  }).catch(() => {
    data.loading = false
  });
}
const back = () => {
  data.step = 1
  data.currentCategoryItem = {}
  data.currentLibraryItem = {}
  data.treeList = []
}

watch(()=>data.fileName, val => {
  proxy.$refs["treeCateRef"].filter(val)
});
watch(()=>data.name, val => {
  if(!val){
    data.libraryList = data.tLibraryList
  }else{
    data.libraryList = data.tLibraryList.filter(item => item.name.indexOf(val) !== -1)
  }
  
});
/** 通过条件过滤节点  */
const filterNode = (value, item) => {
  console.log('item=',item);
  if (!value) return true;
  return item.fileName.indexOf(value) !== -1;
}
const treeCheck = (node,list) => {
  if (list.checkedKeys.length == 2) {
    //单选实现
    proxy.$refs.treeCateRef.setCheckedKeys([node.id])
    data.currentCategoryItem = node
  }else if(list.checkedKeys.length == 0) {
    data.currentCategoryItem = {}
  }else{
    data.currentCategoryItem = node
  }

}
getLibrary()

</script>

<style lang="scss" scoped>
ul,li{
  margin: 0;
  padding: 0;
  list-style: none;
}
.libary-wrap{
  max-height: 400px;
  overflow-y: auto;
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
  .library-item{
    margin: 15px 0;
    display: flex;

    .nav-img{
      img{
        width: 75px;
        height: 50px;
        margin-right: 5px;
        border-radius: 3px;
      }
    }
    .nav-content{
      width: 280px;
      margin: 0 10px;
      color: #3E4967;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .nav-title{
        font-weight: bold;
      }
      .nav-desc{
        // width: 140px;
        white-space: nowrap;
        overflow: hidden;//文本超出隐藏
        text-overflow: ellipsis;//文本超出省略号替代
      }
    }
    .nav-arrow{
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.el-tree{
  max-height: 400px;
  overflow-y: auto;
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
</style>