<template>
  <div class="app-container">
    <div class="app-container-search">
      <div class="fresh-op-wrap">
        <div class="fresh-op-container">
          <el-upload
            style="width: 100%"
            ref="uploadRef"
            v-model:file-list="fileList"
            :limit="1"
            :before-upload="handleBeforeUpload"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :auto-upload="true"
            :show-file-list="false"
          >
            <div style="width: 100%" class="el-upload__text">
              <el-button style="width: 100%" class="mt30 h50 f-16 bgc-primary" type="primary">
                <i class="iconfont icon-shangchuan mr5 f-16"></i>
                上传查新数据
              </el-button>
            </div>
            <template #tip>
              <div class="el-upload__tip mb20 mt10 f-14" style="color: red">
                仅支持xlsx,xls格式文件；单次最多查新500条；不能超过5MB
              </div>
            </template>
          </el-upload>
          <el-button class="mt10 h50 f-16 bgc-primary" type="primary" @click="handleDownloadTemplate">
            <i class="iconfont icon-xiazai mr5 f-20"></i>
            下载导入模板
          </el-button>
          <div class="mt10 f-14" style="color: red">请按导入模板说明正确填写查新数据</div>
        </div>
      </div>
    </div>
    <div v-if="isSearch" class="app-container-content mt15">
      <div class="container-bar fresh-bar">
        <div class="bar-left">
          查新标准数：
          <span class="c-FFB400">{{ statInfo.count || '0' }}</span>
          条；检索到标准数：
          <span class="c-FFB400">{{ statInfo.onCount || '0' }}</span>
          条；未检索到标准数：
          <span class="c-FFB400">{{ statInfo.nonCount || '0' }}</span>
          条；即将实施标准：
          <span class="c-FFB400">{{ statInfo.tobeCount || '0' }}</span>
          条；现行标准：
          <span class="c-FFB400">{{ statInfo.activeCount || '0' }}</span>
          条；废止标准：
          <span class="c-FFB400">{{ statInfo.abolishCount || '0' }}</span>
          条；被替代标准：
          <span class="c-FFB400">{{ statInfo.beReplacedCount || '0' }}</span>
          条
        </div>
        <div class="bar-right">
          <el-button type="primary" @click="handleExport" icon="Download">下载查新结果</el-button>
        </div>
      </div>
      <el-table :data="currentPageData" class="mt-10" :border="true">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" width="80">
          <template #default="{ $index }">
            {{ (form.pageNum - 1) * form.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" show-overflow-tooltip label="标准号" min-width="180" />
        <el-table-column prop="standardName" show-overflow-tooltip label="标准名称" min-width="180" />
        <el-table-column prop="searchTypeName" show-overflow-tooltip label="检索结果" min-width="150" />
        <el-table-column prop="standardTypeName" show-overflow-tooltip label="标准类型" min-width="150" />
        <el-table-column prop="standardStatusName" show-overflow-tooltip label="标准状态" min-width="150" />
        <el-table-column prop="repealDate" show-overflow-tooltip label="废止日期" min-width="180" />
        <el-table-column prop="beReplacedStandardCode" show-overflow-tooltip label="被替代标准号" min-width="200" />
        <el-table-column prop="beReplacedStandardName" show-overflow-tooltip label="被替代标准名称" min-width="200" />
      </el-table>
      <pagination
        v-show="filteredDataLength > 0"
        v-model:page="form.pageNum"
        v-model:limit="form.pageSize"
        :total="filteredDataLength"
        @pagination="updateCurrentPageData"
      />
    </div>
    <div v-else class="app-container-content mt15 container-height">
      <el-empty :image="EMPTYBOX" description="暂无查新数据" />
    </div>
  </div>
</template>

<script setup>
  import { json2excel } from '@/utils/formatExcel.js';
  import { getToken } from '@/utils/auth';
  import { ElLoading } from 'element-plus';
  import EMPTYBOX from '@/assets/images/empty-box.png';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    loading: false,
    open: false,
    total: 0,
    dataList: [],
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      batchNumber: undefined,
    },
    upload: {
      // 是否禁用上传
      isUploading: false,
      // 是否更新已经存在的数据
      // updateSupport: 0,
      // 设置上传的请求头部
      headers: { Authorization: 'Bearer ' + getToken() },
      // 上传的地址
      url: process.env.VITE_APP_BASE_API + '/novelty/search/import',
    },
    fileSize: 5,
    fileType: ['xlsx', 'xls'],
    fileList: [],
    statInfo: {},
  });
  const { dateRange, loading, open, total, dataList, queryParams, upload, fileSize, fileType, fileList, statInfo } = toRefs(data);
  let downloadLoadingInstance;

  const tableData = ref([]);
  const form = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const currentPageData = ref([]);
  const filteredDataLength = ref(0);
  const isSearch = ref(false);

  const updateCurrentPageData = () => {
    loading.value = true;
    filteredDataLength.value = tableData.value.length;
    currentPageData.value = tableData.value.slice(
      (form.value.pageNum - 1) * form.value.pageSize,
      form.value.pageNum * form.value.pageSize
    );
    nextTick(() => {
      loading.value = false;
    });
  };

  const handleDownloadTemplate = () => {
    window.location.href = '/file/标准查新导入模板.xlsx';
  };

  const handleExport = () => {
    let excelDatas = [
      {
        tHeader: ['标准号', '标准名称', '检索结果', '标准类型', '标准状态', '废止日期', '被替代标准号', '被替代标准名称'],
        filterVal: [
          'standardCode',
          'standardName',
          'searchTypeName',
          'standardTypeName',
          'standardStatusName',
          'repealDate',
          'beReplacedStandardCode',
          'beReplacedStandardName',
        ],
        tableDatas: tableData.value,
        sheetName: 'Sheet1',
      },
    ];
    let multiHeader = [['标准查新结果']];
    json2excel(excelDatas, multiHeader, '标准查新结果', true, 'xlsx');
  };

  // 上传前校检格式和大小
  const handleBeforeUpload = file => {
    let isValid = false;
    if (data.fileType.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = data.fileType.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${data.fileType.join('/')}格式文件!`);
      return false;
    }
    if (data.fileSize) {
      const isLt = file.size / 1024 / 1024 < data.fileSize;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${data.fileSize} MB!`);
        return false;
      }
    }
    return true;
  };

  // 文件上传中处理
  const handleFileUploadProgress = (event, file, fileList) => {
    data.upload.isUploading = true;
    downloadLoadingInstance = ElLoading.service({ text: '正在上传数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  // 文件上传成功处理
  const handleFileSuccess = (response, file, fileList) => {
    data.upload.open = false;
    data.upload.isUploading = false;
    proxy.$refs.uploadRef.clearFiles();
    downloadLoadingInstance.close();
    const { code, data: dataT, msg } = response;

    if (code == 200) {
      isSearch.value = true;
      statInfo.value = dataT;
      tableData.value = dataT.list;
      updateCurrentPageData();
    } else {
      proxy.$modal.msgError(msg);
    }
  };

  const handleFileError = (error, uploadFile, uploadFiles) => {
    downloadLoadingInstance.close();
  };
</script>

<style lang="scss" scoped>
  .app-container{
    min-width: calc(100vw - 280px);
    width: 100%;
  }
  .container-height {
    width: 100%;
    height: calc(100vh - 420px);
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .fresh-op-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    .fresh-op-container {
      width: 400px;
      display: flex;
      flex-direction: column;
      :deep(.el-upload) {
        width: 100%;
      }
    }
  }
  .fresh-bar {
    .bar-left {
      position: relative;
      padding-left: 15px;
      height: 40px;
      line-height: 40px;

      &::before {
        content: '';
        width: 7px;
        height: 7px;
        background: #2f5aff;
        border-radius: 50%;
        position: absolute;
        top: 40%;
        left: 0;
      }
    }
  }
</style>
