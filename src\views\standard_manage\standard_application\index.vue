<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" class="demo-form-inline" @submit.prevent>
        <el-form-item label="标准应用名称:" prop="applyName">
          <el-input v-model="queryParams.applyName" placeholder="请输入标准应用名称" @keyup.enter="getList('pageNum')" />
        </el-form-item>
        <el-form-item label=" ">
          <el-button @click="getList('pageNum')" type="primary"  icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="ta-r">
        <el-button v-hasPermi="['standard_manage:standard_application:add']" @click="handleAdd" type="primary" icon="Plus">
          新建应用分类
        </el-button>
      </div>
      <el-table :data="tableData" class="mt15" :border="true">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="applyName" label="标准应用名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="applyDescription" label="应用描述" show-overflow-tooltip width="450" />
        <el-table-column prop="menuCount" label="目录数" show-overflow-tooltip min-width="120" />
        <el-table-column prop="standardCount" label="标准数" show-overflow-tooltip min-width="120" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="150" />
        <el-table-column label="操作" min-width="180">
          <template #default="scope">
            <el-button
              @click.stop="handleView(scope.row)"
              v-hasPermi="['standard_manage:standard_application_item:list']"
              link
              size="small"
              class="f-14 c-primary"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleSet(scope.row)"
              v-hasPermi="['standard_manage:standard_application:setting']"
              link
              size="small"
              class="f-14 c-primary"
            >
              设置
            </el-button>
            <el-button
              @click.stop="handleDelete(scope.row)"
              v-hasPermi="['standard_manage:standard_application:remove']"
              link
              size="small"
              type="danger"
              class="f-14 c-F20000"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <pop-add-standard-application v-if="popAdd" v-model:open="popAdd" v-model:id="id" @success="getList" />
  </div>
</template>

<script setup>
  import { getStandardApplicationList, deleteStandardApplication } from '@/api/standard_manage/standard_application';
  import PopAddStandardApplication from '@/views/components/standard_manage/PopAddStandardApplication';

  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      applyName: null,
    },
    tableData: [],
    total: 0,
    popAdd: false,
    id: null,
  });

  const { queryParams, tableData, total, popAdd, id } = toRefs(data);

  onMounted(() => {
    getList();
  });

  const getList = val => {
    if (val) queryParams.value[val] = 1;
    getStandardApplicationList(queryParams.value).then(res => {
      tableData.value = res.rows;
      total.value = res.total;
    });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    getList('pageNum');
  };

  const handleView = row => {
    router.push({ path: '/standard_manage/standard_application_item', query: { id: row.id, name: row.applyName } });
  };
  const handleAdd = () => {
    id.value = undefined;
    popAdd.value = true;
  };
  const handleSet = row => {
    id.value = row.id;
    popAdd.value = true;
  };

  const handleDelete = row => {
    proxy
      .$confirm('确认删除标准应用名称为“' + row.applyName + '”的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        deleteStandardApplication({ ids: [row.id] }).then(() => {
          proxy.$modal.msgSuccess('删除成功');
          getList();
        });
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped>
  ._cover {
    width: 70px;
    height: 45px;
  }
</style>
