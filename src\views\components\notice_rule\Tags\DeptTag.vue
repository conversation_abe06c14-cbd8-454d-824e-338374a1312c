<template>
  <div>
    <el-tag
      v-for="item in tags"
      :key="item"
      :disable-transitions="false"
      color="#2F5AFF"
      closable
      @close="handleClose(item)"
      class="mx-1 mb5"
    >
      {{ item.label }}
    </el-tag>
    <el-button
      v-if="addBtn"
      @click="memberDialog = true"
      type="primary"
      plain
      color="#2F5AFF"
      class="mx-1"
    >
      + 添加
    </el-button>
    <dept-tree-dialog
      v-if="memberDialog"
      v-model:dialogVisible="memberDialog"
      v-model:tags="tags"
      @handleChoose="handleChoose"
    />
  </div>
</template>

<script setup>
import DeptTreeDialog from "@/views/components/notice_rule/Tags/DeptTreeDialog";

const props = defineProps({
  addBtn: {
    type: Boolean,
    default: true,
  },
  tags: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const { addBtn, tags } = toRefs(props);

const data = reactive({
  memberDialog: false,
});
const { memberDialog } = toRefs(data);

const handleClose = (item) => {
  let arr = tags.value.filter((el) => {
    return el.id != item.id;
  });
  nextTick(() => {
    emit("update:tags", arr);
    emit("handleUpdate", arr);
  });
};

const handleChoose = (data) => {
  emit("update:tags", data);
};

const emit = defineEmits(["update:tags", "handleUpdate"]);
</script>

<style lang="scss" scoped>
.el-tag {
  font-size: 14px !important;
  color: #ffffff !important;
  height: 29px !important;
}

:deep(.el-tag__close) {
  color: #ffffff !important;
}

.el-button--primary.is-plain {
  height: 29px !important;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
</style>