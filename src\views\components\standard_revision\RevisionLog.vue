<template>
  <div>
    <el-table v-loading="loading" :data="tableData" :border="true">
      <el-table-column label="序号" width="60" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="type" label="阶段名称" show-overflow-tooltip min-width="200" fixed />
      <el-table-column prop="content" label="操作事项" show-overflow-tooltip min-width="150" />
      <el-table-column prop="createTime" label="操作时间" show-overflow-tooltip min-width="180" />
      <el-table-column prop="createByName" label="操作人" show-overflow-tooltip min-width="150" fixed="right" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script setup>
  import { getRevisionLogList } from '@/api/standard_revision/manage';

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
  });

  const loading = ref(true);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    standardId: props.id,
  });
  const total = ref(0);
  const tableData = ref([]);

  const getData = () => {
    getRevisionLogList(queryParams.value)
      .then(res => {
        emit('update:activeStep', Number(res.bean.status));
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const emit = defineEmits(['update:activeStep']);

  getData();
</script>

<style lang="scss" scoped></style>
