<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="文号:" prop="letterNumber">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.letterNumber" placeholder="请输入文号" />
        </el-form-item>
        <el-form-item label="名称:" prop="letterName">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.letterName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="发文单位:" prop="issueUnit">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.issueUnit" placeholder="请输入发文单位" />
        </el-form-item>
        <el-form-item label="类别:" prop="letterTypeList" class="one-column">
          <el-checkbox-group
            @keyup.enter="getList('pageNum')"
            v-model="queryParams.letterTypeList"
            placeholder="请选择类别"
          >
            <el-checkbox v-for="item in bxc_letter_type" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="状态:" prop="letterStatusList" class="one-column">
          <el-checkbox-group
            @keyup.enter="getList('pageNum')"
            v-model="queryParams.letterStatusList"
            placeholder="请选择状态"
          >
            <el-checkbox v-for="item in bxc_letter_status" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="创建日期:">
          <el-date-picker
            v-model="dateRange"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="selectDate"
          />
        </el-form-item>
        <el-form-item label="" class="one-column">
          <el-button :loading="loading" @click="getList('pageNum')" type="primary"  icon="Search">
            查询
          </el-button>
          <el-button :loading="loading" plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button
          v-hasPermi="['standard_manage:letter_query:add']"
          @click="handleAdd"
          type="primary"
          icon="Plus"
        >
          新增函文
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        :border="true"
        class="mt15"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="letterNumber" label="文号" show-overflow-tooltip min-width="200" />
        <el-table-column prop="letterName" label="名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="letterTypeName" label="分类" show-overflow-tooltip min-width="150" />
        <el-table-column prop="letterStatusName" label="状态" show-overflow-tooltip min-width="120" />
        <el-table-column prop="issueUnit" label="发文单位" show-overflow-tooltip min-width="200" />
        <el-table-column prop="issueDate" label="发文日期" show-overflow-tooltip min-width="180" />
        <el-table-column prop="createTime" label="创建日期" show-overflow-tooltip min-width="180" />
        <el-table-column prop="createBy" label="创建人" show-overflow-tooltip min-width="150" />
        <el-table-column label="操作" min-width="180" fixed="right">
          <template #default="scope">
            <el-button
              @click.stop="handleEdit(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
              v-hasPermi="['standard_manage:letter_query:edit']"
            >
              编辑
            </el-button>
            <el-button
              @click.stop="handleDetail(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
              v-hasPermi="['standard_manage:letter_query:detail']"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleDel(scope.row)"
              link
              size="small"
              v-hasPermi="['standard_manage:letter_query:delete']"
              class="f-14 c-F20000"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <letter-add-dialog v-if="addDialogVisible" v-model:addDialogVisible="addDialogVisible" :id="currentId" @getList="getList" />
    <letter-detail-dialog v-if="dialogVisible" v-model:dialogVisible="dialogVisible" :id="currentId" />
  </div>
</template>

<script setup name="Standard_query/index">
  import { standardLetterList, delStandardLetter } from '@/api/standard_manage/letter_query';
  import LetterAddDialog from '@/views/components/standard_manage/letter_query/LetterAddDialog.vue';
  import LetterDetailDialog from '@/views/components/standard_manage/letter_query/LetterDetailDialog.vue';

  const { proxy } = getCurrentInstance();

  const { bxc_letter_type, bxc_letter_status } = proxy.useDict(
    'bxc_letter_type',
    'bxc_letter_status'
  );

  const data = reactive({
    dialogVisible:false,
    addDialogVisible:false,
    currentId:null,
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    tableData: [],
    total: 0,
    loading:false,
    ids:[],
    dateRange:[]
  });

  const {
    dialogVisible,
    addDialogVisible,
    currentId,
    loading,
    queryParams,
    tableData,
    total,
    ids,
    dateRange
  } = toRefs(data);

  const getList = () => {
    loading.value = true;
    standardLetterList(queryParams.value)
      .then(res => {
        let rows = res.rows;
        tableData.value = rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    data.dateRange = [];
    selectDate();
    getList();
  };

  const handleAdd= () => {
    currentId.value = null;
    addDialogVisible.value = true;
  };

  const handleEdit = (row) => {
    currentId.value = row.id;
    addDialogVisible.value = true;
  }
  const handleDetail = (row) => {
    currentId.value = row.id;
    dialogVisible.value = true;
  }

  const handleDel = (row) => {
    ids.value.push(row.id)
    proxy.$confirm('确认是否删除此条数据？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      delStandardLetter({ids:ids.value})
        .then(res => {
          proxy.$modal.msgSuccess('函文删除成功！')
        })
        .finally(() => {
          getList();
        });
    })
  }

  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.queryParams.startTime = data.dateRange[0];
      data.queryParams.endTime = data.dateRange[1];
    } else {
      data.queryParams.startTime = undefined;
      data.queryParams.endTime = undefined;
    }
  }

  getList();
</script>

<style lang="scss" scoped>
  
</style>
