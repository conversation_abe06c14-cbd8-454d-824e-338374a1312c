<template>
  <el-drawer title="征求意见" size="75%" append-to-body lock-scroll v-model="props.visible" @close="handleClose">
    <opinion-detail-intro :form="form" />
    <div class="mt20">
      <div class="h-title">项目进度</div>
      <el-steps :active="Number(form.status - 1)" align-center class="mt30">
        <el-step v-for="(item, index) in stepList" :key="index" :title="item.title"></el-step>
      </el-steps>
      <template v-if="form.solicitStatus == 0">
        <div class="flex flex-sb flex-ai-center mt30">
          <div class="h-title">征求意见</div>
          <el-button @click="handleClick" icon="Edit" type="primary">意见反馈</el-button>
        </div>
        <el-descriptions :column="2" size="large" class="mt20 w-871">
          <el-descriptions-item label="征求截止时间：" :span="2" label-align="left">
            {{ form.deadline || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="征求意见稿：" :span="2" label-align="left">
            <span @click="handleDown(item)" v-for="item in form.draftOpinionFileList" :key="item.id" class="file pointer">
              {{ item.name }}
            </span>
            <span @click="handleDown(item)" v-for="item in form.compilationInstructionFileList" :key="item.id" class="file pointer">
              {{ item.name }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <div class="h-title mt20">项目信息</div>
      <el-descriptions :column="3" size="large" class="mt20">
        <el-descriptions-item label="项目编号：" label-align="left">
          {{ form.projectCode || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目名称：" label-align="left">
          {{ form.projectName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目负责人：" label-align="left">
          {{ form.projectManagerName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="制修订类型：" label-align="left">
          {{ form.amendName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="标准号：" label-align="left">
          {{ form.standardCode || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="标准名称：" label-align="left">
          {{ form.standardName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="计划开始日期：" label-align="left">
          {{ form.planStartTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="计划完成日期：" label-align="left">
          {{ form.planEndTime || '-' }}
        </el-descriptions-item>
        <template v-if="form.amend == 1">
          <el-descriptions-item label="替代标准号：" label-align="left">
            {{ form.beReplacedStandardCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="替代标准名称：" label-align="left">
            {{ form.beReplacedStandardName || '-' }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>
    <add-opinion-dialog v-if="opinionVisible" v-model:visible="opinionVisible" :defaultForm="defaultForm" @updateData="success" />
  </el-drawer>
</template>

<script setup>
  import { getSolicitDetail } from '@/api/standard_revision/opinion';
  import OpinionDetailIntro from '@/views/components/standard_revision/OpinionDetailIntro.vue';
  import AddOpinionDialog from '@/views/components/standard_revision/AddOpinionDialog.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const form = ref({
    status: 2,
  });
  const stepList = reactive([
    { title: '起草', value: 2 },
    { title: '征求意见', value: 3 },
    { title: '标准审查', value: 4 },
    { title: '标准报批', value: 5 },
    { title: '标准发布', value: 6 },
  ]);
  const opinionVisible = ref(false);
  const defaultForm = ref({});

  getSolicitDetail(props.id).then(res => {
    form.value = res.data;
  });

  const handleClick = () => {
    defaultForm.value = {
      amendId: form.value.amendId,
      joinId: form.value.joinId,
      joinType: 0,
      opinionId: form.value.id,
      projectCode: form.value.projectCode,
      projectName: form.value.projectName,
      projectIntroduce: form.value.projectIntroduce,
    };
    opinionVisible.value = true;
  };

  const handleDown = item => {
    proxy.download('/system/oss/download/' + item.id, {}, item.name);
  };

  const handleClose = () => {
    emit('updateData');
    emit('update:visible', false);
  };

  const success = () => {
    emit('updateData');
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped>
  .info {
    &-title {
      font-size: 22px;
      color: #333333;
      font-weight: 600;
    }

    &-line {
      flex: 1;
      max-width: 120px;
      border-top: 2px dotted #e5e5e5;
    }
  }

  .description {
    font-size: 14px;
    min-width: 200px;
    max-width: 270px;
    border-radius: 3px;
    position: relative;
    margin-top: 10px;
    box-sizing: border-box;

    &-down {
      padding: 0 15px;
      box-sizing: border-box;
      box-sizing: border-box;
      border: 1px solid $primary-color;
      margin-top: -2px;
    }

    &-title {
      color: #ffffff;
      width: 100%;
      height: 50px;
      // padding-left: 30px;
      // box-sizing: border-box;
      // background-color: $primary-color;
      position: relative;
      background-image: url('../../assets/images/revision/description.png');
      background-size: 100% 100%;

      span {
        position: absolute;
        left: 30px;
        top: 22px;
      }

      &:before {
        content: '';
        position: absolute;
        top: calc(50%);
        left: 20px;
        width: 3px;
        height: 14px;
        background: #ffffff;
        border-radius: 2px;
        z-index: 99;
      }
    }

    &-tip {
      font-size: 14px;
      color: #ffb400;
      width: 100px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      background: #fff4d8;
      border-radius: 3px;
      margin-top: 15px;
    }

    &-code {
      color: $primary-color;
      margin-top: 10px;
    }

    &-name {
      color: #333333;
      margin: 5px 0 10px;
    }
  }

  .file {
    color: $primary-color;
    text-decoration: underline;

    &:nth-child(n + 2) {
      margin-left: 30px;
    }
  }

  .w-78 {
    width: 78px;
  }

  .w-890 {
    width: 890px;
  }

  :deep(.el-step__head.is-process) {
    color: #cacaca !important;
    border-color: #cacaca !important;
  }

  :deep(.el-step__head.is-wait) {
    color: #cacaca !important;
    border-color: #cacaca !important;
  }

  :deep(.el-step__title) {
    font-size: 14px !important;
  }

  :deep(.el-step__title.is-process) {
    color: #333333 !important;
    font-weight: 400 !important;
  }

  :deep(.el-step__title.is-wait) {
    color: #333333 !important;
    font-weight: 400 !important;
  }

  :deep(.el-step__line) {
    height: 3px !important;
    background-color: #e5e8ef !important;
  }

  :deep(.el-step__icon-inner) {
    display: none !important;
  }

  :deep(.el-step__icon.is-text) {
    border-width: 4px !important;
  }

  :deep(.el-step__title.is-finish) {
    font-weight: 600 !important;
  }

  :deep(.el-step__icon) {
    width: 18px !important;
    height: 18px !important;
  }

  :deep(.el-step.is-horizontal .el-step__line) {
    top: 8px !important;
  }
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
