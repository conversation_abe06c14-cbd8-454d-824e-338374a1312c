<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form label-width="auto" :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="标准名称:" prop="standardName">
          <el-input @keyup.enter="handleQuery" v-model="queryParams.standardName" placeholder="请输入标准名称" />
        </el-form-item>
        <el-form-item label="标准号:" prop="standardCode">
          <el-input @keyup.enter="handleQuery" v-model="queryParams.standardCode" placeholder="请输入标准号" />
        </el-form-item>
        <el-form-item label="标准类型:" prop="standardType">
          <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
            <el-option v-for="dict in bxc_standard_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="废止日期:">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <!-- <div class="container-bar">
        <div class="bar-right">
          <el-button
            v-hasPermi="['customer:manage:export']"
            icon="Upload"
            @click="handleExport"
            >导入更新</el-button
          >
        </div>
      </div> -->
      <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15" :border="true">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="标准号" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span @click="toDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip />
        <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip />
        <el-table-column prop="executeDate" label="废止日期" show-overflow-tooltip />
        <el-table-column prop="beReplacedStandardCode" label="被代替标准号" show-overflow-tooltip />
        <el-table-column label="预警理由" width="300" show-overflow-tooltip>
          <template #default="scope">
            <div class="m-red">{{ scope.row.reason }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="预警时间" show-overflow-tooltip />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="selectRow.standardId"
      :standardType="selectRow.standardType"
    />
  </div>
</template>

<script setup>
  import { getWarnInfoList } from '@/api/standard_radar/radar.js';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const { proxy } = getCurrentInstance();
  const { bxc_standard_type } = proxy.useDict('bxc_standard_type');
  const router = useRouter();
  const data = reactive({
    loading: false,
    dateRange: [],
    total: 0,
    dataList: [],
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      warnType: '2',
      standardName: undefined,
      standardCode: undefined,
      standardType: undefined,
    },
  });
  const { loading, dateRange, total, dataList, queryParams } = toRefs(data);

  const selectRow = ref({});
  const drawerVisible = ref(false);

  const resetQuery = () => {
    data.dateRange = [];
    proxy.resetForm('queryFormRef');
    selectDate();
    handleQuery();
  };
  /** 搜索按钮操作 */
  const handleQuery = () => {
    data.queryParams.pageNum = 1;
    getList();
  };

  //获取列表
  const getList = () => {
    data.loading = true;
    getWarnInfoList(data.queryParams)
      .then(res => {
        data.dataList = res.rows;
        data.total = res.total;
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };

  const toDetail = row => {
    selectRow.value = row;
    drawerVisible.value = true;
  };

  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.queryParams.startTime = data.dateRange[0];
      data.queryParams.endTime = data.dateRange[1];
    } else {
      data.queryParams.startTime = undefined;
      data.queryParams.endTime = undefined;
    }
  };
  getList();
</script>

<style lang="scss" scoped></style>
