<template>
  <div>

    <div class="collect-type flex">
      <div class="tab flex flex-center mr20 pointer" @click="toggle('standard')" :class="active == 'standard'? 'active' : ''">标准收藏</div>
      <div class="tab flex flex-center mr20 pointer" @click="toggle('notice')" :class="active == 'notice'? 'active' : ''">公告收藏</div>
      <div class="tab flex flex-center pointer" @click="toggle('knowledge')" :class="active == 'knowledge'? 'active' : ''">知识收藏</div>
    </div>

    <el-table
      v-if="active == 'standard'"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="标准号"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="toDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="standardName"
        label="标准名称"
        min-width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="standardTypeName"
        label="标准类型"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="standardStatusName"
        label="标准状态"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="publishDate"
        label="发布日期"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="executeDate"
        label="实施日期"
        width="180"
        show-overflow-tooltip
      />
      
      <el-table-column
        prop="repealDate"
        label="废止日期"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="standardAttrName"
        label="标准属性"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="amendName"
        label="制修订"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="downloadTotal"
        label="下载总量"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column width="150" fixed="right" label="操作">
        <template #default="scope">
          <el-button
            @click.stop="handleNoCollect(scope.row)"
            link
            size="small"
            type="danger"
            class="f-14 c-F20000"
            v-hasPermi="['personal:standardCollectCancle']"
          >
            取消收藏
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-table
      v-if="active == 'notice'"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        label="标题"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handleDetail(row)" class="c-primary pointer">{{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="pname"
        label="类型"
        show-overflow-tooltip
      />
      <el-table-column
        prop="publishDate"
        label="发布日期"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="pv"
        label="阅读总量"
        show-overflow-tooltip
      />
      <el-table-column
        prop="collectTotal"
        label="收藏总量"
        show-overflow-tooltip
      />
      <el-table-column width="150" fixed="right" label="操作">
        <template #default="scope">
          <el-button
            @click.stop="handleNoCollect(scope.row)"
            link
            size="small"
            type="danger"
            class="f-14 c-F20000"
            v-hasPermi="['personal:noticeCollectCancle']"
          >
            取消收藏
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-table
      v-if="active == 'knowledge'"
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed width="80">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        label="资料名称"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span @click="handlePreview(row)" class="c-primary pointer">{{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="pname"
        label="知识类别"
        show-overflow-tooltip
      />
      <el-table-column
        prop="pv"
        label="阅览量"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createTime"
        label="收藏日期"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column width="150" fixed="right" label="操作">
        <template #default="scope">
          <el-button
            v-hasPermi="['personal:knowledgeDownload']"
            @click.stop="handleDownload(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
          >
            下载
          </el-button>
          <el-button
            @click.stop="handleNoCollect(scope.row)"
            link
            size="small"
            type="danger"
            class="f-14 c-F20000"
            v-hasPermi="['personal:knowledgeCollectCancle']"
          >
            取消收藏
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <notice-detail-dialog
      v-if="detailDialog"
      :id="currentId"
      v-model:dialogVisible="detailDialog"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
    <!-- 标准查看 -->
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="standardId"
      :standardType="standardType"
    />
  </div>
</template>

<script setup>
import {userCollectInfoList} from '@/api/personal/personal.js'
import NoticeDetailDialog from '@/views/components/notice_rule/NoticeDetailDialog'
import {userCollectInfoIsCollect} from '@/api/notice_rule/index.js'
import PreviewFile from '@/components/PreviewFile';
import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer'
import { getFileInfo } from "@/api/knowledge/library";

const { proxy } = getCurrentInstance();

const router = useRouter();
const data = reactive({
  loading: false,
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recordType:'0'
  },
  active:'standard',
  detailDialog:false,
  currentId:null,
  form:{
    recordType:null
  },
  openFile:false,
  currentUrl:null
});
const {
  loading,
  total,
  dataList,
  queryParams,
  active,
  detailDialog,
  currentId,
  openFile,
  currentUrl
} = toRefs(data);

const standardId = ref('');
const standardType = ref(undefined);
const drawerVisible = ref(false);

const toggle = (type) => {
  data.active = type;
  switch(type) {
    case 'standard':
      data.queryParams.recordType = '0'
      break;
    case 'notice':
      data.queryParams.recordType = '1'
      break;
    case 'knowledge':
      data.queryParams.recordType = '2'
      break;   
  }
  getList()
}

//获取列表
const getList = () => {
  data.loading = true;
  userCollectInfoList(data.queryParams)
  .then(res => {
    data.dataList = res.rows;
    data.total = res.total;
    data.loading = false;
  })
  .catch(() => {
    data.loading = false;
  });
}

//查看
const handleDetail = (row) => {
  data.currentId = row.recordId;
  data.detailDialog = true;
};
//取消收藏
const handleNoCollect = (row) => {
  data.form.recordId = row.recordId;
  data.form.isCollect = '0';
  data.form.recordType = row.recordType;
  userCollectInfoIsCollect(data.form).then(res => {
    proxy.$modal.msgSuccess('取消收藏成功')
    getList()
  }).catch(res => {
    getList()
  })
};

const getFileExtension = urlStr => {
  let fileExtension = '';
  if (urlStr.lastIndexOf('.') > -1) {
    fileExtension = urlStr.slice(urlStr.lastIndexOf('.') + 1);
    fileExtension = fileExtension.toLowerCase();
  }
  return fileExtension;
};
// 点击查看
const handlePreview = (row) => {
  //增加阅览量
  getFileInfo(row.recordId).then(res => {
    getList()
  })

  if (['.zip', '.rar'].includes(getFileExtension(row.file))) {
    proxy.$modal.msgError('zip,rar等压缩文件不能直接查看，请下载后查看！');
  } else {
    data.currentUrl = row.file;
    data.openFile = true;
  }
};

const toDetail = row => {
  standardId.value = row.recordId;
  standardType.value = row.standardType;
  drawerVisible.value = true;
};

const handleDownload = (item) => {
  let params = {
    recordId: item.recordId,
    recordType: '1',
  }
  proxy.download("/process/userDownloadInfo/download", params, item.title);
}
getList()
</script>

<style lang="scss" scoped>
.tab{
  width: 100px;
  height: 40px;
  border-radius: 5px;
  border: 1px solid #2F5AFF;
  color: #2F5AFF;
}
.active{
  background-color: #2F5AFF;
  color: #fff;
}

.top-icon{
  height: 16px;
}
</style>