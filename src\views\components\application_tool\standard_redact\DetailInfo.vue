<template>
  <div class="info">
    <div class="info-item">
      <div class="info-item-title">标准名称：</div>
      <div class="info-item-content">{{ form.standardName }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">标准号：</div>
      <div class="info-item-content">{{ form.standardCode }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">标准类型：</div>
      <div class="info-item-content">{{ form.standardTypeName }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">制修订：</div>
      <div class="info-item-content">{{ form.amendName }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">文稿版本：</div>
      <div class="info-item-content">{{ form.manuscriptVersionName }}</div>
    </div>
    <div v-if="form.amend == 1" class="info-item">
      <div class="info-item-title">修订标准号：</div>
      <div class="info-item-content">{{ form.revisionStandardCode }}</div>
    </div>
    <div v-if="form.standardType == 0" class="info-item">
      <div class="info-item-title">标准性质：</div>
      <div class="info-item-content">{{ form.standardAttrName }}</div>
    </div>
    <div v-if="form.standardType == 1" class="info-item">
      <div class="info-item-title">行业领域：</div>
      <div class="info-item-content">{{ form.industryCategory }}</div>
    </div>
    <div v-if="form.standardType == 2" class="info-item">
      <div class="info-item-title">所属地区：</div>
      <div class="info-item-content">{{ form.address }}</div>
    </div>
    <div v-if="form.standardType == 3" class="info-item">
      <div class="info-item-title">所属团体：</div>
      <div class="info-item-content">{{ form.associationName }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">CCS分类：</div>
      <div class="info-item-content">{{ form.standardTypeCodeGbInfo }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">ICS分类：</div>
      <div class="info-item-content">{{ form.standardTypeCodeIsoInfo }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">发布日期：</div>
      <div class="info-item-content">{{ form.publishDate }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">实施日期：</div>
      <div class="info-item-content">{{ form.executeDate }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">一致性国际文件：</div>
      <div class="info-item-content">{{ form.consistentInternationalDocument }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">一致性程度：</div>
      <div class="info-item-content">{{ form.consistencyLevel }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">所有者：</div>
      <div class="info-item-content">{{ form.createName }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">最后编写时间：</div>
      <div class="info-item-content">{{ form.finalWriteTime }}</div>
    </div>
    <div class="info-item">
      <div class="info-item-title">创建时间：</div>
      <div class="info-item-content">{{ form.createTime }}</div>
    </div>
  </div>
  <template v-if="form.archiveStatus == 1">
    <div class="h-title mt20">归档信息</div>
    <div class="info">
      <div class="info-item">
        <div class="info-item-title">归档状态：</div>
        <div class="info-item-content">{{ form.archiveStatusName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">归档人：</div>
        <div class="info-item-content">{{ form.archiveName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">归档时间：</div>
        <div class="info-item-content">{{ form.archiveTime }}</div>
      </div>
    </div>
  </template>
  <template v-if="form.storageStatus == 1">
    <div class="h-title mt20">数字标准入库信息</div>
    <div class="info">
      <div class="info-item">
        <div class="info-item-title">入库状态：</div>
        <div class="info-item-content">{{ form.storageStatusName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">关联标准：</div>
        <div class="info-item-content">{{ form.storageStandardCode }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">关联标准名称：</div>
        <div class="info-item-content">{{ form.storageStandardName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">操作人：</div>
        <div class="info-item-content">{{ form.storageName }}</div>
      </div>
      <div class="info-item">
        <div class="info-item-title">操作时间：</div>
        <div class="info-item-content">{{ form.storageTime }}</div>
      </div>
    </div>
  </template>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);
</script>

<style lang="scss" scoped>
  .info {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    &-item {
      width: 33%;
      display: flex;
      margin-top: 20px;

      &-title {
        width: 120px;
        color: #999;
        font-size: 14px;
        line-height: 25px;
        text-align: right;
      }

      &-content {
        flex: 1;
        color: #333;
        font-size: 14px;
        line-height: 25px;
        text-align: left;
      }
    }
  }
</style>
