<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="知识库设置"
      :before-close="handleClose"
      width="690px"
    >
      <el-tabs
        tab-position="left"
        v-model="activeName"
        @tab-click="handleClick"
      >
        <el-tab-pane name="set">
          <template #label>
            <span
              class="custom-tabs-label"
              :class="activeName == 'set' ? 'c-primary fw-b' : 'c-3E'"
            >
              <span class="iconfont icon-knowledge f-17"></span>
              <span class="ml5 f-16">知识库信息</span>
            </span>
          </template>
          <!-- <el-scrollbar height="500px"> -->
          <el-form
            :model="form"
            :label-position="'top'"
            ref="queryRef"
            :rules="rules"
            class="pl30 pr30 mt10"
          >
            <el-form-item label="知识库名称" prop="name">
              <el-input
                v-model="form.name"
                size="large"
                placeholder="请输入知识库名称"
                class="w-290"
              />
            </el-form-item>
            <el-form-item label="知识库封面" prop="cover">
              <image-cropper
                v-model:coverImage="form.cover"
                @uploadEnd="uploadEnd"
              />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input
                size="large"
                maxlength="4"
                v-model="form.sort"
                placeholder="请输入排序值，1-9999的正整数（按数值由大到小排序）"
              />
            </el-form-item>
            <el-form-item label="知识库描述">
              <el-input
                v-model="form.describe"
                :rows="5"
                :show-word-limit="true"
                maxlength="300"
                type="textarea"
                placeholder="请输入知识库描述说明信息"
              />
            </el-form-item>
            <el-form-item label="可见范围" prop="visibleRange">
              <el-select
                @change="handleSelect"
                v-model="form.visibleRange"
                size="large"
                class="w-290"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <!-- </el-scrollbar> -->

        </el-tab-pane>
        <el-tab-pane name="member" v-if="form.visibleRange == 1">
          <template #label>
            <span
              class="custom-tabs-label"
              :class="activeName == 'member' ? 'c-primary fw-b' : 'c-3E'"
            >
              <span class="iconfont icon-jiaoseguanli f-17"></span>
              <span class="ml5 f-16">成员管理</span>
            </span>
          </template>
          <el-scrollbar height="500px">
            <div class="pl30 pr30 mt20">
              <div class="f-16 c-3E fw-b">知识库成员</div>
              <b-tags v-model:tags="form.member" class="mt20" />
            </div>
          </el-scrollbar>
          
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click.prevent="handleConfirm" type="primary" :loading="loading">
            <span v-if="!loading">保存</span>
            <span v-else>保存中...</span>
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { detailKnowledge, editKnowledge } from "@/api/knowledge/library";
import BTags from "@/components/BTags";
import ImageCropper from "@/components/ImageCropper";
import { ElMessage, ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();

const props = defineProps({
  id: {
    type: Number,
    default: null,
  },
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { id, dialogVisible } = toRefs(props);

const data = reactive({
  loading: false,
  activeName: "set",
  form: {
    visibleRange: 1,
    member: [],
    sort: undefined,
  },
  options: [
    {
      value: 1,
      label: "私有：只有加入的成员才能看见此项目",
    },
    {
      value: 0,
      label: "公开：企业所有成员都可以看见此项目",
    },
  ],
  rules: {
    name: [
      {
        required: true,
        message: "请输入知识库名称",
        trigger: "blur",
      },
    ],
    cover: [
      {
        required: true,
        message: "请上传知识库封面",
        trigger: "change",
      },
    ],
    sort: [{ pattern: /^[1-9]{1}[0-9]{0,3}$/, message: "请输入1-9999的正整数", trigger: "blur" }]
  },
});
const { loading, activeName, form, options, rules } = toRefs(data);

onMounted(() => {
  getDetail();
});

const getDetail = () => {
  detailKnowledge(id.value).then((res) => {
    form.value = res.data;
  });
};

const handleSelect = (val) => {
  if (val == 0) {
    form.value.member = [];
  }
};

const handleClick = () => {};

const uploadEnd = () => {
  proxy.$refs.queryRef.validateField("cover");
};

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      editKnowledge(form.value)
        .then(() => {
          ElMessage({
            type: "success",
            message: "修改成功",
          });
          emit("getList");
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const handleClose = () => {
  emit("update:id", null);
  emit("update:dialogVisible", false);
};

const emit = defineEmits(["update:id", "update:dialogVisible", "getList"]);
</script>

<style lang="scss" scoped>
.el-tabs {
  min-height: 590px !important;
}

:deep(.el-tabs--left .el-tabs__header.is-left) {
  width: 180px !important;
  min-height: 590px;
  margin-right: 0 !important;
  background-color: #f8f9fd !important;
}

.custom-tabs-label {
  height: 60px !important;
  display: flex;
  align-items: center;
  padding-left: 25px !important;
  box-sizing: border-box;
}

:deep(.el-tabs__item) {
  padding: 0 !important;
}

:deep(.el-tabs__active-bar) {
  display: none;
}

.el-tabs--left :deep(.el-tabs__nav-wrap.is-left::after) {
  position: static !important;
  width: 0px !important;
}

.content {
  padding: 10px 30px;
  box-sizing: border-box;
}

:deep(.el-dialog__body) {
  padding: 0px !important;
}

.w-290 {
  width: 290px;
}

</style>