<template>
  <div v-if="form.phase == 1 && isAuth(form.authorizedUserIdList) && !props.isDetail" class="flex flex-sb mt30">
    <el-button @click="handleFinish" type="primary">
      <i class="iconfont icon-guanli"></i>
      复审组组建完成
    </el-button>
    <div class="flex flex-ai-center">
      <el-button @click="handleAddSystemPerson" icon="Plus">选择内部专家</el-button>
      <el-button @click="handleAddExternalPerson" icon="Plus">添加外部专家</el-button>
    </div>
  </div>
  <el-table :data="tableData" :border="true" :class="props.isDetail ? 'mt30' : 'mt15'">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column label="设为组长" show-overflow-tooltip min-width="100" fixed>
      <template #default="{ row }">
        <el-radio-group :disabled="form.phase != 1 || props.isDetail" v-model="row.isTeamLeader">
          <el-radio v-if="row.expertType == 0" label="1" @change="handleRadioChange(row)"></el-radio>
        </el-radio-group>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="姓名" show-overflow-tooltip fixed min-width="150" />
    <el-table-column prop="expertTypeName" label="专家类型" show-overflow-tooltip min-width="120" />
    <el-table-column prop="sexName" label="性别" show-overflow-tooltip min-width="100" />
    <el-table-column prop="contactNumber" label="联系方式" show-overflow-tooltip min-width="200" />
    <el-table-column prop="unit" label="所在部门/单位" show-overflow-tooltip min-width="200" />
    <el-table-column v-if="form.phase == 1 && !props.isDetail" label="操作" min-width="100" fixed="right">
      <template #default="{ row, $index }">
        <el-button @click="handleDelete(row, $index)" type="danger" link>删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
  <choose-person-dialog v-if="personVisible" v-model:visible="personVisible" :tags="tableData" @handleChoose="personInfo" />
  <add-external-person v-if="externalPersonVisible" v-model:visible="externalPersonVisible" @handleChoose="externalPersonInfo" />
</template>

<script setup>
  import { isAuth } from '@/utils/index';
  import {
    getReviewInfo,
    getExpertList,
    delExpert,
    addExpert,
    setTeamLeader,
    componentPersonnel,
  } from '@/api/standard_revision/review';
  import ChoosePersonDialog from '@/views/components/standard_revision/ChoosePersonDialog.vue';
  import AddExternalPerson from '@/views/components/standard_revision/AddExternalPerson.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [String, Number],
      required: true,
    },
    flowStatus: { type: Number },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const form = ref({});
  const selectedId = ref('');
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    reviewId: props.id,
  });
  const tableData = ref([]);
  const total = ref(0);
  const personVisible = ref(false);
  const externalPersonVisible = ref(false);

  const emit = defineEmits(['update:flowStatus']);

  getReviewInfo(props.id).then(res => {
    form.value = res.data;
  });

  const getList = () => {
    getExpertList(queryParams.value).then(res => {
      tableData.value = res.rows;
      total.value = res.total;
    });
  };

  getList();

  const handleAddSystemPerson = () => {
    personVisible.value = true;
  };

  const personInfo = data => {
    let arr = data
      .filter(item => item.userId != null)
      .map(item => {
        return item.userId;
      });
    addExpert({ reviewId: props.id, userIdList: arr, expertType: 0 }).then(() => {
      proxy.$modal.msgSuccess('添加成功！');
      getList();
    });
  };

  const handleAddExternalPerson = () => {
    externalPersonVisible.value = true;
  };

  const externalPersonInfo = data => {
    data.reviewId = props.id;
    data.expertType = 1;
    addExpert(data).then(() => {
      proxy.$modal.msgSuccess('添加成功！');
      getList();
    });
  };

  const handleRadioChange = row => {
    setTeamLeader(row.id).then(() => {
      proxy.$modal.msgSuccess('设置组长成功！');
    })
    .finally(() => {
      getList();
    })
  };

  const handleDelete = (row, index) => {
    if (selectedId.value == row.id) {
      proxy.$modal.msgWarning('当前成员为组长不可删除！');
    } else {
      delExpert({ ids: [row.id] }).then(() => {
        proxy.$modal.msgSuccess('删除成功！');
        getList();
      });
    }
  };

  const handleFinish = () => {
    proxy
      .$confirm('确认复审组已组建完成，并进入下一阶段“意见征集”？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        componentPersonnel(props.id).then(res => {
          proxy.$modal.msgSuccess('设置成功！');
          emit('update:flowStatus', 2);
        });
      })
      .catch(() => {});
  };
</script>

<style lang="scss" scoped>
  .icon-guanli {
    font-size: 14px;
    margin-right: 6px;
  }

  :deep(.el-radio__label) {
    display: none !important;
  }
</style>
