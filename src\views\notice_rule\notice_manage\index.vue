<template>
  <div class="app-container flex">
    <div class="left-wrap mr20">
      <div class="fw-b f-22 c-33 flex flex-sb flex-ai-center">
        类型目录
        <el-button type="primary" icon="Plus" @click="addType" v-hasPermi="['notice:notice_manage:addType']">新增</el-button>
      </div>
      <div class="notice-type-wrap fw-b c-33 scroller-bar-style">
        <div
          class="item flex flex-ai-center"
          :class="active == item.id ? 'active' : ''"
          v-for="item in typeDataList"
          :key="item.id"
          @click="chooseType(item)"
        >
          <img src="@/assets/images/notice_rule/file-icon.png" alt="" />
          <div class="ml-13 pointer flex-1 mr10 w-all">{{ item.name }}</div>
          <el-icon
            v-hasPermi="['notice:notice_manage:editType']"
            class="f-20 c-primary pointer mr10"
            @click.stop="editCatalog(item.id)"
          >
            <Edit />
          </el-icon>
          <el-icon
            v-hasPermi="['notice:notice_manage:deleteType']"
            class="f-20 pointer"
            @click.stop="delCatalog(item.id)"
            style="color: red"
          >
            <Delete />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="right-wrap flex-1 flex">
      <div class="mb20 app-container-search">
        <el-form label-width="auto" :model="params" ref="formRef" :inline="true" class="mb20">
          <el-form-item class="half-form-item" label="标题:" prop="title">
            <el-input @keyup.enter="handleQuery" v-model="params.title" placeholder="请输入标题" />
          </el-form-item>
          <el-form-item class="half-form-item" label="发布状态:" prop="publishStatus">
            <el-select v-model="params.publishStatus" placeholder="请选择发布状态">
              <el-option
                v-for="item in bxc_notice_laws_publish_status"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发布日期:" class="half-form-item">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDate"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <div class="search-bar-wrapper">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
      <div class="list-wrap">
        <div class="tool-wrap f-14 flex flex-jc-end">
          <el-button type="primary" plain icon="Plus" @click="addNotice" v-hasPermi="['notice:notice_manage:add']">
            新增
          </el-button>
          <el-button
            plain
            icon="Edit"
            :disabled="!currentRow || single || currentRow.publishStatus == 1"
            @click="handleUpdate"
            v-hasPermi="['notice:notice_manage:edit']"
          >
            修改
          </el-button>
          <el-button
            plain
            icon="Delete"
            :disabled="!currentRow || single"
            @click="handleDelete"
            v-hasPermi="['notice:notice_manage:delete']"
          >
            删除
          </el-button>
          <el-button
            plain
            :disabled="!currentRow || (currentRow.publishStatus == 1 && currentRow.isPutTop == 0) || currentRow.isPutTop == 1"
            @click="handlePublish"
            v-hasPermi="['notice:notice_manage:publish']"
          >
            <i class="iconfont icon-fabu1 mr5 f-14"></i>
            发布
          </el-button>
          <el-button
            plain
            :disabled="!currentRow || (currentRow.publishStatus == 0 && currentRow.isPutTop == 0) || currentRow.isPutTop == 1"
            @click="handlePublishCancel"
            v-hasPermi="['notice:notice_manage:publishCancel']"
          >
            <i class="iconfont icon-quxiaofabu mr5 f-14"></i>
            取消发布
          </el-button>
          <el-button
            plain
            :disabled="
              !currentRow || (currentRow.isPutTop == 1 && currentRow.publishStatus == 1) || currentRow.publishStatus == 0
            "
            @click="handleTop"
            v-hasPermi="['notice:notice_manage:top']"
          >
            <i class="iconfont icon-zhiding1 mr5 f-14"></i>
            置顶
          </el-button>
          <el-button
            plain
            :disabled="
              !currentRow || (currentRow.isPutTop == 0 && currentRow.publishStatus == 1) || currentRow.publishStatus == 0
            "
            @click="handleTopCancel"
            v-hasPermi="['notice:notice_manage:topCancel']"
          >
            <i class="iconfont icon-quxiaozhiding1 mr5 f-14"></i>
            取消置顶
          </el-button>
        </div>
        <el-table
          v-loading="loading"
          :data="dataList"
          :border="true"
          highlight-current-row
          @current-change="handleSelectionChange"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
            <template #default="scope">
              <i v-if="scope.row.isPutTop == 1" class="iconfont icon-zhiding1 c-primary f-20 f-bold"></i>
              <div v-else>{{ (params.pageNum - 1) * params.pageSize + scope.$index + 1 - isPutTopNums }}</div>
            </template>
          </el-table-column>

          <el-table-column prop="title" label="标题" show-overflow-tooltip min-width="200" />
          <el-table-column prop="scopeName" label="范围" show-overflow-tooltip min-width="150" />
          <el-table-column prop="pv" label="阅读总量" show-overflow-tooltip min-width="120" />
          <el-table-column prop="collectTotal" label="收藏总量" show-overflow-tooltip min-width="120" />
          <el-table-column prop="publishStatusName" label="发布状态" show-overflow-tooltip min-width="150">
            <template #default="scope">
              <el-tag :class="getStatusClass(scope.row.publishStatus)">{{ scope.row.publishStatusName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" label="发布时间" show-overflow-tooltip min-width="180" />
          <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="180" />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="params.pageNum"
          v-model:limit="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <notice-add-dialog v-if="addDialog" v-model:dialogVisible="addDialog" :id="currentId" :pid="params.pid" @getList="getList" />
    <notice-type-add-dialog v-if="typeAddDialog" v-model:dialogVisible="typeAddDialog" :id="currentId" @getList="getTypeList" />
  </div>
</template>

<script setup>
  import NoticeAddDialog from '@/views/components/notice_rule/NoticeAddDialog';
  import NoticeTypeAddDialog from '@/views/components/notice_rule/NoticeTypeAddDialog';
  import {
    getNoticeCatalogList,
    delNoticeCatalog,
    getNoticeManageList,
    noticePublish,
    noticePublishCancel,
    delNotice,
    noticePutTop,
    noticePutTopCancel,
  } from '@/api/notice_rule/manage.js';

  const { proxy } = getCurrentInstance();

  const { bxc_notice_laws_publish_status } = proxy.useDict('bxc_notice_laws_publish_status');

  const data = reactive({
    // 日期范围
    dateRange: [],
    loading: false,
    open: false,
    openAccept: false,
    currentId: undefined,
    total: 0,
    dataList: [],
    typeDataList: [],
    addDialog: false,
    typeAddDialog: false,
    params: {
      pageNum: 1,
      pageSize: 10,
      title: undefined,
      startTime: undefined,
      endTime: undefined,
      pid: null,
    },
    active: '',
    publishDisabled: true,
    noPublishDisabled: true,
    topDisabled: true,
    noTopDisabled: true,
    single: true,
    ids: [],
    currentRow: null,
    isPutTopNums: 0,
  });

  const {
    dateRange,
    loading,
    total,
    dataList,
    params,
    addDialog,
    typeAddDialog,
    typeDataList,
    currentId,
    active,
    single,
    ids,
    currentRow,
    isPutTopNums,
  } = toRefs(data);

  const resetQuery = () => {
    data.dateRange = [];
    proxy.resetForm('formRef');
    selectDate();
    handleQuery();
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    data.params.pageNum = 1;
    getList();
  };
  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.params.startTime = data.dateRange[0];
      data.params.endTime = data.dateRange[1];
    } else {
      data.params.startTime = undefined;
      data.params.endTime = undefined;
    }
  };

  //单行选中
  function handleSelectionChange(currentRow) {
    ids.value = [];
    data.currentRow = currentRow;
    if (currentRow) {
      ids.value.push(currentRow.id);
      data.single = false;
    }
  }

  //新增目录
  const addType = () => {
    data.currentId = null;
    data.typeAddDialog = true;
  };
  //获取目录列表
  const getTypeList = () => {
    getNoticeCatalogList(data.params).then(res => {
      data.typeDataList = res.rows;
      if (data.typeDataList && data.typeDataList.length > 0) {
        data.active = data.typeDataList[0].id;
        data.params.pid = data.typeDataList[0].id;
      }
      getList();
    });
  };

  //修改目录
  const editCatalog = id => {
    data.currentId = id;
    data.typeAddDialog = true;
  };

  //删除目录
  const delCatalog = id => {
    proxy
      .$confirm('您确定要删除该目录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        delNoticeCatalog(id).then(() => {
          proxy.$modal.msgSuccess('删除成功');
          getTypeList();
        });
      })
      .catch(() => {});
  };

  //选择目录
  const chooseType = item => {
    data.active = item.id;
    data.params.pid = item.id;
    data.params.pageNum = 1;
    getList();
  };

  //新增公告法规
  const addNotice = () => {
    data.currentId = null;
    data.addDialog = true;
  };

  //获取公告法规列表
  const getList = () => {
    data.loading = true;
    getNoticeManageList(data.params)
      .then(res => {
        let rows = res.rows;
        data.dataList = rows;
        if (data.dataList && data.dataList.length > 0 && data.params.pageNum == 1) {
          let arr = data.dataList.filter(item => item.isPutTop == 1);
          data.isPutTopNums = arr.length || 0;
        }
        data.total = res.total;
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };

  //编辑
  const handleUpdate = () => {
    data.currentId = ids.value[0];
    data.addDialog = true;
  };
  //发布
  const handlePublish = () => {
    proxy
      .$confirm('您确定要发布该公告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        noticePublish(data.ids).then(() => {
          proxy.$modal.msgSuccess('发布成功');
          getList();
        });
      })
      .catch(() => {});
  };
  //取消发布
  const handlePublishCancel = () => {
    proxy
      .$confirm('您确定要取消发布该公告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        noticePublishCancel(data.ids).then(() => {
          proxy.$modal.msgSuccess('取消发布成功');
          getList();
        });
      })
      .catch(() => {});
  };

  //置顶
  const handleTop = () => {
    proxy
      .$confirm('您确定要置顶该公告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        noticePutTop(data.ids).then(() => {
          proxy.$modal.msgSuccess('置顶成功');
          getList();
        });
      })
      .catch(() => {});
  };

  //取消置顶
  const handleTopCancel = () => {
    proxy
      .$confirm('您确定要取消置顶该公告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        noticePutTopCancel(data.ids).then(() => {
          proxy.$modal.msgSuccess('取消置顶成功');
          getList();
        });
      })
      .catch(() => {});
  };

  const handleDelete = () => {
    proxy
      .$confirm('您确定要删除该公告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        delNotice(data.ids).then(() => {
          proxy.$modal.msgSuccess('删除成功');
          data.single = true;
          getList();
        });
      })
      .catch(() => {});
  };

  const getStatusClass = status => {
    if (status == 0) {
      return 's-red';
    } else if (status == 1) {
      return 's-green';
    }
  };

  getTypeList();
</script>

<style lang="scss" scoped>
  .app-container {
    box-sizing: border-box;
    .left-wrap {
      box-sizing: border-box;
      flex: 0 0 24%;
      max-width: 25%;
      // height: calc(100vh - 130px);
      padding: 30px 20px;
      background-color: #fff;
      border-radius: 15px;
    }
    .right-wrap {
      flex-direction: column;
      flex: 0 0 75%;
      max-width: 75%;
      // height: calc(100vh - 130px);
      .search-wrap {
        box-sizing: border-box;
        height: 210px;
        padding: 30px 30px 20px 30px;
        background-color: #fff;
        border-radius: 15px;
      }
      .list-wrap {
        box-sizing: border-box;
        padding: 15px 30px;
        background-color: #fff;
        border-radius: 15px;
      }
    }
  }
  .notice-type-wrap {
    height: calc(100vh - 260px);
    overflow-y: auto;
    margin-top: 38px;
    .item {
      // margin-bottom: 24px;
      border-radius: 5px;
      padding: 10px;
      img {
        height: 15px;
      }
    }
  }
  .active {
    background-color: #e6ebff;
    color: #2f5aff;
  }

  .btn-icon {
    margin-right: 5px;
  }
  .top-icon {
    height: 16px;
  }
  .tool-wrap {
    margin-bottom: 15px;
    .btn-common {
      padding: 0 15px;
      height: 38px;
      border-radius: 5px;
      border: 1px solid #d2d9e9;
      cursor: pointer;
      img {
        height: 14px;
        margin-right: 5px;
      }
    }
    .add {
      background-color: #2f5aff;
    }
  }

  .publish-name {
    width: 60px;
    height: 26px;
    background-color: #dbfddd;
    border-radius: 5px;
    line-height: 26px;
    color: #04ca29;
  }
  .noPublish-name {
    width: 60px;
    height: 26px;
    background-color: #ffe4e4;
    border-radius: 5px;
    line-height: 26px;
    color: #ff0000;
  }

  .f-22 {
    font-size: 22px;
  }
  .ml-13 {
    margin-left: 13px;
  }

  .w-all {
    word-break: break-all;
  }
  :deep(.el-form-item) {
    margin-bottom: 0px !important;
    align-items: center;
  }
  :deep(.el-form .el-form-item__label) {
    font-size: 14px !important;
    font-weight: 400;
  }
  :deep(.left-wrap .el-button) {
    height: 34px;
  }
  :deep(.el-table .cell.el-tooltip) {
    display: flex;
    justify-content: center;
  }
</style>
