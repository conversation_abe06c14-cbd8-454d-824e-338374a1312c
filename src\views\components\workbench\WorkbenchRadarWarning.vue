<template>
  <div class="standard-query-wrap">
    <div class="flex flex-sb">
      <div class="f-22 f-bold c-33 mb10">雷达预警</div>
      <div class="f-14 c-33 mb10 flex flex-ai-center">
        {{warnTime}}
        <i class="iconfont icon-leida ml10 f-22 c-primary"></i>
        <el-button class="ml20" @click="handleMore" type="primary" link>
          更多<el-icon class="el-icon--right"><ArrowRight /></el-icon>
        </el-button>
      </div>
    </div>
    <div style="position: relative;">
      <el-tabs v-model="activeName" class="workbench-tabs" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in info" :key="index" :label="item.title" :name="index">
          <workbench-radar-warning-list :list="item.list" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import WorkbenchRadarWarningList from '@/views/components/workbench/WorkbenchRadarWarningList'
import { getRadarScanList,getRadarLatestTime } from '@/api/workbench'

const { proxy }= getCurrentInstance()

const data = reactive({
  activeName: 0,
  info: {},
  warnTime: ''
})
const { activeName,info,warnTime } = toRefs(data)
const handleClick = (val) => {
  
}
const handleMore = () => {
  if(data.activeName == 0){
    proxy.$router.push('/standard_manage/standard_radar/apply_warning')
  }else if(data.activeName == 1){
    proxy.$router.push('/standard_manage/standard_radar/abolish_warning')
  }else if(data.activeName == 2){
    proxy.$router.push('/standard_manage/standard_radar/replace_warning')
  }
  
}
const getData = () => {
  data.loading = true;
  getRadarScanList({pageSize: 5}).then((response) => {
    if(response.data){
      data.info = response.data
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const getTime = () => {
  getRadarLatestTime().then((response) => {
    if(response.data){
      data.warnTime = response.data
    }

  }).catch(() => {

  });
}
getData()
getTime()

</script>

<style lang="scss" scoped>

.more-btn{
  position: absolute;
  right: 0;
  top: 10px;
}
.standard-query-wrap {
  padding: 30px;
  flex: 1;
  height: 416px;
  background: #FFFFFF;
  border-radius: 15px;
  box-sizing: border-box;
  overflow: hidden;
}
</style>