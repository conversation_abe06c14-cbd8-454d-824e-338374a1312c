<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="指标名称" prop="indexName">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.indexName" placeholder="请输入指标名称" />
        </el-form-item>
        <el-form-item label="试验方法" prop="experimentalPlan">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.experimentalPlan" placeholder="请输入试验方法" />
        </el-form-item>
        <el-form-item label="源标准" prop="fromStandardCode">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.fromStandardCode" placeholder="请输入源标准" />
        </el-form-item>
        <el-form-item label="">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end flex-ai-center">
        <el-button @click="indicatorExtractionVisible = true" icon="Document" type="primary" class="mr10">指标提取</el-button>
      </div>
      <div>笔头代码为 UF:(0.5_{-0.1}^{0})N ，笔头代码为EF、F、M、B： (1_{-0.3}^{0})N</div>
      <el-table v-loading="loading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="80">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="indexName" label="指标名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexContent" label="指标内容" show-overflow-tooltip min-width="200" />
        <el-table-column prop="measuringUnit" label="计量单位" show-overflow-tooltip min-width="120" />
        <el-table-column prop="experimentalPlan" label="试验方法" show-overflow-tooltip min-width="200" />
        <el-table-column prop="standardObject" label="标准对象" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexObject" label="指标对象" show-overflow-tooltip min-width="200" />
        <el-table-column prop="indexInfluenceCount" label="指标影响因素" show-overflow-tooltip min-width="200" />
        <el-table-column prop="fromStandardCode" label="所属标准" show-overflow-tooltip min-width="200" />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="{ row }">
            <el-button @click="handleClick('detail', row)" type="primary" link>详情</el-button>
            <el-button @click="handleClick('edit', row)" type="primary" link>编辑</el-button>
            <el-button @click="handleClick('delete', row)" type="danger" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <indicator-extraction-dialog
      v-if="indicatorExtractionVisible"
      v-model:visible="indicatorExtractionVisible"
      @updateData="getData('pageNum')"
    />
    <add-dialog v-if="addVisible" v-model:visible="addVisible" :id="selectId" :title="'编辑指标'" @success="getData('pageNum')" />
    <detail-dialog v-if="detailVisible" v-model:visible="detailVisible" :id="selectId" />
  </div>
</template>

<script setup>
  import { getIndexLibList, deleteIndexLib } from '@/api/business_manage/standard_indicators';
  import useComparisonStore from '@/store/modules/comparison';
  import IndicatorExtractionDialog from '@/views/components/business_manage/standard_indicators/IndicatorExtractionDialog.vue';
  import AddDialog from '@/views/components/business_manage/standard_indicators/AddDialog.vue';
  import DetailDialog from '@/views/components/business_manage/standard_indicators/DetailDialog.vue';

  const { proxy } = getCurrentInstance();

  const loading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const selectId = ref('');
  const indicatorExtractionVisible = ref(false);
  const addVisible = ref(false);
  const detailVisible = ref(false);

  const getData = val => {
    loading.value = true;
    if (val) queryParams.value.pageNum = 1;
    getIndexLibList(queryParams.value)
      .then(res => {
        tableData.value = res.rows || [];
        total.value = res.total || 0;
        setTimeout(() => {
          MathJax.typesetPromise();
        }, 300);
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    getData('pageNum');
  };

  const handleClick = (type, row) => {
    switch (type) {
      case 'detail':
        selectId.value = row.id;
        detailVisible.value = true;
        break;
      case 'edit':
        selectId.value = row.id;
        addVisible.value = true;
        break;
      case 'delete':
        proxy
          .$confirm(`确认删除名称为【${row.indexName}】的指标？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            deleteIndexLib({ id: row.id }).then(res => {
              proxy.$modal.msgSuccess('删除成功！');
              getData();
            });
          });
        break;
      default:
        break;
    }
  };

  getData();
</script>

<style lang="scss" scoped></style>
