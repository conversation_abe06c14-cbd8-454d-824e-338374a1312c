<template>
  <el-dialog
    class="preview-wrap"
    append-to-body
    v-model="open"
    width="70%"
    title="查看"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="close"
  >
    <iframe :src="previewUrl" style="width: 100%; height: 100vh; border: none"></iframe>
  </el-dialog>
</template>

<script setup>
  import { base64Encode } from '@/utils/base64';

  const props = defineProps({
    open: Boolean,
    url: String,
  });
  const { open, url } = toRefs(props);
  const emit = defineEmits(['update:open']);

  const previewUrl = computed(() => {
    return `${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(base64Encode(props.url))}`;
  });

  const close = () => {
    emit('update:open', false);
  };
</script>

<style lang="scss">
  .preview-wrap .el-dialog__body {
    padding: 0 !important;
  }
</style>
