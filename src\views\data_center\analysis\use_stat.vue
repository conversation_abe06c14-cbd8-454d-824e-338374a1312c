<template>
  <div class="app-container">
    <div class="data-center-wrap">
      <div class="flex flex-center c-33 f-22">
        <div class="f-bold">标准使用统计分析</div>
      </div>
      <!-- 图表区 -->
      <div class="chart-list">
        <div v-for="(item, index) in chartList" :key="index" class="chart-item">
          <div class="h-title">{{ item.title }}</div>
          <data-center-stat-chart v-if="item.chartData && item.chartData.length > 0" :chart-data="item.chartData" />
          <empty v-else />
        </div>
      </div>
      <div class="flex flex-center mt40">
        <el-button @click="handleSearch(undefined, $event)" type="primary" :plain="currentStandardType != undefined">
          全部
        </el-button>
        <el-button
          v-for="item in bxc_standard_type"
          @click="handleSearch(item, $event)"
          type="primary"
          :plain="!(currentStandardType == item.value)"
        >
          {{ item.label }}
        </el-button>
      </div>
      <!-- 统计报表 -->
      <div class="stat-wrap mt40">
        <div class="h-title">标准使用TOP100统计报表</div>
        <div class="flex flex-jc-end">
          <el-button v-hasPermi="['data_center:use_stat:export']" @click="handleExport" type="primary">
            <i class="iconfont icon-daochu mr5"></i>
            导出
          </el-button>
        </div>
        <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15" :border="true">
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
            <template #default="scope">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="standardCode" label="标准编号" min-width="100" fixed="left" show-overflow-tooltip />
          <el-table-column prop="standardName" label="标准名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="standardTypeName" label="标准类型" min-width="100" show-overflow-tooltip />
          <el-table-column prop="standardStatusName" label="标准状态" min-width="100" show-overflow-tooltip />
          <el-table-column prop="pv" label="查看量" min-width="100" show-overflow-tooltip />
          <el-table-column prop="downloadTotal" label="下载量" min-width="100" show-overflow-tooltip />
          <el-table-column prop="collectCount" label="收藏量" min-width="100" show-overflow-tooltip />
          <el-table-column prop="allCount" label="合计总量" min-width="100" show-overflow-tooltip />
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getUseStatList, getUseStatChart } from '@/api/data_center/analysis';
  import DataCenterStatChart from '@/views/components/data_center/DataCenterStatChart';

  const { proxy } = getCurrentInstance();
  const { bxc_standard_type } = proxy.useDict('bxc_standard_type');

  const data = reactive({
    loading: false,
    total: 0,
    dataList: [],
    chartList: [
      {
        title: '合计TOP100统计分析',
        loading: false,
        type: 0,
        chartData: [],
      },
      {
        title: '标准查阅TOP100统计分析',
        loading: false,
        type: 1,
        chartData: [],
      },
      {
        title: '标准下载TOP100统计分析',
        loading: false,
        type: 2,
        chartData: [],
      },
      {
        title: '标准收藏TOP100统计分析',
        loading: false,
        type: 3,
        chartData: [],
      },
    ],
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      standardType: undefined,
    },
    currentStandardType: undefined,
  });
  const { loading, total, dataList, chartList, queryParams, currentStandardType } = toRefs(data);

  const getList = () => {
    data.loading = true;
    getUseStatList(data.queryParams)
      .then(response => {
        if (response.rows) {
          data.dataList = response.rows;
          data.total = response.total;
        }
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };
  const handleSearch = (item, e) => {
    data.queryParams.pageNum = 1;
    if (!item) {
      data.queryParams.standardType = undefined;
    } else {
      data.queryParams.standardType = item.value;
    }
    data.currentStandardType = data.queryParams.standardType;

    let target = e.target;
    if (target.nodeName == 'SPAN') {
      target = e.target.parentNode;
    }
    target.blur();

    getList();
  };
  const handleExport = () => {
    proxy.download(
      '/process/common/standard/query/statistics/use/export',
      {
        ...queryParams.value,
      },
      `标准使用统计分析_${new Date().getTime()}.xlsx`
    );
  };
  const getChartData = type => {
    let info = data.chartList[type];
    info.loading = true;
    getUseStatChart(type)
      .then(response => {
        if (response.data) {
          info.chartData = response.data;
        }
        info.loading = false;
      })
      .catch(() => {
        info.loading = false;
      });
  };
  for (let i = 0; i < 4; i++) {
    getChartData(i);
  }

  getList();
</script>

<style lang="scss" scoped>
  :deep(.el-button--primary.is-plain) {
    background-color: #fff;
    &:hover {
      color: $primary-color;
    }
  }
  .data-center-wrap {
    background: #ffffff;
    border-radius: 15px;
    // height: calc(100vh - 130px);
    padding: 30px;
    .chart-list {
      margin-top: 30px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 30px 0px;
      .chart-item {
        width: calc((100% - 20px) / 2);
      }
    }
  }
</style>
