<template>
  <div class="standard-query-list">
    <template v-if="list.length > 0">
      <div v-for="item in list" :key="item.id" @click="handleClick(item)" class="standard-query-item f-14">
        <div class="c-33 title overflow-ellipsis">{{item.title}}</div>
        <div class="num-wrap c-99 flex-shrink">
          {{item.publishDate}}
        </div>
      </div>
    </template>
    <template v-else>
      <empty />
    </template>
    <notice-detail-dialog
      v-if="open"
      :id="currentId"
      v-model:dialogVisible="open"
    />
  </div>
</template>

<script setup>
import NoticeDetailDialog from '@/views/components/notice_rule/NoticeDetailDialog'

const props = defineProps({
  list: {
    type: Array,
    default: []
  }
})
const { list } = toRefs(props)

const data = reactive({
  open: false,
  currentId: undefined
})

const {open,currentId} = toRefs(data)

const handleClick = (row) => {
  data.currentId = row.id
  data.open = true
}
</script>

<style lang="scss" scoped>
.standard-query-item {
  display: flex;
  height: 58px;
  line-height: 58px;
  border-bottom: 1px solid #E8E8E8;
  overflow: hidden;
  cursor: pointer;
  &:hover div{
    color: $primary-color !important;
  }
  .title{
    margin-right: auto;
    flex: 1;
  }
  .num-wrap{
    margin-left: 60px;
    width: 100px;
    text-align: right;
  }
  &:last-child{
    border-bottom:0;
  }
}
</style>