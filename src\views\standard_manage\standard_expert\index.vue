<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="姓名:" prop="name">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="工作单位:" prop="workUnit">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.workUnit" placeholder="请输入工作单位" />
        </el-form-item>
        <el-form-item label="性别:" prop="sex">
          <el-select clearable filterable v-model="queryParams.sex" placeholder="请选择性别">
            <el-option v-for="item in sys_user_sex" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否为外聘专家:" prop="isExternalExpert">
          <el-select clearable filterable v-model="queryParams.isExternalExpert" placeholder="请选择">
            <el-option v-for="item in yes_no" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期:">
          <el-date-picker
            v-model="dateRange"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="selectDate"
          />
        </el-form-item>
        <el-form-item label="" class="one-column">
          <el-button :loading="loading" @click="getList('pageNum')" type="primary"  icon="Search">查询</el-button>
          <el-button :loading="loading" plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button v-hasPermi="['standard_manage:standard_expert:add']" @click="handleAdd" type="primary" icon="Plus">
          新增专家
        </el-button>
      </div>
      <el-table v-loading="loading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" fixed label="姓名" show-overflow-tooltip min-width="150" />
        <el-table-column prop="sexName" label="性别" show-overflow-tooltip min-width="120" />
        <el-table-column prop="contactNumber" label="联系方式" show-overflow-tooltip min-width="180" />
        <el-table-column prop="workUnit" label="工作单位" show-overflow-tooltip min-width="200" />
        <el-table-column prop="job" label="职务" show-overflow-tooltip min-width="150" />
        <el-table-column prop="isExternalExpertName" label="是否为外聘专家" show-overflow-tooltip min-width="150" />
        <el-table-column prop="regionName" label="所在区域" show-overflow-tooltip min-width="200" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="180" />
        <el-table-column label="操作" min-width="200">
          <template #default="scope">
            <el-button
              @click.stop="handleEdit(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
              v-hasPermi="['standard_manage:standard_expert:edit']"
            >
              编辑
            </el-button>
            <el-button
              @click.stop="handleDetail(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
              v-hasPermi="['standard_manage:standard_expert:detail']"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleDel(scope.row)"
              link
              size="small"
              v-hasPermi="['standard_manage:standard_expert:delete']"
              class="f-14 c-F20000"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <expert-add-dialog v-if="addDialogVisible" v-model:addDialogVisible="addDialogVisible" :id="currentId" @getList="getList" />
    <expert-detail-dialog v-if="dialogVisible" v-model:dialogVisible="dialogVisible" :id="currentId" />
  </div>
</template>

<script setup>
  import { stcExpertList, delStcExpert } from '@/api/standard_manage/standard_expert.js';
  import ExpertAddDialog from '@/views/components/standard_manage/ExpertAddDialog.vue';
  import ExpertDetailDialog from '@/views/components/standard_manage/ExpertDetailDialog.vue';

  const { proxy } = getCurrentInstance();

  const { sys_user_sex, yes_no } = proxy.useDict('sys_user_sex', 'yes_no');

  const data = reactive({
    dialogVisible: false,
    addDialogVisible: false,
    currentId: null,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    tableData: [],
    total: 0,
    loading: false,
    ids: [],
    dateRange: [],
  });

  const { dialogVisible, addDialogVisible, currentId, loading, queryParams, tableData, total, ids, dateRange } = toRefs(data);

  const getList = val => {
    loading.value = true;
    stcExpertList(queryParams.value)
      .then(res => {
        let rows = res.rows;
        rows.forEach(item => {
          item.selected = false;
        });
        tableData.value = rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    data.dateRange = [];
    selectDate();
    getList('pageNum');
  };

  const handleAdd = () => {
    currentId.value = null;
    addDialogVisible.value = true;
  };

  const handleEdit = row => {
    currentId.value = row.id;
    addDialogVisible.value = true;
  };
  const handleDetail = row => {
    currentId.value = row.id;
    dialogVisible.value = true;
  };

  const handleDel = row => {
    ids.value.push(row.id);
    proxy
      .$confirm('确认是否删除此条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        delStcExpert({ ids: ids.value })
          .then(res => {
            proxy.$modal.msgSuccess('专家删除成功！');
          })
          .finally(() => {
            getList();
          });
      });
  };

  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.queryParams.startTime = data.dateRange[0];
      data.queryParams.endTime = data.dateRange[1];
    } else {
      data.queryParams.startTime = undefined;
      data.queryParams.endTime = undefined;
    }
  };

  getList();
</script>

<style lang="scss" scoped>
  .icon-piliangruku:before,
  .icon-daoru:before {
    margin-right: 6px;
  }
</style>
