<template>
  <el-dialog
    width="1200px"
    title="制修订管理"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="tabs flex flex-sa flex-ai-center">
      <div
        @click="handleCut(item)"
        v-for="item in tabList"
        :key="item.value"
        class="tabs-item pointer"
        :class="activeTab == item.value ? 'tabs-active' : ''"
      >
        {{ item.title }}
      </div>
    </div>
    <el-steps :active="activeStep" align-center class="mt30 mb30">
      <el-step v-for="(item, index) in stepList" :key="index" :title="item.title"></el-step>
    </el-steps>
    <component
      :is="subcomponent"
      :key="subcomponentKey"
      v-model:activeStep="activeStep"
      v-model:activeTab="activeTab"
      :id="props.id"
    ></component>
  </el-dialog>
</template>

<script setup>
  import { getRevisionDetail } from '@/api/standard_revision/manage';
  import RevisionInfo from '@/views/components/standard_revision/RevisionInfo.vue';
  import RevisionDraft from '@/views/components/standard_revision/RevisionDraft.vue';
  import RevisionOpinion from '@/views/components/standard_revision/RevisionOpinion.vue';
  import RevisionExamine from '@/views/components/standard_revision/RevisionExamine.vue';
  import RevisionApproval from '@/views/components/standard_revision/RevisionApproval.vue';
  import RevisionPublish from '@/views/components/standard_revision/RevisionPublish.vue';
  import RevisionLog from '@/views/components/standard_revision/RevisionLog.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const subcomponentKey = ref('info');
  const activeTab = ref(1);
  const activeStep = ref(1);
  const tabList = reactive([
    { title: '项目信息', value: 1 },
    { title: '标准草案', value: 2 },
    { title: '征求意见稿', value: 3 },
    { title: '标准审查', value: 4 },
    { title: '标准报批', value: 5 },
    { title: '标准发布', value: 6 },
    { title: '流程日志', value: 7 },
  ]);
  const stepList = reactive([
    { title: '项目立案', value: 1 },
    { title: '标准草案', value: 2 },
    { title: '意见征求', value: 3 },
    { title: '标准审查', value: 4 },
    { title: '标准报批', value: 5 },
    { title: '标准发布', value: 6 },
  ]);

  let subcomponent = computed(() => {
    switch (activeTab.value) {
      case 1:
        return RevisionInfo;
        break;
      case 2:
        return RevisionDraft;
        break;
      case 3:
        return RevisionOpinion;
        break;
      case 4:
        return RevisionExamine;
        break;
      case 5:
        return RevisionApproval;
        break;
      case 6:
        return RevisionPublish;
        break;
      case 7:
        return RevisionLog;
        break;
      default:
        break;
    }
  });

  getRevisionDetail(props.id).then(res => {
    let data = res.data;
    activeTab.value = Number(data.status);
    activeStep.value = Number(data.status);
  });

  const handleCut = item => {
    subcomponentKey.value = item.value;
    activeTab.value = item.value;
  };

  const handleClose = () => {
    emit('updateData');
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped>
  .tabs {
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    color: #333333;
    background-color: #f3f6fd;
    border-radius: 5px;

    &-item {
      position: relative;
    }

    &-active {
      color: $primary-color !important;
      font-weight: 600 !important;

      &:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 3px;
        background-color: $primary-color;
        border-radius: 2px;
      }
    }
  }

  .mb30 {
    margin-bottom: 30px;
  }

  :deep(.el-step__head.is-process) {
    color: #cacaca !important;
    border-color: #cacaca !important;
  }

  :deep(.el-step__head.is-wait) {
    color: #cacaca !important;
    border-color: #cacaca !important;
  }

  :deep(.el-step__title) {
    font-size: 14px !important;
  }

  :deep(.el-step__title.is-process) {
    color: #333333 !important;
    font-weight: 400 !important;
  }

  :deep(.el-step__title.is-wait) {
    color: #333333 !important;
    font-weight: 400 !important;
  }

  :deep(.el-step__line) {
    height: 3px !important;
    background-color: #e5e8ef !important;
  }

  :deep(.el-step__icon-inner) {
    display: none !important;
  }

  :deep(.el-step__icon.is-text) {
    border-width: 4px !important;
  }

  :deep(.el-step__title.is-finish) {
    font-weight: 600 !important;
  }

  :deep(.el-step__icon) {
    width: 18px !important;
    height: 18px !important;
  }

  :deep(.el-step.is-horizontal .el-step__line) {
    top: 8px !important;
  }
</style>
