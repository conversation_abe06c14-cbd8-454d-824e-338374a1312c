<template>
  <div>
    <el-dialog
      v-model="open"
      :title="title"
      :before-close="close"
      width="60%"
    >
      <approve-apply-info />
      <approve-revision-info class="mt20" />
      <div class="c-33 f-18 f-bold mt20">审批记录</div>
      <approve-audit-process class="mt15" />

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button
            @click="save"
            :loading="loading"
            type="primary"
            
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getStandardApplicationDetail,addStandardApplication,updateStandardApplication } from "@/api/standard_manage/standard_application";
import ApproveApplyInfo from '@/views/components/approve/ApproveApplyInfo'
import ApproveRevisionInfo from '@/views/components/approve/ApproveRevisionInfo'
import ApproveAuditProcess from '@/views/components/approve/ApproveAuditProcess'

const { proxy } = getCurrentInstance();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Number,
    default: null,
  }
});
const { open, id } = toRefs(props);

const data = reactive({
  loading: false,
  title: '审批',
  form: {
    id: props.id,
    viewScope: '0',
    userList: [],
    deptList: [],
    sort: undefined,
  },
  
  rules: {
    applyName: [
      {
        required: true,
        message: "请输入标准应用名称",
        trigger: "blur",
      },
    ],
    
  },
});
const { loading, title, form, rules } = toRefs(data);


const save = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if(data.form.id){
        updateStandardApplication(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess('设置成功')
          close();
        })
        .finally(() => {
          loading.value = false;
        });
      }else{
        addStandardApplication(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess('新建成功')
          close();
        })
        .finally(() => {
          loading.value = false;
        });
      }
    }
  });
};

const close = () => {
  emit("success");
  emit("update:open", false);
};

const emit = defineEmits(["update:open", "success"]);

const getData = id => {
  data.loading = true;

  getStandardApplicationDetail(id).then(response => {
    if (response.data) {
      data.form = response.data;
    }
    data.loading = false;
  }).catch(() => {
    data.loading = false;
  });
}


</script>

<style lang="scss" scoped>
</style>