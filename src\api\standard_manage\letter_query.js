import request from '@/utils/request'

//函文查询

//新增
export const addStandardLetter = (data) => {
  return request({
    url: '/process/standardLetter',
    method: 'post',
    data
  })
}

//列表
export const standardLetterList = (params) => {
  return request({
    url: '/process/standardLetter/list',
    method: 'get',
    params
  })
}

//详情
export const getStandardLetterDetail = (id) => {
  return request({
    url: '/process/standardLetter/' + id,
    method: 'get'
  })
}

//编辑
export const editStandardLetter = (data) => {
  return request({
    url: '/process/standardLetter',
    method: 'put',
    data
  })
}

//删除
export const delStandardLetter = (data) => {
  return request({
    url: '/process/standardLetter/remove',
    method: 'delete',
    data
  })
}