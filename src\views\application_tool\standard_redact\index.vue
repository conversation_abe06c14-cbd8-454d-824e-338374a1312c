<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="标准号:" prop="standardCode">
          <el-input v-model="queryParams.standardCode" placeholder="请输入标准号" @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="标准名称:" prop="standardName">
          <el-input v-model="queryParams.standardName" placeholder="请输入标准名称" @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="身份角色:" prop="teamMemberRole">
          <el-select v-model="queryParams.teamMemberRole" placeholder="请选择身份角色" clearable>
            <el-option v-for="dict in bxc_standard_write_member_role" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="归档状态:" prop="archiveStatus">
          <el-select v-model="queryParams.archiveStatus" placeholder="请选择归档状态" clearable>
            <el-option v-for="dict in bxc_archived_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="入库状态:" prop="storageStatus">
          <el-select v-model="queryParams.storageStatus" placeholder="请选择入库状态" clearable>
            <el-option v-for="dict in bxc_storage_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="最后编写日期:">
          <el-date-picker
            v-model="redactTime"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="创建日期:">
          <el-date-picker
            v-model="createTime"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
          <el-button plain icon="Refresh" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button v-hasPermi="['application_tool:standard_redact:list']" type="primary" @click="handleBtn('add')">
          <i class="iconfont icon-bianji1 mr6"></i>
          编写新标准
        </el-button>
        <el-button
          v-hasPermi="['application_tool:standard_redact:team']"
          :disabled="!currentRow || currentRow.teamMemberRoleId != 1 || currentRow.archiveStatus == 1"
          @click="handleBtn('team')"
        >
          <i class="iconfont icon-jiaoseguanli mr6"></i>
          团队管理
        </el-button>
        <el-button
          v-hasPermi="['application_tool:standard_redact:archive']"
          :disabled="!currentRow || currentRow.teamMemberRoleId != 1 || currentRow.archiveStatus != 0"
          @click="handleBtn('archive')"
        >
          <i class="iconfont icon-guidang mr6"></i>
          归档
        </el-button>
        <el-button
          v-hasPermi="['application_tool:standard_redact:warehouse']"
          :disabled="
            !currentRow || currentRow.teamMemberRoleId != 1 || currentRow.archiveStatus == 0 || currentRow.storageStatus == 1
          "
          @click="handleBtn('warehouse')"
        >
          <i class="iconfont icon-ruku mr6"></i>
          数字标准入库
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        :border="true"
        highlight-current-row
        @current-change="handleCurrentChange"
        class="mt15"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" width="60" fixed>
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="标准名称" min-width="200" show-overflow-tooltip fixed>
          <template #default="{ row }">
            <span @click="handleBtn('detail', row)" class="c-primary pointer">{{ row.standardName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" label="标准号" min-width="200" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="120" show-overflow-tooltip />
        <el-table-column prop="amendName" label="制修订" min-width="120" show-overflow-tooltip />
        <el-table-column prop="teamMemberCount" label="团队成员" min-width="120" show-overflow-tooltip />
        <el-table-column prop="teamMemberRoleName" label="身份角色" min-width="150" show-overflow-tooltip />
        <el-table-column prop="manuscriptVersionName" label="文稿版次" min-width="150" show-overflow-tooltip />
        <el-table-column label="归档状态" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="row.archiveStatus == 0 ? 'status-red' : 'status-green'">{{ row.archiveStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="数字标准入库" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="row.storageStatus == 0 ? 'status-red' : 'status-green'">{{ row.storageStatusName }}</span>
            <el-tooltip v-if="row.storageFailInfo" :content="row.storageFailInfo" placement="top-start">
              <el-icon class="c-99 ml5"><InfoFilled /></el-icon>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="所有者" min-width="150" show-overflow-tooltip />
        <el-table-column prop="standardTypeCodeGb" label="CCS分类" min-width="150" show-overflow-tooltip />
        <el-table-column prop="standardTypeCodeIso" label="ICS分类" min-width="150" show-overflow-tooltip />
        <el-table-column prop="finalWriteTime" label="最后编写时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" min-width="180" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <el-button
              v-hasPermi="['application_tool:standard_redact:review']"
              v-if="row.teamMemberRoleId == 3 && row.archiveStatus == 0"
              @click="handleBtn('review', row)"
              link
              type="primary"
            >
              批阅
            </el-button>
            <el-button
              v-hasPermi="['application_tool:standard_redact:redact']"
              v-if="(row.teamMemberRoleId == 1 || row.teamMemberRoleId == 2) && row.archiveStatus == 0"
              @click="handleBtn('redact', row)"
              link
              type="primary"
            >
              编写
            </el-button>
            <el-button
              v-hasPermi="['application_tool:standard_redact:set']"
              v-if="row.teamMemberRoleId == 1 && row.archiveStatus == 0"
              @click="handleBtn('set', row)"
              link
              type="primary"
            >
              设置
            </el-button>
            <el-button
              v-hasPermi="['application_tool:standard_redact:delete']"
              v-if="row.teamMemberRoleId == 1 && row.archiveStatus == 0"
              @click="handleBtn('delete', row)"
              link
              type="danger"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <add-dialog v-if="addVisible" v-model:visible="addVisible" :id="selectId" @updateData="handleSearch" />
    <standard-warehousing-dialog
      v-if="standardWarehousingVisible"
      v-model:visible="standardWarehousingVisible"
      :id="selectId"
      @updateData="getList"
    />
    <team-drawer
      v-if="teamVisible"
      v-model:visible="teamVisible"
      :id="selectId"
      :standardName="selectStandardName"
      @updateData="getList"
    />
    <detail-drawer v-if="detailVisible" v-model:visible="detailVisible" :id="selectId" />
  </div>
</template>

<script setup>
  import { getStandardRedactList, deleteStandardRedact, putStandardRedact } from '@/api/application_tool/standard_redact';
  import AddDialog from '@/views/components/application_tool/standard_redact/AddDialog.vue';
  import StandardWarehousingDialog from '@/views/components/application_tool/standard_redact/StandardWarehousingDialog.vue';
  import TeamDrawer from '@/views/components/application_tool/standard_redact/TeamDrawer.vue';
  import DetailDrawer from '@/views/components/application_tool/standard_redact/DetailDrawer.vue';

  const { proxy } = getCurrentInstance();
  const { bxc_standard_write_member_role, bxc_archived_status, bxc_storage_status } = proxy.useDict(
    'bxc_standard_write_member_role',
    'bxc_archived_status',
    'bxc_storage_status'
  );

  const queryFormRef = ref();
  const loading = ref(false);
  const redactTime = ref([]);
  const createTime = ref([]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const currentRow = ref({});
  const selectId = ref('');
  const selectStandardName = ref('');
  const addVisible = ref(false);
  const standardWarehousingVisible = ref(false);
  const teamVisible = ref(false);
  const detailVisible = ref(false);

  onMounted(() => {
    getList();
  });

  const handleSearch = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const handleReset = () => {
    redactTime.value = [];
    createTime.value = [];
    queryFormRef.value?.resetFields();
    handleSearch();
  };

  const getList = () => {
    loading.value = true;
    currentRow.value = {};
    queryParams.value.finalWriteStartTime = redactTime.value.length > 0 ? redactTime.value[0] : '';
    queryParams.value.finalWriteEndTime = redactTime.value.length > 0 ? redactTime.value[1] : '';
    queryParams.value.createStartTime = createTime.value.length > 0 ? createTime.value[0] : '';
    queryParams.value.createEndTime = createTime.value.length > 0 ? createTime.value[1] : '';
    getStandardRedactList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleCurrentChange = val => {
    currentRow.value = val;
  };

  const handleBtn = (type, row) => {
    switch (type) {
      case 'detail':
        selectId.value = row.id;
        detailVisible.value = true;
        break;
      case 'add':
        selectId.value = '';
        addVisible.value = true;
        break;
      case 'set':
        selectId.value = row.id;
        addVisible.value = true;
        break;
      case 'team':
        selectId.value = currentRow.value.id;
        selectStandardName.value = currentRow.value.standardName;
        teamVisible.value = true;
        break;
      case 'archive':
        proxy
          .$confirm('确认将标准' + currentRow.value.standardName + '归档，归档后标准将不可再编写？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            putStandardRedact(currentRow.value.id).then(() => {
              proxy.$modal.msgSuccess('归档成功！');
              getList();
            });
          })
          .catch(() => {});
        break;
      case 'warehouse':
        selectId.value = currentRow.value.id;
        standardWarehousingVisible.value = true;
        break;
      case 'redact':
        window.open(`${window.location.origin}/editor?id=${row.id}`, '_blank');
        break;
      case 'review':
        window.open(`${window.location.origin}/editor?id=${row.id}`, '_blank');
        break;
      case 'delete':
        proxy
          .$confirm('确认删除名称【' + row.standardName + '】的编写标准？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            deleteStandardRedact({ ids: [row.id] }).then(() => {
              proxy.$modal.msgSuccess('删除成功！');
              nextTick(() => {
                getList();
              });
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };
</script>

<style lang="scss" scoped></style>
