<template>
  <div class="content-main-wrap">
    <div class="main-header-box">
      <div class="main-header-title">
        {{info.name || '-'}}
      </div>
    </div>
    <div class="main-header-desc">
      {{info.describe || '-'}}
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="全部文档" name="allDoc">
        <knowledge-main-doc v-if="activeName == 'allDoc'" type="0" @itemClick="docClick"></knowledge-main-doc>
      </el-tab-pane>
    </el-tabs>

    <set-knowledge-dialog
      v-if="openSet"
      v-model:id="info.id"
      v-model:dialogVisible="openSet"
      @getList="refreshData"
    />
  </div>
</template>

<script setup>
import { delKnowledge } from "@/api/knowledge/library";
import SetKnowledgeDialog from "@/views/components/knowledge/library/SetKnowledgeDialog";
import KnowledgeMainDoc from '@/views/components/knowledge/main/KnowledgeMainDoc'

const { proxy } = getCurrentInstance()

const props = defineProps({
  info: Object
})

const {info} = toRefs(props)

const data = reactive({
  activeName: 'allDoc',
  openSet: false
})

const {activeName,openSet} = toRefs(data)

const commonInfo = inject('commonInfo')

const handleCommand = (command) => {
  switch (command) {
    case "setLibrary":
    setLibrary()
      break;
    case "deleteLibrary":
    deleteLibrary()
      break;
    default:
      break;
  }
}

const setLibrary = () => {
  data.openSet = true
}
const deleteLibrary = () => {
  let tip = '您确定要删除该知识库和该知识库下的所有文件吗?删除后将不可恢复!'
  proxy.$modal.confirm(tip,'提示').then(function () {
    return delKnowledge(props.info.id);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    const obj = { path: "knowledge/library" };
    proxy.$tab.closeOpenPage(obj);
  }).catch(() => {
    
  });
}

const refreshData = () => {
  commonInfo.refreshDetail()
}
const docClick = (param) => {
  commonInfo.isHome = false
  commonInfo.fileId = param.id
}

</script>

<style lang="scss" scoped>
.content-main-wrap{
  .main-header-box{
    display: flex;
    justify-content: space-between;
    .main-header-title{
      flex: 1;
    }
    .main-header-op{
      width: 200px;
      text-align: right;
    }
  }
  .main-header-desc{
    margin-top: 10px;
    padding: 10px 20px;
    background: #F8F9FD;
    border-radius: 3px;
    font-size: 14px;
    color: #3E4967;
  }
  :deep(.el-tabs__header){
    margin: 0px !important;
  }
}
</style>