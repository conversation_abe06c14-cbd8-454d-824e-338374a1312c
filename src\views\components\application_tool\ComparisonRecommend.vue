<template>
  <div class="comparison-recommend-wrap">
    <div class="recommend-title">推荐比对标准</div>
    <div class="recommend-content">
      <template v-if="recommendInfo.beReplacedStandardList && recommendInfo.beReplacedStandardList.length > 0">
        <div class="title">被替代标准</div>
        <ul class="s-list">
          <li v-for="item in recommendInfo.beReplacedStandardList" :key="item.id" @click="handleSelected(item)" class="s-item">{{'【'+item.standardStatusName+'】'+item.standardCode + ' | '+ item.standardName }}</li>
        </ul>
      </template>
      <template v-if="recommendInfo.replaceStandardList && recommendInfo.replaceStandardList.length > 0">
        <div class="title">被替代标准</div>
        <ul class="s-list">
          <li v-for="item in recommendInfo.replaceStandardList" :key="item.id" @click="handleSelected(item)" class="s-item">{{'【'+item.standardStatusName+'】'+item.standardCode + ' | '+ item.standardName }}</li>
        </ul>
      </template>
    </div>
  </div>
</template>
<script setup>
import useComparisonStore from '@/store/modules/comparison'

const comparisonStore = useComparisonStore()
const { recommendInfo } = storeToRefs(comparisonStore)

const emit = defineEmits(['selected'])

const handleSelected = (item) => {
  emit('selected', item)
  // 确保任何可能获取焦点的元素都失去焦点
  document.activeElement?.blur();
}

</script>
<style lang="scss" scoped>
.comparison-recommend-wrap{
  .recommend-title{
    height: 58px;
    line-height: 58px;
    font-size: 20px;
    color: #333333;
    font-weight: bold;
    padding: 0px 25px;
    border-bottom: 1px solid #E5E8EF;
  }
  .recommend-content{
    padding: 20px 25px;
    .title{
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      position: relative;
      padding-left: 12px;
      &::before{
        content: '';
        display: inline-block;
        position: absolute;
        width: 4px;
        height: 16px;
        left: 0px;
        top: 2px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
    ul,li{
      list-style: none;
      padding: 0px;
      margin: 0px;
    }
    .s-list{
      display: flex;
      flex-direction: column;
      padding: 0px 0px 12px 0px;
      .s-item{
        margin-top: 8px;
        font-size: 14px;
        color: #333333;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:hover{
          color: $primary-color;
        }
      }
    }
  }
}
</style>