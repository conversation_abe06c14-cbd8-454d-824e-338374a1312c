<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="流程名称:" prop="procDefName">
          <el-input
            v-model="queryParams.procDefName"
            placeholder="请输入流程名称"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发起人:" prop="processCreateUserId">
          <el-select
            v-model="queryParams.processCreateUserId"
            :remote-method="getPersonList"
            value-key="userId"
            filterable
            remote
            clearable
            placeholder="请搜索和选择发起人"
          >
            <el-option v-for="item in personList" :key="item.userId" :value="item.userId" :label="item.nickName" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批状态:" prop="processStatus">
          <el-select
            v-model="queryParams.processStatus"
            placeholder="请选择审批状态"
            clearable
          >
            <el-option
              v-for="dict in flowable_process_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批时间:">
          <el-date-picker
            v-model="auditDateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectAuditDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="指派时间:">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <div class="container-bar">
        <div class="bar-right">
        </div>
      </div>
      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="dataList"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column 
          type="index"
          align="center" 
          label="序号" 
          fixed="left"  
          min-width="55"
        >
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="procDefName"
          label="审批流程名称"
          min-width="200"
          fixed="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="startUserName"
          label="发起人"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="startDeptName"
          label="所在部门"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="processStatusName"
          label="审批状态"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span :class="`c-${scope.row.processStatus}`">{{scope.row.processStatusName}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="processCreateTime"
          label="发起时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createTime"
          label="指派时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="finishTime"
          label="审批时间"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="duration"
          label="流程耗时"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button
              @click.stop="handleView(scope.row)"
              v-hasPermi="['approve:approve_record:view']"
              link
              size="small"
              class="f-14 c-primary"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleProcess(scope.row)"
              v-hasPermi="['approve:approve_record:process']"
              link
              size="small"
              class="f-14 c-primary"
            >
              审批流程
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

     <!-- 审批流程图弹框 -->
     <approve-process-view v-if="openProcess" v-model:open="openProcess" :procInsId="currentInfo.procInsId" />
      <!-- 申请详情弹框 -->
      <pop-apply-detail v-if="open" v-model:open="open" :id="currentInfo.procInsId"/>
  </div>
</template>

<script setup>
import { getApproveRecordList } from '@/api/approve/approve_record'
import { getUserList } from '@/api/common'
import ApproveProcessView from '@/views/components/approve/ApproveProcessView'
import PopApplyDetail from '@/views/components/approve/PopApplyDetail'

const { proxy } = getCurrentInstance()

const {flowable_process_status} = proxy.useDict('flowable_process_status')

const data = reactive({
  loading: false,
  dateRange: [],
  auditDateRange: [],
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    procDefName: undefined,
    processCreateUserId: undefined,
    processStatus: undefined,
    processStartTime: undefined,
    processEndTime: undefined,
    taskFinishStartTime: undefined,
    taskFinishEndTime: undefined,
  },
  personList: [],
  openProcess: false,
  open: false,
  currentInfo: {}
});
const {
  loading,
  dateRange,
  auditDateRange,
  total,
  dataList,
  queryParams,
  personList,
  openProcess,
  open,
  currentInfo
} = toRefs(data);
const resetQuery = () => {
  data.dateRange = [];
  data.auditDateRange = [];
  proxy.resetForm("queryFormRef");
  selectDate();
  selectAuditDate();
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.queryParams.pageNum = 1;
  getList();
}

const getList = () => {
  data.loading = true;
  getApproveRecordList(data.queryParams).then((response) => {
    if(response.rows){
      data.dataList = response.rows
      data.total = response.total
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const selectDate = () => {
  if (data.dateRange && data.dateRange.length > 0) {
    data.queryParams.startTime = data.dateRange[0];
    data.queryParams.endTime = data.dateRange[1];
  } else {
    data.queryParams.startTime = undefined;
    data.queryParams.endTime = undefined;
  }
}
const selectAuditDate = () => {
  if (data.auditDateRange && data.auditDateRange.length > 0) {
    data.queryParams.taskFinishStartTime = data.auditDateRange[0];
    data.queryParams.taskFinishEndTime = data.auditDateRange[1];
  } else {
    data.queryParams.taskFinishStartTime = undefined;
    data.queryParams.taskFinishEndTime = undefined;
  }
}
const getPersonList = (query) => {
  getUserList({nickName: query,pageSize:10,pageNum:1}).then((response) => {
    if(response.rows){
      data.personList = response.rows
    }
  }).catch(() => {

  });
}
const handleProcess = (row) => {
  data.currentInfo = row
  data.openProcess = true
}
const handleView = (row) => {
  data.currentInfo = row
  data.open = true
}

getList()
getPersonList()
</script>

<style lang="scss" scoped>

</style>