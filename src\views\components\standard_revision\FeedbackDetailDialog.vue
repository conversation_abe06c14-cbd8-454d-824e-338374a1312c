<template>
  <el-dialog
    width="1000px"
    title="征求意见详情"
    append-to-body
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="h-title">反馈意见</div>
    <el-descriptions :column="2" size="large" class="br-5 mt20">
      <el-descriptions-item label="反馈用户：" label-align="left">
        {{ form.feedbackUser || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="所属部门：" label-align="left">
        {{ form.feedbackDept || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="反馈时间：" label-align="left">
        {{ form.feedbackTime || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="意见类别：" label-align="left">
        {{ form.opinionTypeName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="意见内容：" :span="2" label-align="left">
        {{ form.opinionContent || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="附件：" :span="2" label-align="left">
        <ele-upload-image
          v-if="form.feedbackFileList && form.feedbackFileList.length > 0"
          :isShowUploadIcon="false"
          v-model:value="form.feedbackFileList"
          class="mt10"
        />
      </el-descriptions-item>
    </el-descriptions>
    <template v-if="form.processResult != 0">
      <div class="h-title mt20">反馈处理</div>
      <el-descriptions :column="2" size="large" class="br-5 mt20">
        <el-descriptions-item label="处理结果：" label-align="left">
          {{ form.processResultName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理说明：" label-align="left">
          {{ form.processContent || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时间：" label-align="left">
          {{ form.processTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理人：" label-align="left">
          {{ form.processUser || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="附件：" :span="2" label-align="left">
          <ele-upload-image
            v-if="form.processFileList && form.processFileList.length > 0"
            :isShowUploadIcon="false"
            v-model:value="form.processFileList"
            class="mt10"
          />
        </el-descriptions-item>
      </el-descriptions>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getFeedbackDetail } from '@/api/standard_revision/opinion';
  import EleUploadImage from '@/components/EleUploadImage';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const form = ref({});

  onMounted(() => {
    getFeedbackDetail(props.id).then(res => {
      form.value = res.data;
    });
  });

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible']);
</script>

<style lang="scss" scoped>
  .br-5 {
    border-radius: 5px !important;
    overflow: hidden !important;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
