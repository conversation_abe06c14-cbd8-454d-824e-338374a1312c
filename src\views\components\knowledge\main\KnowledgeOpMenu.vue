<template>
  <span>
    <el-dropdown @command="handleOpCommand" trigger="click">
      <el-icon class="mr5 pointer"><MoreFilled /></el-icon>
      <template #dropdown>
        <el-dropdown-menu v-if="from === 'mainDocList'">
          <el-dropdown-item :command="beforeHandleCommand('renameFile',item)">重命名</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('deleteFile',item)">删除</el-dropdown-item> 
        </el-dropdown-menu>
        <el-dropdown-menu v-else-if="from === 'mainDocHeader'">
          <el-dropdown-item :command="beforeHandleCommand('moveFile',item)">移动</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('deleteFile',item)">删除</el-dropdown-item> 
        </el-dropdown-menu>
        <el-dropdown-menu v-else>
          <el-dropdown-item v-if="item.fileType == '0'" :command="beforeHandleCommand('addFolder',item)">新增文件夹</el-dropdown-item>
          <el-dropdown-item v-if="item.fileType == '0'" :command="beforeHandleCommand('uploadFile',item)">上传文件</el-dropdown-item>

          <el-dropdown-item :command="beforeHandleCommand('moveFile',item)">移动</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('renameFile',item)">重命名</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('deleteFile',item)">删除</el-dropdown-item> 
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 重命名文件或文件夹弹框 -->
    <knowledge-rename-folder-file v-if="openRename" v-model:open="openRename" :item="item" @success="renameSuccess"></knowledge-rename-folder-file>
    <!-- 移动文件或文件夹弹框 -->
    <knowledge-move-folder-file v-if="openMove" v-model:open="openMove" :fileId="fileId" @success="moveSuccess"></knowledge-move-folder-file>
    <!-- 新建文件夹弹框 -->
    <knowledge-add-folder v-if="open" v-model:open="open" @success="addFolderSuccess"></knowledge-add-folder>
  </span>
</template>

<script setup>
import { deleteByFileId } from "@/api/knowledge/library";
import KnowledgeRenameFolderFile from '@/views/components/knowledge/main/KnowledgeRenameFolderFile'
import KnowledgeMoveFolderFile from '@/views/components/knowledge/main/KnowledgeMoveFolderFile'
import KnowledgeAddFolder from '@/views/components/knowledge/main/KnowledgeAddFolder'

const {proxy} = getCurrentInstance()

const props = defineProps({
  item: {
    type: Object,
    default: {}
  },
  from: String,
  uploadDom: {
    type: Object,
    default: null
  }
})
const { item,from,uploadDom } = toRefs(props)

const commonInfo = inject('commonInfo')

const data = reactive({
  openRename: false,
  openMove: false,
  fileId: undefined,
  open: false
})

const {openRename,openMove,fileId,open} = toRefs(data)

const emit = defineEmits(['renameSuccess','deleteSuccess','moveSuccess'])

const beforeHandleCommand = (opName,row) => {
 	return {
 		command:opName,
 		row: row
 	}
}
const handleOpCommand = (command) => {
  switch (command.command) {
    case "moveFile":
      moveFile(command.row)
      break;
    case "renameFile":
      renameFile(command.row)
      break;
    case "deleteFile":
      deleteFile(command.row)
      break;
    case "addFolder":
      addFolder(command.row)
      break;
    case "uploadFile":
      uploadFile(command.row)
      break;
    default:
      break;
  }
}
const moveFile = (row) => {
  data.openMove = true
  data.fileId = row.id ? row.id : 0
}
const moveSuccess = () => {
  commonInfo.refreshMove()
}
const renameFile = (row) => {
  data.openRename = true
  // data.fileId = row.id ? row.id : 0
}
const renameSuccess = (item) => {
  if(item && item.fileName){
    commonInfo.fileName = item.fileName
  }
  
  commonInfo.refreshCategory()
  // el-tree 多次点击不在发送请求，fileId改变激活watch
  if(props.form){
    commonInfo.fileId = 0
    commonInfo.fileId = item.fileId
  }
  
  emit('renameSuccess')
}
/** 删除按钮操作 */
const deleteFile = (row) => {
  let tip = row.fileType == '0' ? '您确定要删除"' + row.fileName + '"的数据项?若删除则该文件夹及文件夹下所有内容也将会被删除!' : '您确定要删除"' + row.fileName + '"的数据项?'
  proxy.$modal.confirm(tip,'提示').then(function () {
    return deleteByFileId(row.id);
  }).then(() => {
    commonInfo.setSelectedCategory(row)
    emit('deleteSuccess')
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
    
  });
}
const addFolder = (row) => {
  data.open = true
  commonInfo.fileId = row.id ? row.id : 0
}
const uploadFile = (row) => {
  commonInfo.fileId = row.id ? row.id : 0
  uploadDom.value && uploadDom.value.click()
  // proxy.$refs.kFileUpload.$refs.uploadRef.$el.children[0].children[0].click()
}
const addFolderSuccess = () => {
  commonInfo.refreshCategory()
}
</script>

<style lang="scss" scoped>

</style>