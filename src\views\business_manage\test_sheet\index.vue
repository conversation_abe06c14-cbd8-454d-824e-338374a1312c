<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="试验单编号" prop="testSheetCode">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.testSheetCode" placeholder="请输入试验单编号" />
        </el-form-item>
        <el-form-item label="样品名称" prop="sampleName">
          <el-input @keyup.enter="getList('pageNum')" v-model="queryParams.sampleName" placeholder="请输入样品名称" />
        </el-form-item>
        <el-form-item label="创建日期">
          <el-date-picker
            v-model="dateRange"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="selectDate"
          />
        </el-form-item>
        <el-form-item label="" class="one-column">
          <el-button :loading="loading" @click="getList('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button :loading="loading" plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end">
        <el-button @click="handleAdd" type="primary" icon="Plus">新增试验单</el-button>
      </div>
      <el-table v-loading="loading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="testSheetCode" label="试验单编号" show-overflow-tooltip min-width="180" fixed />
        <el-table-column prop="sampleName" label="样品名称" show-overflow-tooltip min-width="180" fixed />
        <el-table-column prop="sampleModel" label="样品型号" show-overflow-tooltip min-width="180" />
        <el-table-column prop="sampleNum" label="样品数量" show-overflow-tooltip min-width="120" />
        <el-table-column prop="entrustedUnit" label="委托单位" show-overflow-tooltip min-width="150" />
        <el-table-column prop="contactPerson" label="联系人" show-overflow-tooltip min-width="150" />
        <el-table-column prop="contactPhone" label="联系方式" show-overflow-tooltip min-width="200" />
        <el-table-column prop="testProjectNum" label="试验项目数" show-overflow-tooltip min-width="120" />
        <el-table-column prop="createBy" label="创建人" show-overflow-tooltip min-width="120" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="180" />
        <el-table-column label="操作" min-width="180" fixed="right">
          <template #default="scope">
            <el-button @click.stop="handleEdit(scope.row)" type="primary" link>编辑</el-button>
            <el-button @click.stop="handleDetail(scope.row)" type="primary" link>查看</el-button>
            <el-button @click.stop="handleDel(scope.row)" type="danger" link>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <add-dialog
      v-if="addDialogVisible"
      v-model:addDialogVisible="addDialogVisible"
      :id="currentId"
      @updateData="getList('pageNum')"
    />
    <detail-dialog v-if="dialogVisible" v-model:dialogVisible="dialogVisible" :id="currentId" />
  </div>
</template>

<script setup>
  import { getExperimentList, delExperiment } from '@/api/business_manage/test_sheet';
  import AddDialog from '@/views/components/business_manage/test_sheet/AddDialog.vue';
  import DetailDialog from '@/views/components/business_manage/test_sheet/DetailDialog.vue';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    dialogVisible: false,
    addDialogVisible: false,
    currentId: null,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    tableData: [],
    total: 0,
    loading: false,
    dateRange: [],
  });

  const { dialogVisible, addDialogVisible, currentId, loading, queryParams, tableData, total, dateRange } = toRefs(data);

  const getList = val => {
    loading.value = true;
    if (val) queryParams.value[val] = 1;
    queryParams.value.startTime = dateRange.value.length > 0 ? dateRange.value[0] : '';
    queryParams.value.endTime = dateRange.value.length > 0 ? dateRange.value[1] : '';
    getExperimentList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    data.dateRange = [];
    selectDate();
    getList('pageNum');
  };

  const handleAdd = () => {
    currentId.value = null;
    addDialogVisible.value = true;
  };

  const handleEdit = row => {
    currentId.value = row.id;
    addDialogVisible.value = true;
  };

  const handleDetail = row => {
    currentId.value = row.id;
    dialogVisible.value = true;
  };

  const handleDel = row => {
    proxy
      .$confirm('确认删除编号为【' + row.testSheetCode + '】的试验单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        delExperiment({ id: row.id })
          .then(res => {
            proxy.$modal.msgSuccess('删除成功！');
          })
          .finally(() => {
            getList();
          });
      });
  };

  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.queryParams.startTime = data.dateRange[0];
      data.queryParams.endTime = data.dateRange[1];
    } else {
      data.queryParams.startTime = undefined;
      data.queryParams.endTime = undefined;
    }
  };

  getList();
</script>

<style lang="scss" scoped>
  .icon-piliangruku:before,
  .icon-daoru:before {
    margin-right: 6px;
  }
</style>
