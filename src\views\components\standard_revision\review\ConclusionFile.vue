<template>
  <el-dialog
    width="530px"
    title="上传结论文件"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form ref="queryRef" :model="form" :label-position="'top'" :rules="rules" @submit.prevent>
      <!-- 必填 -->
      <el-form-item label="结论文件" prop="reviewConclusionFileList">
        <div>
          <ele-upload-image
            v-model:value="form.reviewConclusionFileList"
            :fileSize="10"
            :multiple="true"
            :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']"
            :responseFn="handleResponse"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：word，pdf，excel，ppt；文件大小不超过10MB
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { reviewConclusion } from '@/api/standard_revision/review';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const loading = ref(false);
  const form = ref({
    reviewId: props.id,
  });
  const rules = reactive({
    deadline: [{ required: true, message: '请选择意见征求截止日期', trigger: 'blur' }],
  });

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        reviewConclusion(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('提交成功！');
            emit('success');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped></style>
