import request from '@/utils/request'

//查询未读消息消息top和未读消息数
export const unReadCountTopAndCount = (data) => {
  return request({
    url: '/process/messagePublish/unReadCountTopAndCount',
    method: 'get',
    params:data
  })
}



//标记已读/全部标记
export const messagePublishReadTag = (params) => {
  return request({
    url: '/process/messagePublish/readTag',
    method: 'put',
    params
  })
}

export const getMessagePublishList = (params) => {
  return request({
    url: '/process/messagePublish/list',
    method: 'get',
    params
  })
}

//删除消息
export const messagePublishRemove = (data) => {
  return request({
    url: '/process/messagePublish/remove',
    method: 'delete',
    data
  })
}


//获取消息控制开关
export const getMessageConfigs = () => {
  return request({
    url: '/system/config/getMessageConfigs',
    method: 'get'
  })
}
