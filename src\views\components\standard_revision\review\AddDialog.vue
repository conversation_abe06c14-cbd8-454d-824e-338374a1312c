<template>
  <el-dialog
    width="930px"
    :title="props.id ? '编辑复审项目' : '新增复审项目'"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form ref="queryRef" :model="form" label-position="top" :rules="rules" @submit.prevent>
      <el-form-item class="one-column">
        <div class="h-title">基础信息</div>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="form.projectName" placeholder="请输入项目名称" class="vw-half" />
      </el-form-item>
      <el-form-item label="计划完结时间" prop="planFinishDate">
        <el-date-picker
          :editable="false"
          :clearable="false"
          type="date"
          v-model="form.planFinishDate"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm"
          placeholder="请选择计划完结时间"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="项目负责人" prop="projectManagerId">
        <el-cascader
          v-model="form.projectManagerId"
          filterable
          :options="memberTreeList"
          :show-all-levels="false"
          :props="{ emitPath: false, label: 'name', value: 'id' }"
          placeholder="请选择项目负责人"
        />
      </el-form-item>
      <el-form-item prop="reviewStandardList" class="one-column mt30">
        <template #label>
          <div class="flex flex-sb flex-ai-center">
            <div class="h-title">复审标准</div>
            <el-button
              v-if="form.reviewStandardList && form.reviewStandardList.length < 100"
              @click="handleAddStandard"
              type="primary"
              icon="Plus"
            >
              添加复审标准
            </el-button>
          </div>
        </template>
        <el-table :data="form.reviewStandardList" :border="true" :max-height="400" class="mt15">
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column label="序号" type="index" width="55" fixed />
          <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip fixed min-width="180" />
          <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="180" />
          <el-table-column label="标准状态" show-overflow-tooltip min-width="150">
            <template #default="{ row }">
              <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reviewBasis" label="复审依据" show-overflow-tooltip min-width="180">
            <template #default="{ row, $index }">
              <el-form-item :prop="'reviewStandardList.' + $index + '.reviewBasis'" :rules="rules.reviewBasis">
                <el-select v-model="row.reviewBasis" placeholder="请选择复审依据">
                  <el-option v-for="dict in bxc_review_basis" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" fixed="right">
            <template #default="{ row, $index }">
              <el-button @click="form.reviewStandardList.splice($index, 1)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSave">保存</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">保存并立项</el-button>
    </template>
    <choose-standard
      v-if="standardVisible"
      v-model:visible="standardVisible"
      :selectTable="form.reviewStandardList"
      @chooseData="handleChoose"
    />
  </el-dialog>
</template>

<script setup>
  import { memberTree } from '@/api/common';
  import { addReview, editReview, approveReview, editApproveReview, getReviewDetail } from '@/api/standard_revision/review';
  import ChooseStandard from '@/views/components/standard_revision/review/ChooseStandard.vue';

  const { proxy } = getCurrentInstance();
  const { bxc_review_basis } = proxy.useDict('bxc_review_basis');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const loading = ref(false);
  const memberTreeList = ref([]);
  const form = ref({
    reviewStandardList: [],
  });
  const rules = ref({
    projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    planFinishDate: [{ required: true, message: '请选择计划完结时间', trigger: 'change' }],
    projectManagerId: [{ required: true, message: '请选择项目负责人', trigger: 'change' }],
  });
  const standardVisible = ref(false);

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  const handleData = data => {
    data.forEach(item => {
      if (item.type == 0) {
        item.disabled = false;
        return;
      }
      if (item.type == 1 && item.children && item.children.length > 0) {
        item.disabled = false;
        handleData(item.children);
      } else {
        item.disabled = true;
      }
    });
    return data;
  };

  memberTree().then(res => {
    memberTreeList.value = handleData(res.data);
  });

  if (props.id) {
    getReviewDetail(props.id).then(res => {
      let data = res.data;
      data.reviewStandardList = data.reviewStandardList && data.reviewStandardList.length > 0 ? data.reviewStandardList : [];
      form.value = data;
    });
  }

  const disabledDate = time => {
    return time.getTime() < Date.now() - 8.64e7;
  };

  const handleAddStandard = () => {
    standardVisible.value = true;
  };

  const handleChoose = data => {
    form.value.reviewStandardList = data;
  };

  const handleSave = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        if (props.id) {
          editReview(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('数据保存成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addReview(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('数据保存成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  };

  const handleConfirm = () => {
    loading.value = true;
    rules.value.reviewBasis = [{ required: true, message: '请选择复审依据', trigger: 'change' }];
    if (!form.value.reviewStandardList || form.value.reviewStandardList.length == 0) {
      proxy.$modal.msgWarning('请选择复审标准');
      loading.value = false;
      return;
    }
    if (
      form.value.reviewStandardList &&
      form.value.reviewStandardList.length > 0 &&
      !form.value.reviewStandardList.every(item => item.reviewBasis)
    ) {
      proxy.$modal.msgWarning('存在未设置复审依据标准');
    }
    proxy.$refs.queryRef.validate((valid, e) => {
      if (valid) {
        proxy
          .$confirm('立项后基础信息与复审标准将不可修改，确认立项吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            if (props.id) {
              editApproveReview(form.value)
                .then(res => {
                  proxy.$modal.msgSuccess('数据保存成功！');
                  emit('success');
                  handleClose();
                })
                .finally(() => {
                  loading.value = false;
                });
            } else {
              approveReview(form.value)
                .then(res => {
                  proxy.$modal.msgSuccess('数据保存成功！');
                  emit('success');
                  handleClose();
                })
                .finally(() => {
                  loading.value = false;
                });
            }
          })
          .catch(() => {});
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-right: 0 !important;
  }

  :deep(.el-table .el-form-item) {
    margin-bottom: 0 !important;
  }

  :deep(.el-date-editor) {
    width: 50% !important;
  }

  :deep(.el-cascader) {
    width: 50% !important;
  }

  .vw-half {
    width: 50% !important;
  }
</style>
