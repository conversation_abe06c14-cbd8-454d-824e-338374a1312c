<template>
  <div v-loading="loading" class="content-main-container">
    <div class="header-box">
      <div class="header-title-box">
        <span class="title">{{info.fileName || '-'}}</span>
        <span class="ml10">{{info.createUserName || '-'}}</span>
        <span class="ml10">于{{info.createDate || '-'}}创建</span>
      </div>
      
    </div>
    <div class="content-title">
      {{info.fileName || '-'}}
      <span v-if="info.fileSize" class="ml5">({{info.fileSize || '-'}})</span>
    </div>
    <div v-if="info.fileType == '0'" class="folder-wrap">
      <knowledge-main-doc @itemClick="docClick"></knowledge-main-doc>
    </div>
    <div v-else class="doc-show">
      <iframe :src="previewUrl" style="width:100%;height:75vh;border:none;"></iframe>
    </div>
  </div>
</template>

<script setup>
import { getFileInfo } from "@/api/knowledge/library";

import { base64Encode } from '@/utils/base64'
import KnowledgeMainDoc from '@/views/components/knowledge/main/KnowledgeMainDoc'
import KnowledgeOpMenu from '@/views/components/knowledge/main/KnowledgeOpMenu'

const {proxy} = getCurrentInstance()
const commonInfo = inject('commonInfo')

const data = reactive({
  loading: false,
  info: {},
  previewUrl: ''
})
const {loading,info,previewUrl} = toRefs(data)

const docClick = (param) => {
  getData(param)
  commonInfo.fileId = param.id
}
const getData = (fObj) => {
  data.loading = true;
  let fId = fObj ? fObj.id : commonInfo.fileId
  getFileInfo(fId).then((response) => {
    data.info = response.data
    data.previewUrl = `${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(base64Encode(data.info.fileUrl))}`
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}

watch(()=>commonInfo.fileId,(newVal,oldVal) => {
  if(newVal > 0 && newVal != oldVal){
    getData()
  }
},{ deep: true, immediate: true })

watch(()=>commonInfo.fileName,(newVal,oldVal) => {
  if(newVal && newVal != oldVal){
    getData()
  }
},{ deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.content-main-container{
  .header-box{
    display: flex;
    justify-content: space-between;
    .header-title-box{
      margin-right: auto;
      font-size: 14px;
      color: #3E4967;
    }
    
  }
  .content-title{
    margin-top: 15px;
    font-size: 20px;
    font-weight: bold;
    color: #3E4967;
    span{
      font-size: 14px;
      color: #3E4967;
    }
  }
  
  .doc-show{
    margin-top: 20px;
  }
}
</style>