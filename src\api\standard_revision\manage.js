import request from '@/utils/request';

// 列表
export const getRevisionList = data => {
  return request({
    url: '/process/amendProjectApproval/list',
    method: 'get',
    params: data,
  });
};

// 删除制修订
export const delRevision = data => {
  return request({
    url: '/process/amendProjectApproval/remove',
    method: 'delete',
    data: data,
  });
};

// 详情
export const getRevisionDetail = data => {
  return request({
    url: '/process/amendProjectApproval/' + data,
    method: 'get',
  });
};

// 新增
export const addRevision = data => {
  return request({
    url: '/process/amendProjectApproval',
    method: 'post',
    data: data,
  });
};

// 修改
export const putRevision = data => {
  return request({
    url: '/process/amendProjectApproval',
    method: 'put',
    data: data,
  });
};

// 文件列表
export const getRevisionFileList = data => {
  return request({
    url: '/process/amendProjectApproval/filesList',
    method: 'get',
    params: data,
  });
};

// 征求意见稿
export const getRevisionOpinionDetail = data => {
  return request({
    url: '/process/amendProjectApproval/exposureDraftDetail',
    method: 'get',
    params: data,
  });
};

// 日志
export const getRevisionLogList = data => {
  return request({
    url: '/process/amendProjectApproval/operationFlow/list',
    method: 'get',
    params: data,
  });
};

// 启动阶段
export const openPhase = data => {
  return request({
    url: '/process/amendProjectApproval/startThisPhase',
    method: 'get',
    params: data,
  });
};

// 发布
export const standardPublish = data => {
  return request({
    url: '/process/amendProjectApproval/standardPublishing',
    method: 'put',
    data: data,
  });
};

// 删除文件
export const delRevisionFile = data => {
  return request({
    url: '/process/amendProjectApproval/deleteFile',
    method: 'get',
    params: data,
  });
};

// 设置文件名称
export const updateFileName = data => {
  return request({
    url: '/process/amendProjectApproval/setFileName',
    method: 'put',
    data: data,
  });
};

// 推送至标准库
export const pushStandard = data => {
  return request({
    url: '/process/amendProjectApproval/pushStandard',
    method: 'get',
    params: data,
  });
};

// 设置截止时间
export const updateDeadline = data => {
  return request({
    url: '/process/amendProjectApproval/setDeadline',
    method: 'get',
    params: data,
  });
};

// 意见稿作废
export const fileCancellation = data => {
  return request({
    url: '/process/amendProjectApproval/cancellationFile',
    method: 'get',
    params: data,
  });
};

// 详情(重启)
export const getProcessData = data => {
  return request({
    url: '/workflow/process/getProcessData',
    method: 'get',
    params: data,
  });
};

export const putArchive = data => {
  return request({
    url: '/process/amendProjectApproval/archive/' + data,
    method: 'put',
  });
};

export const startNextPhase = data => {
  return request({
    url: '/process/amendProjectApproval/startNextPhase/' + data,
    method: 'get',
  });
};

export const getSolicitOpinionDraft = data => {
  return request({
    url: '/process/amendProjectApproval/getSolicitOpinionDraft',
    method: 'get',
    params: data,
  });
};

export const amendProjectApproval = data => {
  return request({
    url: '/process/amendProjectApproval/' + data,
    method: 'get',
  });
};

export const solicitOpinion = data => {
  return request({
    url: '/process/solicitOpinion',
    method: 'post',
    data: data,
  });
};

export const solicitOpinionFeedback = data => {
  return request({
    url: '/process/solicitOpinionFeedback/list',
    method: 'get',
    params: data,
  });
};

export const setDeadline = data => {
  return request({
    url: '/process/amendProjectApproval/setDeadline',
    method: 'get',
    params: data,
  });
};

export const cancelSolicitOpinionDraft = data => {
  return request({
    url: '/process/amendProjectApproval/cancelSolicitOpinionDraft/' + data,
    method: 'post',
  });
};

export const putSolicitOpinionFeedback = data => {
  return request({
    url: '/process/solicitOpinionFeedback',
    method: 'put',
    data: data,
  });
};

export const putAmendProjectApproval = data => {
  return request({
    url: '/process/amendProjectApproval/complete/' + data,
    method: 'put',
  });
};
