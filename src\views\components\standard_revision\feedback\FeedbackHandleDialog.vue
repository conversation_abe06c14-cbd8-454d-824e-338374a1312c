<template>
  <div class="app-container">
    <el-dialog
      v-model="dialogVisible"
      width="1000px"
      title="反馈处理"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <div class="notice-content">
        <div class="h-title">反馈信息</div>
        <el-descriptions class="mt20" :column="2">
          <el-descriptions-item label="反馈用户：" :span="2" label-align="left">
            {{ feedbackForm.feedbackUser || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属部门：" :span="2" label-align="left">
            {{ feedbackForm.feedbackDept || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="反馈内容：" :span="2" label-align="left">
            {{ feedbackForm.feedbackContent || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="反馈时间：" :span="2">
            {{ feedbackForm.feedbackTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="附件：" :span="2">
            <ele-upload-image
              v-if="feedbackForm.feedbackFileList && feedbackForm.feedbackFileList.length > 0"
              class="accept-content"
              :responseFn="handleResponse"
              :fileType="fileType"
              :isShowUploadIcon="false"
              v-model:value="feedbackForm.feedbackFileList"
            ></ele-upload-image>
            <div v-else>无</div>
          </el-descriptions-item>
        </el-descriptions>
        <div class="h-title mt20">关联标准</div>
        <el-table class="mt20" :data="dataList" :border="true">
          <el-table-column label="序号" type="index" align="center" width="60" />
          <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip min-width="200" />
          <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
          <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="150" />
          <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="150" />
          <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
        </el-table>
        <div class="h-title mt20">反馈处理</div>
        <el-form
          inline
          label-width="auto"
          :model="form"
          :label-position="'top'"
          ref="queryRef"
          :rules="rules"
          class="pl10 mt5 dia-form"
        >
          <el-form-item label="反馈处理说明" prop="processContent" class="one-column">
            <el-input
              v-model="form.processContent"
              :rows="5"
              :show-word-limit="true"
              maxlength="500"
              type="textarea"
              placeholder="请输入反馈处理说明"
            />
          </el-form-item>
          <el-form-item label="" prop="processFileList" class="one-column">
            <div>
              <div class="f-16 c-3E">附件</div>
              <ele-upload-image
                :multiple="true"
                :responseFn="handleResponse"
                :fileType="fileType"
                :fileSize="5"
                v-model:value="form.processFileList"
                @success="handleUpload('processFileList')"
              />
              <div class="c-EE0000 flex upload-notice flex-ai-center">
                <i class="iconfont icon-tishi f-18 mr5"></i>
                支持文件格式：jpeg、jpg、png、bmp、doc、docx、xls、xlsx、ppt、pptx、pdf；单个文件大小不超过5MB
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="handleConfirm" :loading="loading" type="primary">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import EleUploadImage from '@/components/EleUploadImage';
  import { standardFeedbackDetail, standardFeedbackInfoHandle } from '@/api/standard_revision/feedback.js';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number],
  });
  const { dialogVisible, id } = toRefs(props);

  const state = reactive({
    loading: false,
    feedbackForm: {},
    form: {},
    dataList: [],
    fileType: ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'bmp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
    rules: {
      processContent: [
        {
          required: true,
          message: '请输入处理说明',
          trigger: 'blur',
        },
      ],
    },
  });

  const { title, loading, form, dataList, fileType, rules, feedbackForm } = toRefs(state);

  onMounted(() => {
    if (id.value) getDetail();
  });

  const getDetail = () => {
    standardFeedbackDetail(id.value).then(res => {
      let data = res.data;
      state.feedbackForm = data;
      state.dataList = data.standardList;
    });
  };

  //点击提交
  const handleConfirm = () => {
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        loading.value = true;
        state.form.id = state.feedbackForm.id;
        standardFeedbackInfoHandle(state.form)
          .then(() => {
            proxy.$modal.msgSuccess('处理成功');
            emit('getList');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  };

  const emit = defineEmits(['update:dialogVisible', 'getList']);

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleClose = () => {
    emit('update:dialogVisible', false);
    proxy.$refs.queryRef.resetFields();
  };

  const handleUpload = type => {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  };
</script>

<style lang="scss" scoped>
  .dia-form {
    width: 100%;
    .el-form-item {
      width: 50%;
    }
    .one-column {
      flex: 0 0 calc(95% + 30px) !important;
    }
  }
  .notice-header {
    padding-bottom: 17px;
    border-bottom: 1px solid #e5e8ef;
    .title {
      margin-bottom: 14px;
    }
    .dec {
      margin-bottom: 12px;
    }
    .times {
      .collect {
        width: 90px;
        height: 30px;
        margin-right: 12px;
        color: #ffb400;
        background: #fff8e1;
        img {
          height: 14px;
          margin-right: 7px;
        }
      }
      .pv {
        width: 90px;
        height: 30px;
        margin-right: 12px;
        color: #04ae00;
        background: #e5fae4;
        img {
          height: 14px;
          margin-right: 7px;
        }
      }
    }
  }

  .upload-notice {
    margin-top: 5px;
    img {
      height: 18px;
      margin-right: 8px;
    }
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
