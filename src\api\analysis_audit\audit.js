import request from '@/utils/request';

// 审核数据
// 解析数据-标准数据、可解析数据、不解析数据、已解析数据
export function getAuditList(params) {
  return request({
    url: '/stdAnalysisStandard/list',
    method: 'get',
    params,
  });
}
// 分派
export function setAssign(data) {
  return request({
    url: '/stdAnalysisStandard/assign',
    method: 'put',
    data,
  });
}
// 解析详情-全部（包括目次、正文、pdf地址）
export function getAnalysisDetail(data) {
  return request({
    url: '/process/common/standard/getAnalysisDetail/' + data,
    method: 'get'
  });
}
// 章条移动
export function moveCategory(data) {
  return request({
    url: '/stdAnalysisStandard/move',
    method: 'put',
    data
  });
}
// 新增章条、内容
export function addSection(data) {
  return request({
    url: '/stdAnalysisStandard/addSection',
    method: 'post',
    data
  });
}
// 编辑章条、内容
export function editSection(data) {
  return request({
    url: '/stdAnalysisStandard/editSection',
    method: 'post',
    data
  });
}
// 获取指定章条、内容
export function getSection(sectionId) {
  return request({
    url: '/stdAnalysisStandard/getSection/'+ sectionId,
    method: 'get'
  });
}
// 删除章条、内容
export function deleteSection(id) {
  return request({
    url: '/stdAnalysisStandard/delSection/' + id,
    method: 'delete'
  });
}
// 审核完成
export function auditCompleted(data) {
  return request({
    url: '/stdAnalysisStandard/auditCompleted',
    method: 'put',
    data
  });
}
// 二次审核结果
export function auditSecond(data) {
  return request({
    url: '/stdAnalysisStandard/auditResult',
    method: 'put',
    data
  });
}
// 重新加工
export function setRebuild(params) {
  return request({
    url: '/stdAnalysisStandard/restarAftercure',
    method: 'get',
    params
  });
}