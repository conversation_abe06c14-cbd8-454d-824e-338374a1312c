<template>
  <el-dialog
    width="500px"
    title="导入结果"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-ai-center f-16">
      <el-icon class="c-primary mr5"><InfoFilled /></el-icon>
      标准数据导入解析器完成！
    </div>
    <div class="mt20 mb10">
      <div class="flex flex-sb f-14">
        <div>
          导入标准：
          <span class="c-primary">{{ rowData.totalCount || 0 }}</span>
          条
        </div>
        |
        <div>
          导入成功：
          <span class="c-primary">{{ rowData.successCount || 0 }}</span>
          条
        </div>
        |
        <div>
          导入失败：
          <span class="c-primary">{{ rowData.errorCount || 0 }}</span>
          条
        </div>
      </div>
      <div v-if="rowData.errorCount != 0" @click="handleDownload" class="c-primary mt20 f-14 flex flex-center pointer" style="text-decoration: underline;">下载导入失败数据</div>
    </div>
    
    <!-- <template #footer>
      <div>
        <el-button type="info" @click="handleClose">关闭</el-button>
      </div>
    </template> -->
  </el-dialog>
</template>

<script setup>
  import { json2excel } from '@/utils/formatExcel.js'
  const props = defineProps({
    rowData: {
      required: true,
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const { rowData } = toRefs(props);

  const handleDownload = () => {
    let data = rowData.value.errorVOList || []
    let excelDatas = [
      {
        tHeader: ['标准号', '失败原因'],
        filterVal: ['standardCode', 'cause'],
        tableDatas: data,
        sheetName: 'Sheet1',
      },
    ];
    let multiHeader = [];
    json2excel(excelDatas, multiHeader, '导入失败数据', true, 'xlsx');
  }

  const handleClose = () => {
    emit('updateData');
    emit('update:rowData', {});
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'update:rowData','updateData']);
</script>

<style lang="scss" scoped></style>
