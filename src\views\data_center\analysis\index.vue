<template>
  <div class="app-container">
    <div class="data-center-wrap">
      <div class="flex flex-center c-33 f-22">
        <div class="mt40 f-bold">标准数据统计与分析中心</div>
      </div>
      <div class="data-list mt40">
        <div v-for="(item,index) in dataList" :key="index" @click="handlePush(item)" class="data-item">
          <div class="item-left">
            <div class="left-title">
              {{item.name}}
            </div>
          </div>
          <div class="item-right">
            <div class="img-wrap">
              <i class="iconfont icon-shujuzhongxin c-primary f-40"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()

const data = reactive({
  dataList: [{
    name: '企业标准库统计分析',
    path: 'library_stat',
    permi: 'data_center:analysis:library_stat'
  },{
    name: '标准使用统计分析',
    path: 'use_stat',
    permi: 'data_center:analysis:use_stat'
  }]
})
const {dataList} = toRefs(data)

const handlePush = (item) => {
  if(item.permi && proxy.$auth.hasPermi(item.permi)){
    proxy.$router.push(item.path)
  }else{
    proxy.$modal.msgError('无相关权限')
  }
}

</script>

<style lang="scss" scoped>
.data-center-wrap {
  background: #FFFFFF;
  border-radius: 15px;
  height: calc(100vh - 130px);
}
.data-list {
  padding: 0 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px 40px;
  align-items: center;
  .data-item {
    background-color: $primary-color;
    width: calc((100% - 120px)/4);
    min-width: 320px;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    .item-left{
      .left-title{
        margin-top: 10px;
        font-size: 18px;
        font-weight: bold;
        color: #FFFFFF;
        position: relative;
        &::after{
          content: '';
          position: absolute;
          left: 0px;
          top: 50px;
          width: 34px;
          height: 3px;
          background: #FFFFFF;
        }
      }
    }
    .item-right{
      margin-left: 10px;
      display: flex;
      align-items: center;
      .img-wrap{
        flex-shrink: 0;
        width: 73px;
        height: 73px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  
}
</style>