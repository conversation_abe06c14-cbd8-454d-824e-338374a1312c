<template>
  <el-dialog
    v-model="open"
    :title="title"
    :before-close="close"
    append-to-body
    width="90%"
  >
    <process-viewer v-if="modelId" :key="`designer-0`" :xml="xmlStr" :style="{height: '700px'}" />
    <process-viewer v-else :key="`designer-1`" :style="'height:700px'" :xml="xmlStr"
                          :finishedInfo="finishedInfo" :allCommentList="historyProcNodeList" />
  </el-dialog>
</template>

<script setup>
import { getProcessSettingDetail } from "@/api/approve/process_setting";
import { getProcessDetail } from "@/api/approve/todo";
import ProcessViewer from '@/components/ProcessViewer'

const { proxy } = getCurrentInstance();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  modelId: {
    type: String,
    default: undefined,
  },
  procInsId: {
    type: String,
    default: undefined,
  },
  info: {
    type: Object,
    default: {},
  }
});
const { open,modelId,procInsId,info } = toRefs(props);

const data = reactive({
  loading: false,
  title: '流程设计',
  finishedInfo: {
    finishedSequenceFlowSet: [],
    finishedTaskSet: [],
    unfinishedTaskSet: [],
    rejectedTaskSet: []
  },
  historyProcNodeList: [],
  xmlStr: undefined
});
const { loading, title,finishedInfo,historyProcNodeList,xmlStr } = toRefs(data);

if(props.info){
  data.finishedInfo = props.info.flowViewer
  data.historyProcNodeList = props.info.historyProcNodeList
  data.xmlStr = props.info.bpmnXml
}

const close = () => {
  emit("success");
  emit("update:open", false);
};

const emit = defineEmits(["update:open", "success"]);

/** 初始化 */
onMounted(async () => {
  if(props.modelId) {
    getData()
  }
  if(props.procInsId) {
    getProcessData()
  }
})

const getData = () => {
  data.loading = true;

  getProcessSettingDetail(props.modelId).then(response => {
    if (response.data) {
      xmlStr.value = response.data
    }
    data.loading = false;
  }).catch(() => {
    data.loading = false;
  });
}
const getProcessData = () => {
  data.loading = true;
  let params = {
    procInsId: props.procInsId
  }
  getProcessDetail(params).then(response => {
    if (response.data) {
      data.finishedInfo = response.data.flowViewer
      data.historyProcNodeList = response.data.historyProcNodeList
      data.xmlStr = response.data.bpmnXml
    }
    data.loading = false;
  }).catch(() => {
    data.loading = false;
  });
}


</script>

<style lang="scss" scoped>

</style>