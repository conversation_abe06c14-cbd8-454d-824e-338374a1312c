<template>
<el-dialog :append-to-body="true" v-model="open" width="500px" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close" class="pop-container">
  <div class="tip-box">
    <el-icon color="#0272FF" size="18"><InfoFilled /></el-icon>
    <span class="ml10">文本内容比较大时，数据组装需要一定时间，在进行重新加工操作后，请等待5-10秒，待数据生成完成后，进行该数据的审核操作！</span>
  </div>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="mt-10">
    <el-form-item label="加工方式" prop="type">
      <el-button :type="form.type == 0 ? 'primary' : 'default'"  size="large" @click="changeResult('0')" >包含目次</el-button>
      <el-button :type="form.type == 1 ? 'primary' : 'default'" size="large" @click="changeResult('1')" >不包含目次</el-button>
    </el-form-item>
    <template v-if="form.type !== undefined">
      <div class="flex">
        <el-icon color="#F28900" size="18"><InfoFilled /></el-icon>
        <span class="ml10 c-F28900 f-14">{{
          form.type == 0? '包含目次时，目次将做为独立标题进行识别！' : '不包含目次时，目次将做为文本内容进行识别！'
        }}</span>
      </div>
    </template>
  </el-form>

  <template #footer>
    <div class="dialog-footer">
      <el-button @click="close">关闭</el-button>
      <el-button @click.prevent="finish" type="primary" :loading="finishLoading">
        <span v-if="!finishLoading">确认</span>
        <span v-else>确认中...</span>
      </el-button>
    </div>
  </template>
</el-dialog>
</template>
<script setup>
import { setRebuild } from "@/api/standard_analysis/analysis_audit";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  standardItem: {
    type: Object,
    default: {}
  }
});
const { open,standardItem } = toRefs(props)

const title = ref('重新加工')
const finishLoading = ref(false)
const form = ref({
  standardId: undefined,
  type: undefined,
})
const rules = ref({
  type: [{ required: true, message: '请选择加工方式', trigger: 'blur' }],
});
onMounted(()=>{
  form.value.standardId = standardItem.value.standardId
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const changeResult = (val) => {
  form.value.type = val
}
const finish = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      finishLoading.value = true
      setRebuild(form.value).then(response => {
        finishLoading.value = false;
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("数据重新加工完成");
      }).catch(error => {
        finishLoading.value = false;
      });
    }
  });
}

</script>
<style lang="scss" scoped>
.tip-box {
  padding: 15px 20px;
  background: #EFF6FF;
  border-radius: 5px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
:deep(.el-form-item){
  margin-right: 0px !important;
}

</style>