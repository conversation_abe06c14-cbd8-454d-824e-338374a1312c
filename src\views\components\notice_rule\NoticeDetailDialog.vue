<template>
  <div class="app-container">
    <el-dialog v-model="dialogVisible" width="860" title="详情" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
      <div class="notice-header">
        <div class="title fw-b f-18 c-33 overflow-ellipsis">{{ data.title }}</div>
        <div class="dec f-14 c-99 flex">
          <div class="push-date mr20">发布日期：{{ data.publishDate }}</div>
          <div class="source flex-1 overflow-ellipsis">来源/部门：{{ data.source }}</div>
        </div>
        <div class="times flex">
          <div class="collect flex flex-center">
            <i class="iconfont icon-shoucang1 f-14 mr5"></i>
            {{ data.collectTotal }}
            次
          </div>
          <div class="pv flex flex-center">
            <i class="iconfont icon-chaxun f-14 mr5"></i>
            {{ data.pv }}
            次
          </div>
        </div>
      </div>
      
      <div class="notice-content mt20">
        <!-- <div class="title f-18 c-33 fw-b">公告正文</div> -->
        <div class="mt20" v-html="data.content"></div>
        <div class="title f-18 c-33 fw-b mt20 mb20" v-if="data.noticeFileList && data.noticeFileList.length > 0">附件</div>
        <ele-upload-image
          v-if="data.noticeFileList && data.noticeFileList.length > 0"
          class="accept-content"
          :responseFn="handleResponse"
          :isShowUploadIcon="false"
          v-model:value="data.noticeFileList"
        ></ele-upload-image>
        <template v-if="data.isJoin == 1">
          <div class="title f-18 c-33 fw-b mt20">关联标准</div>
          <el-table
          class="mt20"
          :data="data.standardList"
          :border="true"
          >
            <el-table-column label="序号" type="index" align="center" width="60" />
            <el-table-column prop="standardCode" width="300" label="标准号" show-overflow-tooltip />
            <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip />
          </el-table>
        </template>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="cancel">关 闭</el-button>
        </div>
      </template>
      
    </el-dialog>
  </div>
  </template>
  
  <script setup>
  import EleUploadImage from "@/components/EleUploadImage"
  import {getNoticeManageDetail} from '@/api/notice_rule/manage.js'


  const {proxy} = getCurrentInstance()
  
  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number]
  })
  const {dialogVisible,id} = toRefs(props)
  
  const state = reactive({
    loading: false,
    data: {},
  })
  
  const {title,loading,data} = toRefs(state)

  onMounted(() => {
    if (id.value) getDetail();
  });

  const getDetail = () => {
    getNoticeManageDetail(id.value).then((res) => {
      state.data = res.data;
    });
  };

  
  const emit = defineEmits(['update:dialogVisible','success'])
  const close = () => {
    emit('update:dialogVisible',false)
  }
  const cancel = () => {
    emit('update:dialogVisible',false)
  }
  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id,'url': response.data.url,'name':response.data.name}
  }
  
  const handleDownload = () => {
    proxy.download('/online/demand/word', {id: state.data.id}, `咨询报价工单详情表_${new Date().getTime()}.docx`)
  }
  
  </script>
  
  <style lang="scss" scoped>
  .notice-header{
    padding-bottom: 17px;
    border-bottom: 1px solid #E5E8EF;
    .title{
      margin-bottom: 14px;
    }
    .dec{
      margin-bottom: 12px;
    }
    .times{
      .collect{
        width: 90px;
        height: 30px;
        margin-right: 12px;
        color: #FFB400;
        background: #FFF8E1;
        img{
          height: 14px;
          margin-right: 7px;
        }
      }
      .pv{
        width: 90px;
        height: 30px;
        margin-right: 12px;
        color: #04AE00;
        background: #E5FAE4;
        img{
          height: 14px;
          margin-right: 7px;
        }
      }
    }
  }
  
  .ele-upload-image{
    display: block !important;
  }
  </style>