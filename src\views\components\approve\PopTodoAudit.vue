<template>
  <div>
    <el-dialog v-model="open" :title="title" :before-close="close" width="60%">
      <approve-apply-info :info="approveInfo.applyInfo" />
      <approve-revision-info
        v-if="approveInfo.processFormData && approveInfo.processFormData.amend_project_approval"
        :info="approveInfo.processFormData.amend_project_approval"
        class="mt20"
      />
      <div class="flex flex-sb mt20">
        <div class="c-33 f-18 f-bold">审批记录</div>
        <div class="c-primary f-14 pointer" @click="handleViewProcess">查看审批流程图</div>
      </div>
      <approve-audit-process :list="approveInfo.historyProcNodeList" class="mt15" />

      <div class="f-14 c-33 mt20">审批意见</div>
      <el-form-item class="mt10" label="" prop="comment">
        <el-input
          v-model="form.comment"
          :rows="5"
          :show-word-limit="true"
          maxlength="500"
          type="textarea"
          placeholder="请输入审批意见说明"
        />
      </el-form-item>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="save(0)" :loading="loading" type="primary" color="#D70D0E">驳回</el-button>
          <el-button @click="save(1)" :loading="loading" type="primary">同意</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 流程图弹框 -->
    <approve-process-view v-if="openView" v-model:open="openView" :info="approveInfo" />
  </div>
</template>

<script setup>
  import { getProcessDetail, agreeProcess, rejectProcess } from '@/api/approve/todo';
  import ApproveApplyInfo from '@/views/components/approve/ApproveApplyInfo';
  import ApproveRevisionInfo from '@/views/components/approve/ApproveRevisionInfo';
  import ApproveAuditProcess from '@/views/components/approve/ApproveAuditProcess';
  import ApproveProcessView from '@/views/components/approve/ApproveProcessView';

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    open: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: {},
    },
  });
  const { open, info } = toRefs(props);

  const data = reactive({
    loading: false,
    title: '审批',
    openView: false,
    approveInfo: {
      bpmnXml: '',
      flowViewer: {},
      historyProcNodeList: [],
      applyInfo: {}, // 申请信息
      processFormData: {}, // 立项信息
      taskFormData: {},
      processKey: '',
      wfTaskSimpleVO: {},
    },
    form: {
      procInsId: props.info.procInsId,
      taskId: props.info.taskId,
      comment: '',
    },
  });
  const { loading, title, openView, approveInfo, form } = toRefs(data);

  const save = type => {
    //type:0 驳回，1同意
    if (type == 1) {
      agreeProcess(data.form)
        .then(res => {
          proxy.$modal.msgSuccess('审批流程同意提交成功');
          close();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      rejectProcess(data.form)
        .then(res => {
          proxy.$modal.msgSuccess('审批流程驳回提交成功');
          close();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  const close = () => {
    emit('success');
    emit('update:open', false);
  };

  const emit = defineEmits(['update:open', 'success']);

  const getData = () => {
    data.loading = true;
    let params = {
      procInsId: props.info.procInsId,
      taskId: props.info.taskId,
    };
    getProcessDetail(params)
      .then(response => {
        if (response.data) {
          data.approveInfo = response.data;
        }
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };
  const handleViewProcess = () => {
    data.openView = true;
  };

  getData();
</script>

<style lang="scss" scoped></style>
