<template>
  <div class="plr25">
    <div class="flex flex-sb flex-ai-center">
      <div class="h-title">起草单位</div>
      <div v-if="form.draftUnitsList && form.draftUnitsList.length > 12" class="flex flex-column flex-sa flex-ai-center pointer">
        <span @click="handClick" class="iconfont f-8" :class="portion ? 'icon-xiala' : 'icon-shangla'"></span>
      </div>
    </div>
    <div v-if="form.draftUnitsList && form.draftUnitsList.length > 0" class="unit">
      <div v-for="item in list" :key="item.id" class="unit-label">
        <!-- <img :src="unitImage" class="unit-label-icon" alt="" /> -->
        <span class="iconfont icon-gongsi f-20 c-primary"></span>
        <div class="tool-tip ml10" v-showToolTip>
          <el-tooltip placement="top" :content="item">
            <div class="tool-tip-ellipsis">{{ item }}</div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div v-else class="normal-emtpy">暂无信息</div>
  </div>
</template>

<script setup>
  // const unitImage = new URL('@/assets/images/db/unit.png', import.meta.url).href;

  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const portion = ref(true);

  let list = computed(() => {
    return portion.value ? form.value.draftUnitsList.slice(0, 12) : form.value.draftUnitsList;
  });

  const handClick = () => {
    portion.value = !portion.value;
  };
</script>

<style lang="scss" scoped>
  .unit {
    display: flex;
    flex-wrap: wrap;

    &-label {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #333333;
      padding-right: 10px;
      width: 16.6%;
      box-sizing: border-box;
      border-radius: 5px;
      margin-top: 20px;

      &-icon {
        width: 30px;
        height: 30px;
      }
    }
  }
</style>
