<template>
<div class="pop-container">
  <el-dialog :modal-append-to-body="false" v-model="open" width="25%" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close">
    <el-form :model="form" ref="formRef" @submit.prevent="save" :rules="formRules" :label-position="'top'">
      <el-form-item :label="typeTip" prop="fileName">
        <el-input ref="firstInputRef" maxlength="50" v-model="form.fileName" :placeholder="`请输入${typeTip}`" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button @click.prevent="save" type="primary" :loading="loading">
          <span v-if="!loading">保存</span>
          <span v-else>保存中...</span>
        </el-button>
      </div>
    </template>
  </el-dialog>
</div>

</template>

<script setup>
import { renameFolderOrFile } from "@/api/knowledge/library";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  item: {
    type: Object,
    default: {}
  },
});
const { open,item } = toRefs(props)

const commonInfo = inject('commonInfo')

const typeTip = computed(() => {
  return props.item.fileType == '0' ? '文件夹名称' : '文件名称'
})

const data = reactive({
  title: '重命名文件',
  loading: false,
  form: {
    fileId: props.item.id,
    fileName: ''
  },
  formRules: {
    fileName: [
      { required: true, trigger: "blur", message: "名称不能为空" }
    ]
  },
})
const { title,loading,form,formRules } = toRefs(data)

nextTick(() => {
  setTimeout(() => {
    proxy.$refs.firstInputRef.focus()
  }, 100);
})

data.title = props.item.fileType == '0' ? '重命名文件夹' : '重命名文件'

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}

const save = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      data.loading = true;
      if(data.form.fileId){
        renameFolderOrFile(data.form).then(response => {
          data.loading = false;
          emit('update:open',false);
          emit('success',data.form);
          proxy.$modal.msgSuccess("重命名成功");
        }).catch(error => {
          data.loading = false;
          proxy.$modal.msgError("重命名失败");
        });
      }
    }
  });
}

</script>

<style lang="scss" scoped>

</style>