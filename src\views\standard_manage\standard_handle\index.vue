<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="标准标题:" prop="title">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.title" placeholder="请输入标准号或标准名称" />
        </el-form-item>
        <el-form-item label="是否解析:" prop="isAnalysis">
          <el-select v-model="queryParams.isAnalysis" placeholder="请选择" clearable>
            <el-option v-for="dict in yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="文本状态:" prop="isHave">
          <el-select v-model="queryParams.isHave" placeholder="请选择" clearable>
            <el-option v-for="dict in is_upload_standard_file" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="标准类型:" prop="standardTypeList" class="one-column">
          <el-checkbox-group v-model="queryParams.standardTypeList" placeholder="请选择标准类型">
            <el-checkbox v-for="item in bxc_standard_type" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="标准状态:" prop="standardStatusList" class="one-column">
          <el-checkbox-group v-model="queryParams.standardStatusList" placeholder="请选择标准状态">
            <el-checkbox v-for="item in bxc_standard_status" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="发布日期:">
          <el-date-picker
            v-model="publishTime"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="实施日期:">
          <el-date-picker
            v-model="execute"
            :clearable="false"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="" class="one-column">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end" ref="stickyElement" :class="{ sticky: isSticky }">
        <el-button
          v-hasPermi="['standard_manage:standard_handle:batch']"
          @click="handleClick('dialog', 'batchAddStandardVisible')"
          type="primary"
          :disabled="disabled"
          class="iconfont icon-piliangruku"
        >
          批量入库
        </el-button>
        <el-button
          v-hasPermi="['standard_manage:standard_handle:add']"
          @click="handleClick('dialog', 'standardVisible')"
          icon="Plus"
          :disabled="disabled"
        >
          标准入库
        </el-button>
        <el-button
          v-hasPermi="['standard_manage:standard_handle:topic']"
          @click="handleClick('topic')"
          :disabled="ids.length != 1 || disabled"
          class="iconfont icon-bianji1"
        >
          题录编辑
        </el-button>
        <el-button
          v-hasPermi="['standard_manage:standard_handle:content']"
          @click="handleClick('content')"
          icon="Edit"
          :disabled="ids.length != 1 || disabled"
        >
          文本编辑
        </el-button>
        <el-button
          v-hasPermi="['standard_manage:standard_handle:into']"
          @click="handleClick('dialog', 'addSystemVisible')"
          :disabled="ids.length == 0 || disabled"
          class="iconfont icon-daoru"
        >
          导入体系
        </el-button>
        <el-button
          v-hasPermi="['standard_manage:standard_handle:delete']"
          @click="handleClick('del')"
          icon="Delete"
          :disabled="ids.length != 1 || disabled"
        >
          删除
        </el-button>
      </div>
      <div ref="tableElement">
        <el-table @selection-change="handleSelectionChange" :data="tableData" :border="true" class="mt15">
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column fixed :align="'center'" type="selection" width="55" />
          <el-table-column label="序号" fixed width="60">
            <template #default="{ $index }">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="standardCode" fixed label="标准号" show-overflow-tooltip min-width="180">
            <template #default="{ row }">
              <span @click="toDetail(row)" class="c-primary pointer">{{ row.standardCode }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
          <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="120" />
          <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="120" />
          <el-table-column prop="publishDate" label="发布日期" show-overflow-tooltip min-width="120" />
          <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="120" />
          <el-table-column prop="standardAttrName" label="标准性质" show-overflow-tooltip min-width="120" />
          <el-table-column prop="amendName" label="制修订" show-overflow-tooltip min-width="120" />
          <el-table-column prop="repealDate" label="废止日期" show-overflow-tooltip min-width="180" />
          <el-table-column prop="isAnalysisName" label="是否解析" show-overflow-tooltip min-width="120" />
          <el-table-column prop="isHaveName" label="文本状态" show-overflow-tooltip min-width="120" />
          <el-table-column prop="downloadTotal" label="下载总量" show-overflow-tooltip min-width="120" />
          <el-table-column prop="pv" label="查看总量" show-overflow-tooltip min-width="120" />
          <el-table-column prop="updateTime" label="更新日期" show-overflow-tooltip min-width="180" />
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <standard-dialog v-if="standardVisible" v-model:visible="standardVisible" @success="success" />
    <add-system-dialog v-if="addSystemVisible" v-model:visible="addSystemVisible" :form="systemForm" @addSystem="addSystem" />
    <add-standard-dialog
      ref="addStandardRef"
      v-if="addStandardVisible"
      v-model:visible="addStandardVisible"
      v-model:standardType="standardType"
      :isEdit="true"
      @updateData="updateData"
    />
    <batch-add-standard-dialog v-if="batchAddStandardVisible" v-model:visible="batchAddStandardVisible" @updateData="getData" />
    <hint-dialog v-if="hintVisible" v-model:visible="hintVisible" :title="msg" />
    <pop-audit-task
      v-if="auditVisible"
      v-model:open="auditVisible"
      :standardItem="auditRow"
      fromState="4"
      @success="updateContentData"
    />
    <detail-drawer v-if="drawerVisible" v-model:visible="drawerVisible" :standardId="standardId" :standardType="standardType" />
  </div>
</template>

<script setup name="Standard_query/index">
  import { importStandardSystem } from '@/api/standard_manage/standard_system';
  import { getStandardList, delStandard, getStandardDetail, putStandard } from '@/api/standard_manage/standard_query';
  import StandardDialog from '@/views/components/standard_manage/standard_query/StandardDialog.vue';
  import AddSystemDialog from '@/views/components/standard_manage/standard_query/AddSystemDialog.vue';
  import AddStandardDialog from '@/views/components/standard_manage/standard_query/AddStandardDialog.vue';
  import BatchAddStandardDialog from '@/views/components/standard_manage/standard_query/BatchAddStandardDialog.vue';
  import PopAuditTask from '@/views/components/standard_analysis/analysis_audit/PopAuditTask';
  import HintDialog from '@/components/HintDialog/index.vue';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_type, bxc_standard_status, yes_no, is_upload_standard_file } = proxy.useDict(
    'bxc_standard_type',
    'bxc_standard_status',
    'yes_no',
    'is_upload_standard_file'
  );

  const addStandardRef = ref(null);

  const data = reactive({
    disabled: false,
    tableLoading: false,
    standardVisible: false,
    msg: '',
    hintVisible: false,
    systemForm: {
      tableData: [],
    },
    addSystemVisible: false,
    standardType: undefined,
    addStandardVisible: false,
    batchAddStandardVisible: false,
    publishTime: [],
    execute: [],
    queryParams: {
      standardTypeList: [],
      standardStatusList: [],
      standardAttrList: [],
      pageNum: 1,
      pageSize: 10,
    },
    tableData: [],
    total: 0,
    multipleSelection: [],
  });

  const {
    disabled,
    tableLoading,
    standardVisible,
    msg,
    hintVisible,
    systemForm,
    addSystemVisible,
    standardType,
    addStandardVisible,
    batchAddStandardVisible,
    publishTime,
    execute,
    queryParams,
    tableData,
    total,
    multipleSelection,
  } = toRefs(data);

  const auditVisible = ref(false);
  const auditRow = ref({});
  const standardId = ref('');
  const drawerVisible = ref(false);
  const timer = ref();

  let ids = computed(() => {
    let arr = multipleSelection.value.map(item => {
      return item.id;
    });
    return arr;
  });

  const stickyElement = ref(null);
  const tableElement = ref(null);
  const isSticky = ref(false);

  // 获取吸顶元素距离文档顶部的初始偏移量
  let stickyOffsetTop = 0;

  onMounted(() => {
    window.addEventListener('resize', updateStickyOffsetTop);
    window.addEventListener('scroll', handleScroll);
    updateStickyOffsetTop(); // 初始化获取吸顶元素的初始偏移量
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateStickyOffsetTop);
    window.removeEventListener('scroll', handleScroll);
  });

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
    if (scrollTop >= stickyOffsetTop) {
      isSticky.value = true;
      stickyElement.value.style.position = 'fixed';
      stickyElement.value.style.top = '0';
    } else {
      isSticky.value = false;
      stickyElement.value.style.position = '';
      stickyElement.value.style.top = '';
    }
  };

  // 更新吸顶元素的初始偏移量
  const updateStickyOffsetTop = () => {
    stickyOffsetTop = stickyElement.value.offsetTop == 0 ? tableElement.value.offsetTop : stickyElement.value.offsetTop;
  };

  const getData = val => {
    multipleSelection.value = [];
    tableLoading.value = true;
    if (val) queryParams.value[val] = 1;
    queryParams.value.standardType =
      data.queryParams.standardTypeList.length > 0 ? queryParams.value.standardTypeList.join(',') : '';
    queryParams.value.standardStatus =
      data.queryParams.standardStatusList.length > 0 ? queryParams.value.standardStatusList.join(',') : '';
    queryParams.value.standardAttr =
      data.queryParams.standardAttrList.length > 0 ? queryParams.value.standardAttrList.join(',') : '';
    queryParams.value.publishStartDate = publishTime.value.length > 0 ? publishTime.value[0] : '';
    queryParams.value.publishEndDate = publishTime.value.length > 0 ? publishTime.value[1] : '';
    queryParams.value.executeStartDate = execute.value.length > 0 ? execute.value[0] : '';
    queryParams.value.executeEndDate = execute.value.length > 0 ? execute.value[1] : '';
    getStandardList(queryParams.value)
      .then(res => {
        let rows = res.rows;
        rows.forEach(item => {
          item.selected = false;
        });
        tableData.value = rows;
        total.value = res.total;
      })
      .finally(() => {
        tableLoading.value = false;
        clearTimeout(timer.value);
      });
  };

  const handleClear = () => {
    queryParams.value.standardType = '';
    queryParams.value.standardStatus = '';
    queryParams.value.standardAttr = '';

    publishTime.value = [];
    execute.value = [];
    proxy.$refs.queryRef.resetFields();
    proxy.$nextTick(() => {
      queryParams.value.standardTypeList = [];
      getData('pageNum');
    });
  };

  const handleSelectionChange = val => {
    multipleSelection.value = val;
  };

  const toDetail = row => {
    standardId.value = row.standardId;
    standardType.value = row.standardType;
    drawerVisible.value = true;
  };

  const handleClick = (type, visible) => {
    disabled.value = true;
    switch (type) {
      case 'dialog':
        if (visible == 'addSystemVisible') {
          systemForm.value.tableData = [...multipleSelection.value];
        }
        data[visible] = true;
        disabled.value = false;
        break;
      case 'topic':
        getStandardDetail(multipleSelection.value[0].standardId)
          .then(res => {
            let data = res.data;
            standardType.value = data.standardType;
            // data.standardTypeCodeIso =
            //   data.standardTypeCodeIso && data.standardTypeCodeIso.length > 0 ? data.standardTypeCodeIso : [];
            // data.standardTypeCodeGb =
            //   data.standardTypeCodeGb && data.standardTypeCodeGb.length > 0 ? data.standardTypeCodeGb : [];
            if (data.standardTextFiles) {
              try {
                data.standardTextFilesList = JSON.parse(data.standardTextFiles);
              } catch (error) {
                data.standardTextFilesList = [];
              }
            }
            addStandardVisible.value = true;
            nextTick(() => {
              addStandardRef.value.setComponentsForm(data);
            });
          })
          .finally(() => {
            disabled.value = false;
          });
        break;
      case 'content':
        if (multipleSelection.value[0].isAnalysis == 0) {
          proxy.$modal.msgWarning('当前标准未解析');
          disabled.value = false;
        } else {
          auditRow.value = multipleSelection.value[0];
          auditVisible.value = true;
          disabled.value = false;
        }
        break;
      case 'del':
        proxy
          .$confirm('确认是否删除此条数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delStandard(ids.value)
              .then(res => {
                toHint('标准删除成功！');
                delayedUpdateData();
              })
              .finally(() => {
                disabled.value = false;
              });
          })
          .catch(() => {
            disabled.value = false;
          });
        break;
      default:
        break;
    }
  };

  const delayedUpdateData = () => {
    timer.value = setTimeout(() => {
      getData('pageNum');
    }, 1100);
  };

  const updateData = value => {
    putStandard(value).then(res => {
      toHint('题录编辑成功！');
      delayedUpdateData();
    });
  };

  const success = msg => {
    toHint(msg);
    delayedUpdateData();
  };

  const updateContentData = msg => {
    if (msg) {
      toHint(msg);
      delayedUpdateData();
    }
  };

  const addSystem = value => {
    importStandardSystem(value).then(res => {
      systemForm.value.tableData = [];
      toHint('导入标准体系成功！');
      delayedUpdateData();
    });
  };

  const toHint = value => {
    msg.value = value;
    hintVisible.value = true;
  };

  watch(
    () => proxy.$route.query,
    val => {
      if (val.sType) {
        data.queryParams.standardTypeList = val.sType == '-1' ? [] : [val.sType];
        getData();
      }
    },
    { immediate: true, deep: true }
  );

  getData();
</script>

<style lang="scss" scoped>
  .icon-bianji1:before,
  .icon-piliangruku:before,
  .icon-daoru:before {
    margin-right: 6px;
  }

  .sticky {
    display: flex;
    align-items: center;
    height: 70px;
    padding: 0 20px;
    box-sizing: border-box;
    background-color: #fff;
    z-index: 99;
    left: 240px;
    right: 20px;
  }
</style>
