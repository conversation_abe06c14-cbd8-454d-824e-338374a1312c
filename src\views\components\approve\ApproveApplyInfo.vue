<template>
  <div class="apply-info-wrap">
    <div class="c-33 f-18 f-bold">申请信息</div>
    <el-descriptions class="mt15" title="" :column="2" :border="true">
      <el-descriptions-item label="申请流程">{{ info.procDefName }}</el-descriptions-item>
      <el-descriptions-item label="申请人">{{ info.startUserName }}</el-descriptions-item>
      <el-descriptions-item label="所在部门">{{ info.startDeptName }}</el-descriptions-item>
      <el-descriptions-item label="申请时间">{{ info.processCreateTime }}</el-descriptions-item>
      <el-descriptions-item label="审批状态">{{ info.processStatusName }}</el-descriptions-item>
      <el-descriptions-item v-if="info.processFinishTime" label="完成时间">{{ info.processFinishTime }}</el-descriptions-item>
      <el-descriptions-item label="申请说明" :span="2">{{ info.applyExplain }}</el-descriptions-item>
      <el-descriptions-item label="附件" :span="2">
        <ele-upload-image
          v-if="info.applyTextFilesList && info.applyTextFilesList.length > 0"
          :isShowUploadIcon="false"
          v-model:value="info.applyTextFilesList"
        />
        <span v-else>无</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>
  import EleUploadImage from '@/components/EleUploadImage';

  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  });
  const { info } = toRefs(props);
</script>

<style lang="scss" scoped></style>
