<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
        <el-form-item label="标准号:" prop="standardCode">
          <el-input v-model="queryParams.standardCode" placeholder="请输入标准号" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="标准名称:" prop="standardName">
          <el-input v-model="queryParams.standardName" placeholder="请输入标准名称" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="标准类型:" prop="standardType">
          <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
            <el-option v-for="dict in bxc_standard_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="更新方式:" prop="updateStyle">
          <el-select v-model="queryParams.updateStyle" placeholder="请选择更新方式" clearable>
            <el-option v-for="dict in bxc_update_style" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人员:" prop="createId">
          <el-select v-model="queryParams.createId" placeholder="请选择操作人员" clearable>
            <el-option v-for="dict in personList" :key="dict.createId" :label="dict.createBy" :value="dict.createId" />
          </el-select>
        </el-form-item>
        <el-form-item label="更新日期:">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="selectDate"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="container-bar mt20">
        <div class="bar-left">
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>
    <div class="app-container-content mt15">
      <el-table v-loading="loading" ref="tableRef" :data="dataList" class="mt15" :border="true">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" label="标准号" min-width="200" fixed="left" show-overflow-tooltip>
          <template #default="scope">
            <span @click.stop="handleStandardCode(scope.row)" class="f-14 c-primary pointer">
              {{ scope.row.standardCode }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="standardName" label="标准名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
        <el-table-column prop="standardStatusName" label="标准状态" min-width="120" show-overflow-tooltip />
        <el-table-column prop="beReplacedStandardCode" label="被替代标准号" min-width="180" show-overflow-tooltip />
        <el-table-column prop="beReplacedStandardName" label="被替代标准名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="updateStyleName" label="更新方式" min-width="120" show-overflow-tooltip />
        <el-table-column prop="content" label="更新内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="更新时间" min-width="180" show-overflow-tooltip />
        <el-table-column prop="createBy" label="操作人员" min-width="120" show-overflow-tooltip />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="selectRow.standardId"
      :standardType="selectRow.standardType"
    />
  </div>
</template>

<script setup>
  import { getStandardSyncList, getOpPersonList } from '@/api/standard_sync/standard_sync';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_type, bxc_update_style } = proxy.useDict('bxc_standard_type', 'bxc_update_style');

  const data = reactive({
    loading: false,
    dateRange: [],
    total: 0,
    dataList: [],
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      standardCode: undefined,
      standardName: undefined,
      standardType: undefined,
      createId: undefined,
      startTime: undefined,
      endTime: undefined,
      updateStyle: undefined, //0导入更新 1系统更新 2手动更新
    },
    personList: [],
  });
  const { loading, dateRange, total, dataList, queryParams, personList } = toRefs(data);

  const selectRow = ref({});
  const drawerVisible = ref(false);

  const resetQuery = () => {
    data.dateRange = [];
    proxy.resetForm('queryFormRef');
    selectDate();
    handleQuery();
  };
  /** 搜索按钮操作 */
  const handleQuery = () => {
    data.queryParams.pageNum = 1;
    getList();
  };

  const getList = () => {
    data.loading = true;
    getStandardSyncList(data.queryParams)
      .then(response => {
        if (response.rows) {
          data.dataList = response.rows;
          data.total = response.total;
        }
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };

  const handleStandardCode = row => {
    selectRow.value = row;
    drawerVisible.value = true;
  };
  const selectDate = () => {
    if (data.dateRange && data.dateRange.length > 0) {
      data.queryParams.startTime = data.dateRange[0];
      data.queryParams.endTime = data.dateRange[1];
    } else {
      data.queryParams.startTime = undefined;
      data.queryParams.endTime = undefined;
    }
  };
  const getPersonList = () => {
    getOpPersonList({})
      .then(response => {
        if (response.data) {
          data.personList = response.data;
        }
      })
      .catch(() => {});
  };

  getList();
  getPersonList();
</script>

<style lang="scss" scoped></style>
