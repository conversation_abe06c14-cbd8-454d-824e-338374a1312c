<template>
  <div class="standard-query-list">
    <template v-if="list.length > 0">
      <div v-for="item in list" :key="item.standardId" @click="handleClick(item)" class="standard-query-item f-14">
        <div class="c-33 flex-shrink mr20">{{item.standardCode}}</div>
        <div class="c-33 title overflow-ellipsis">{{item.standardTitle}}</div>
        <div class="num-wrap c-99 flex-shrink flex flex-ai-center">
          <el-icon class="f-18"><View /></el-icon>
          <span class="ml10">{{item.pv || '0'}}</span>
        </div>
      </div>
    </template>
    <template v-else>
      <empty />
    </template>

    <!-- 查看 -->
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="standardId"
      :standardType="standardType"
    />
  </div>
</template>

<script setup>
import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer'

const props = defineProps({
  list: {
    type: Array,
    default: []
  }
})
const { list } = toRefs(props)

const standardId = ref('');
const standardType = ref(undefined);
const drawerVisible = ref(false);

const handleClick = (row) => {
  standardId.value = row.standardId;
  standardType.value = row.standardType;
  drawerVisible.value = true;
}
</script>

<style lang="scss" scoped>
.standard-query-item {
  display: flex;
  height: 58px;
  line-height: 58px;
  border-bottom: 1px solid #E8E8E8;
  overflow: hidden;
  cursor: pointer;
  &:hover div{
    color: $primary-color !important;
  }
  .title{
    margin-right: auto;
    flex: 1;
  }
  .num-wrap{
    margin-left: 60px;
    text-align: right;
  }
  &:last-child{
    border-bottom:0;
  }
}
</style>