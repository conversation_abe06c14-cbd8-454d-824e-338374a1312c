<template>
  <el-table v-loading="loading" :data="tableData" :border="true">
    <el-table-column label="序号" width="60" fixed>
      <template #default="{ $index }">
        {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
      </template>
    </el-table-column>
    <el-table-column label="意见稿" show-overflow-tooltip min-width="200" fixed>
      <template #default="{ row }">
        <div
          v-if="row.draftOpinionFileList && row.draftOpinionFileList.length > 0"
          @click.stop="handleClick(row, 'look')"
          class="underline"
        >
          {{ row.draftOpinionFileList[0].name }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="编制说明" show-overflow-tooltip min-width="200" fixed>
      <template #default="{ row }">
        <div
          v-if="row.compilationInstructionFileList && row.compilationInstructionFileList.length > 0"
          @click.stop="handleClick(row, 'look')"
          class="underline"
        >
          {{ row.compilationInstructionFileList[0].name }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="征求类型" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        {{ row.typeName }}
        <el-tooltip v-if="row.type == 1" effect="dark" :content="row.solicitUser" placement="bottom-start">
          <el-icon class="c-99"><InfoFilled /></el-icon>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column label="反馈数/征求数" show-overflow-tooltip min-width="150">
      <template #default="{ row }">{{ row.feedbackUserCount || '-' }} / {{ row.solicitUserCount || '-' }}</template>
    </el-table-column>
    <el-table-column label="状态" show-overflow-tooltip min-width="120">
      <template #default="{ row }">
        <div>{{ row.solicitStatusName }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="deadline" label="截止时间" show-overflow-tooltip min-width="180" />
    <el-table-column prop="createTime" label="发稿时间" show-overflow-tooltip min-width="180" />
    <el-table-column prop="createName" label="发布人" show-overflow-tooltip min-width="150" />
    <el-table-column v-if="props.activeStep == props.activeStep && !props.isDetail" label="操作" :min-width="180" fixed="right">
      <template #default="{ row }">
        <template v-if="row.solicitStatus == 0">
          <el-button @click.stop="handleClick(row, 'setTime')" type="primary" link>更新截止时间</el-button>
          <el-button @click.stop="handleClick(row, 'cancellation')" type="danger" link>作废</el-button>
        </template>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getData"
  />
  <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
  <edit-deadline-dialog
    v-if="editDeadlineVisible"
    v-model:visible="editDeadlineVisible"
    :id="props.id"
    :deadline="deadline"
    @success="getData"
  />
</template>

<script setup>
  import { getSolicitOpinionDraft, delRevisionFile, cancelSolicitOpinionDraft } from '@/api/standard_revision/manage';
  import PreviewFile from '@/components/PreviewFile';
  import EditDeadlineDialog from '@/views/components/standard_revision/EditDeadlineDialog.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    activeTab: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const loading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    joinId: props.id,
  });
  const total = ref(0);
  const tableData = ref([]);
  const openFile = ref(false);
  const currentUrl = ref('');
  const editDeadlineVisible = ref(false);
  const deadline = ref('');

  const getData = () => {
    loading.value = true;
    getSolicitOpinionDraft(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const getStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 2:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  const handleClick = (row, type) => {
    switch (type) {
      case 'look':
        currentUrl.value = row.draftOpinionFileList[0].url;
        openFile.value = true;
        break;
      case 'down':
        proxy.download('/system/oss/download/' + row.ossId, {}, row.originalName);
        break;
      case 'del':
        proxy
          .$confirm('确认删除文件名称为【' + row.updateFileName + '】的文件？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delRevisionFile({ relationId: props.id, ossId: row.ossId }).then(res => {
              proxy.$modal.msgSuccess('文件删除成功！');
              getData();
            });
          })
          .catch(() => {});
        break;
      case 'cancellation':
        proxy
          .$confirm('确认作废当前征集意见？确认作废后，当前时间即为截止时间。', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            cancelSolicitOpinionDraft(row.id).then(res => {
              proxy.$modal.msgSuccess('意见稿作废成功！');
              getData();
            });
          })
          .catch(() => {});
        break;
      case 'setTime':
        deadline.value = row.deadline;
        editDeadlineVisible.value = true;
        break;
      default:
        break;
    }
  };

  getData();

  defineExpose({
    getData,
    tableData,
  });
</script>

<style lang="scss" scoped>
  .underline {
    color: $primary-color;
    text-decoration: underline;
    text-decoration-color: $primary-color;
  }
</style>
