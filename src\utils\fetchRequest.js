/**
 * 使用fetch API封装的请求工具
 */
const env = import.meta.env;
const baseURL = 'http://36.213.76.124:8873';
// const baseURL = 'http://36.213.76.124:6060';
/**
 * 使用fetch API发送请求，支持流式响应处理
 * @param {Object} config - 配置对象
 * @returns {Object} 包含promise和abort方法的对象
 */
export const fetchRequest = (config = {}) => {
    const {
        url = '',
        method = 'POST',
        data = null,
        headers = {},
        onProgress,        // 数据块回调
        onComplete,    // 流式传输完成回调
        onError,       // 错误回调
        chunkSeparator = '\n', // 数据块分隔符
        timeout = 600000, // 默认超时时间
    } = config;

    // 创建AbortController用于中止请求
    const controller = new AbortController();
    const signal = controller.signal;

    // 标记请求是否被用户主动中止
    let isUserAborted = false;

    // 合并默认头和自定义头
    const defaultHeaders = {
        'Content-Type': 'application/json'
    };
    const mergedHeaders = {
        ...defaultHeaders,
        ...headers
    };

    // 构建fetch选项
    const fetchOptions = {
        method,
        headers: mergedHeaders,
        signal,
    };

    // 如果有数据，添加到请求体
    if (data !== null) {
        fetchOptions.body = JSON.stringify(data);
    }

    // 创建超时处理
    let timeoutId;
    const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
            controller.abort();
            reject(new Error('请求超时'));
        }, timeout);
    });

    // 发送请求
    const requestPromise = new Promise((resolve, reject) => {
        fetch(baseURL + url, fetchOptions)
            .then(response => {
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态码: ${response.status}`);
                }
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder('utf-8');
                let buffer = '';

                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            // 处理剩余缓冲区
                            if (buffer && onProgress) {
                                onProgress(buffer);
                            }
                            onComplete?.();
                            resolve(buffer); // 解析完成，返回完整响应
                            return;
                        }

                        // 解码新数据并添加到缓冲区
                        const chunk = decoder.decode(value, { stream: true });
                        buffer += chunk;

                        // 按分隔符分割数据块
                        const chunks = buffer.split(chunkSeparator);
                        buffer = chunks.pop() || ''; // 保留未完成部分

                        // 处理完整的数据块
                        chunks.forEach(dataChunk => {
                            if (onProgress) {
                                onProgress(dataChunk);
                            }
                        });
                        // 继续读取流
                        readStream();
                    }).catch(error => {
                        clearTimeout(timeoutId);
                        // 区分用户主动中止和其他错误
                        if (isUserAborted) {
                            return;
                        }
                        reject(error);
                    });
                }

                // 开始读取流
                readStream();
            })
            .catch(error => {
                clearTimeout(timeoutId);
                if (isUserAborted) {
                    return;
                }
                onError?.(error);
                reject(error);
            });
    });

    const promise = Promise.race([requestPromise, timeoutPromise]);
    return {
        promise,
        abort: () => {
            clearTimeout(timeoutId);
            isUserAborted = true;
            try {
                controller.abort();
            } catch (error) {
            }
        }
    };
};
// 默认导出fetchRequest
export default fetchRequest;

/**
 * 简化的文件下载函数
 * @param {string} url - 下载URL
 * @param {Object} data - 请求数据
 * @param {string} filename - 文件名
 * @returns {Promise}
 */
export const fetchDownload = (config = {}) => {
    const {
        url = '',
        method = 'POST',
        data = null,
        fileName = 'download.docx'
    } = config;

    return new Promise((resolve, reject) => {
        fetch(baseURL + url, {
            method: method || 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        }).then(response => {
            if (!response.ok) {
                throw new Error('文件不存在或服务器错误');
            }
            return response.blob();
        }).then(blob => {
            // 创建临时下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            resolve(blob);
        }).catch(error => {
            reject(error);
        });
    });
};


