<template>
  <el-drawer title="反馈详情" size="75%" append-to-body lock-scroll v-model="dialogVisible" @close="close">
    <div class="notice-content">
      <div class="h-title">反馈信息</div>
      <el-descriptions class="mt20" :column="2">
        <el-descriptions-item label="反馈用户：" label-align="left">
          {{ form.feedbackUser || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="所属部门：" label-align="left">
          {{ form.feedbackDept || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="反馈内容：" label-align="left">
          {{ form.feedbackContent || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="反馈时间：">
          {{ form.feedbackTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="附件：" :span="2">
          <ele-upload-image
            v-if="form.feedbackFileList && form.feedbackFileList.length > 0"
            class="accept-content"
            :responseFn="handleResponse"
            :isShowUploadIcon="false"
            v-model:value="form.feedbackFileList"
          ></ele-upload-image>
          <div v-else>无</div>
        </el-descriptions-item>
      </el-descriptions>
      <div class="h-title mt20">关联标准</div>
      <el-table class="mt20" :data="dataList" :border="true">
        <el-table-column label="序号" type="index" align="center" width="60" />
        <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip min-width="200" />
        <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="150" />
        <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="150" />
        <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
      </el-table>
      <template v-if="form.processResult != 0">
        <div class="h-title mt20">反馈处理</div>
        <el-descriptions class="mt20" :column="2">
          <el-descriptions-item label="处理说明：" :span="2">
            {{ form.processContent || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="附件：" :span="2">
            <ele-upload-image
              v-if="form.processFileList && form.processFileList.length > 0"
              class="accept-content"
              :responseFn="handleResponse"
              :isShowUploadIcon="false"
              v-model:value="form.processFileList"
            ></ele-upload-image>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="处理人：">
            {{ form.processUser || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="处理时间：">
            {{ form.processTime || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </div>
  </el-drawer>
</template>

<script setup>
  import EleUploadImage from '@/components/EleUploadImage';
  import { standardFeedbackDetail } from '@/api/standard_revision/feedback.js';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    dialogVisible: Boolean,
    id: [String, Number],
  });
  const { dialogVisible, id } = toRefs(props);

  const state = reactive({
    loading: false,
    form: {},
    dataList: [],
  });

  const { title, loading, form, dataList } = toRefs(state);

  onMounted(() => {
    if (id.value) getDetail();
  });

  const getDetail = () => {
    standardFeedbackDetail(id.value).then(res => {
      let data = res.data;
      state.form = data;
      state.dataList = data.standardList;
    });
  };

  const emit = defineEmits(['update:dialogVisible', 'success']);
  const close = () => {
    emit('update:dialogVisible', false);
  };
  const cancel = () => {
    emit('update:dialogVisible', false);
  };
  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };
</script>

<style lang="scss" scoped>
  .ele-upload-image {
    display: block !important;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
