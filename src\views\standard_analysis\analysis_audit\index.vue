<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="标准号" prop="standardCode">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.standardCode" placeholder="请输入标准号" />
        </el-form-item>
        <el-form-item label="标准名称" prop="standardName">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.standardName" placeholder="请输入标准名称" />
        </el-form-item>
        <el-form-item label="标准类型" prop="standardType">
          <el-select v-model="queryParams.standardType" placeholder="请选择标准类型">
            <el-option v-for="item in bxc_standard_type" :key="item.value"
              :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标准状态" prop="standardStatus">
          <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态">
            <el-option v-for="item in bxc_standard_status" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-sb flex-ai-center">
        <div>
          待审核标准：<span class="c-primary">{{extraData.pendingAudit || 0}}</span>
        </div>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" :border="true"
        class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <!-- <el-table-column type="selection" align="center" width="55" /> -->
        <el-table-column label="序号" fixed min-width="60">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="standardCode" fixed label="标准号" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardName" label="标准名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="standardTypeName" label="标准类型" min-width="150" show-overflow-tooltip />
        <el-table-column label="标准状态" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="getStatusColor(row.standardStatus)">
              {{ row.standardStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="standardTextPages" label="文件页数" min-width="120" show-overflow-tooltip />
        <el-table-column label="审核状态" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span style="color: #A200FF;">
              待审核
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="analysisTime" label="解析日期" width="180" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <el-button v-hasPermi="['standard_analysis:analysis_audit:audit']" @click="handleAudit(row)" type="primary" link>
              审核
            </el-button>
            <el-button
              @click.stop="handleDetail(row)"
              link
              class="c-primary"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page-sizes="[10, 30, 50, 100, 200, 300]"
        v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getData" />
    </div>
    
    <!-- 审核 -->
    <pop-audit-task v-if="openAudit" v-model:open="openAudit" :standardItem="currentRow" fromState="1" @success="getData" />
    <!-- 查看 -->
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="standardId"
      :standardType="standardType"
    />
  </div>
</template>

<script setup>
import {
  getAuditList
} from "@/api/standard_analysis/analysis_audit"
import PopAuditTask from "@/views/components/standard_analysis/analysis_audit/PopAuditTask";
import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer'

const { proxy } = getCurrentInstance();

const { bxc_standard_type, bxc_standard_status, bxc_analysis_assign_status } = proxy.useDict(
  "bxc_standard_type",
  "bxc_standard_status",
  "bxc_analysis_assign_status"
);

const tableLoading = ref(false);
const openAudit = ref(false);
const assignTime = ref([]);
const searchStr = ref("");
const selectedList = ref([]);
const standardId = ref('');
const standardType = ref(undefined);
const drawerVisible = ref(false);
const extraData = ref({})
const currentRow = ref({})
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  analysisStatus: 2,
  // assignStatus: '0'
});
const tableData = ref([]);
const total = ref(0);

const getData = (val) => {
  tableLoading.value = true;
  if (val) queryParams.value[val] = 1;
  queryParams.value.assignStartDate =
    assignTime.value.length > 0 ? assignTime.value[0] : "";
  queryParams.value.assignEndDate =
    assignTime.value.length > 0 ? assignTime.value[1] : "";
  getAuditList(queryParams.value)
    .then((res) => {
      tableData.value = res.rows;
      total.value = res.total;
      extraData.value = res.bean || {}
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const handleClear = () => {
  proxy.$refs.queryRef.resetFields();
  assignTime.value = [];
  searchStr.value = '';
  getData("pageNum");
};
const handleSelectionChange = (list) => {
  selectedList.value = list;
};

const handleAudit = (row) => {
  currentRow.value = row
  openAudit.value = true
}

const getStatusColor = (data) => {
  switch (Number(data)) {
      case 0:
        return 'status-intro-blue';
        break;
      case 1:
        return 'status-intro-green';
        break;
      case 3:
        return 'status-intro-gray';
        break;
      case 4:
        return 'status-intro-red';
        break;
      default:
        break;
    }
};
const handleDetail = (row) => {
  standardId.value = row.standardId;
  standardType.value = row.standardType;
  drawerVisible.value = true;
}


getData();
</script>