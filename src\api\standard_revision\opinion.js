import request from '@/utils/request';

// 征求列表
export const getSolicitList = data => {
  return request({
    url: '/process/solicitOpinion/list',
    method: 'get',
    params: data,
  });
};

// 征求详情
export const getSolicitDetail = data => {
  return request({
    url: '/process/solicitOpinion/' + data,
    method: 'get',
  });
};

// 反馈意见列表
export const getRevisionFeedbackList = data => {
  return request({
    url: '/process/solicitOpinionFeedback/list',
    method: 'get',
    params: data,
  });
};

export const recheckData = data => {
  return request({
    url: '/process/solicitOpinionFeedback/listPersonal',
    method: 'get',
    params: data,
  });
};

// 新增反馈意见
export const addOpinionFeedback = data => {
  return request({
    url: '/process/solicitOpinionFeedback',
    method: 'post',
    data: data,
  });
};

// 反馈意见详情
export const getFeedbackDetail = data => {
  return request({
    url: '/process/solicitOpinionFeedback/' + data,
    method: 'get',
  });
};

// 修改反馈意见
export const putOpinionFeedback = data => {
  return request({
    url: '/process/solicitOpinionFeedback',
    method: 'put',
    data: data,
  });
};

export const getOpinionList = data => {
  return request({
    url: '/process/solicitOpinion/list',
    method: 'get',
    params: data,
  });
};
