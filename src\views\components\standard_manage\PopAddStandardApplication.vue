<template>
  <div>
    <el-dialog
      v-model="open"
      :title="title"
      :before-close="handleClose"
      width="40%"
    >
      <el-form
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5"
      >
        <el-form-item label="标准应用名称" prop="applyName">
          <el-input
            size="large"
            maxlength="50"
            v-model="form.applyName"
            placeholder="请输入标准应用名称"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input
            size="large"
            maxlength="4"
            v-model="form.sort"
            placeholder="请输入排序值，1-9999的正整数（按数值由大到小排序）"
          />
        </el-form-item>
        <el-form-item label="应用描述" prop="applyDescription">
          <el-input
            v-model="form.applyDescription"
            :rows="5"
            :show-word-limit="true"
            maxlength="300"
            type="textarea"
            placeholder="请输入标准应用描述说明信息"
          />
        </el-form-item>
        <el-form-item label="可见范围" prop="viewScope">
          <el-select
            style="width: 100%;"
            @change="handleSelect"
            v-model="form.viewScope"
            size="large"
          >
            <el-option
              v-for="item in bxc_apply_scope"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.viewScope == 2"
          label="可见成员"
          prop="userList"
        >
          <b-tags v-model:tags="form.userList" />
        </el-form-item>
        <el-form-item
          v-if="form.viewScope == 1"
          label="可见部门"
          prop="deptList"
        >
        <dept-tag v-model:tags="form.deptList" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getStandardApplicationDetail,addStandardApplication,updateStandardApplication } from "@/api/standard_manage/standard_application";
import BTags from "@/components/BTags";
import DeptTag from "@/views/components/notice_rule/Tags/DeptTag";

const { proxy } = getCurrentInstance();
const { bxc_apply_scope } = proxy.useDict('bxc_apply_scope')
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: Number,
    default: null,
  }
});
const { open, id } = toRefs(props);

const data = reactive({
  loading: false,
  title: '新建应用分类',
  form: {
    id: props.id,
    viewScope: '0',
    userList: [],
    deptList: [],
    sort: undefined,
  },
  
  rules: {
    applyName: [
      {
        required: true,
        message: "请输入标准应用名称",
        trigger: "blur",
      },
    ],
    
    sort: [{ pattern: /^[1-9]{1}[0-9]{0,3}$/, message: "请输入1-9999的正整数", trigger: "blur" }]
  },
});
const { loading, title, form, rules } = toRefs(data);

const handleSelect = (val) => {

};

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      if(data.form.id){
        updateStandardApplication(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess('设置成功')
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
      }else{
        addStandardApplication(form.value)
        .then((res) => {
          proxy.$modal.msgSuccess('新建成功')
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
      }
    }
  });
};

const handleClose = () => {
  proxy.$refs.queryRef.resetFields();
  emit("success");
  emit("update:open", false);
};

const emit = defineEmits(["update:open", "success"]);

const getData = id => {
  data.loading = true;

  getStandardApplicationDetail(id).then(response => {
    if (response.data) {
      data.form = response.data;
    }
    data.loading = false;
  }).catch(() => {
    data.loading = false;
  });
}

if(props.id){
  data.title = '标准应用分类设置'
  getData(props.id)
}else{
  data.title = '新建应用分类'
}
</script>

<style lang="scss" scoped>
</style>