<template>
  <div class="bxc-editor-wrap">
    <DocumentEditor
      id="docEditor"
      :documentServerUrl="documentServerUrl"
      :config="config"
      :events_onDocumentReady="onDocumentReady"
      :onLoadComponentError="onLoadComponentError"
    />
  </div>
</template>
<script setup>
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import { getEditorConfig } from "@/api/editor";

const { proxy } = getCurrentInstance();
const route = useRoute();
const documentServerUrl = `${process.env.VITE_DOCUMENT_SERVER}`;
const config = ref({})
const params = ref({})
const id = ref('')

const getConfig = () => {
  if(!id.value) return;

  params.value.id = id.value;
  getEditorConfig(params.value).then(res => {
    config.value = res.data;  
  })
}
const onDocumentReady = () => {
  // console.log("Document is loaded");
}
const onLoadComponentError = (errorCode, errorDescription) => {
  switch (errorCode) {
    case -1: // Unknown error loading component
      console.log(errorDescription);
      break;

    case -2: // Error load DocsAPI from http://documentserver/
      console.log(errorDescription);
      break;

    case -3: // DocsAPI is not defined
      console.log(errorDescription);
      break;
  }
}
onMounted(() => {
  if(!route.query.id) {
    proxy.$modal.msgError('请选择要编辑的文档');
    setTimeout(() => {
      window.close()
    }, 1000); 
  }else{
    id.value = route.query.id;
  
    getConfig()
  }
})
</script>
<style lang="scss" scoped>
.bxc-editor-wrap {
  padding: 0;
  margin: 0;
  height: 100vh;
  width: calc(100vw - 0px);
}
</style>