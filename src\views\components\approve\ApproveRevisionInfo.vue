<template>
  <div class="revision-info-wrap">
    <div class="c-33 f-18 f-bold">立项信息</div>
    <el-descriptions class="mt15" title="" :column="2" :border="true">
      <el-descriptions-item label="项目编号">{{ info.projectCode }}</el-descriptions-item>
      <el-descriptions-item label="项目名称">{{ info.projectName }}</el-descriptions-item>
      <el-descriptions-item label="项目负责人">{{ info.projectManagerName }}</el-descriptions-item>
      <el-descriptions-item label="制修订类型">{{ info.amendName }}</el-descriptions-item>
      <el-descriptions-item label="标准号">{{ info.standardCode }}</el-descriptions-item>
      <el-descriptions-item label="标准名称">{{ info.standardName }}</el-descriptions-item>
      <template v-if="info.amend == '1'">
        <el-descriptions-item label="替代标准号">{{ info.beReplacedStandardCode }}</el-descriptions-item>
        <el-descriptions-item label="替代标准名称">{{ info.beReplacedStandardName }}</el-descriptions-item>
      </template>
      <el-descriptions-item label="计划开始日期">{{ proxy.parseTime(info.planStartTime, '{y}-{m}-{d}') }}</el-descriptions-item>
      <el-descriptions-item label="计划完成日期">{{ proxy.parseTime(info.planEndTime, '{y}-{m}-{d}') }}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{ info.createByName }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ info.createTime }}</el-descriptions-item>
      <el-descriptions-item label="可见范围" :span="2">{{ info.viewScopeName }}</el-descriptions-item>
      <el-descriptions-item label="项目介绍" :span="2">{{ info.projectIntroduce }}</el-descriptions-item>
    </el-descriptions>
    <div class="c-33 f-18 f-bold mt20">项目成员</div>
    <el-table :data="info.memberList" :border="true" class="mt15">
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" type="index" fixed width="55" />
      <el-table-column prop="name" label="姓名" show-overflow-tooltip fixed min-width="150" />
      <el-table-column prop="contactNumber" label="联系方式" show-overflow-tooltip min-width="150" />
      <el-table-column prop="memberTypeName" label="成员类型" show-overflow-tooltip min-width="120">
        <template #default="{ row }">
          {{ row.memberType == 0 ? '内部成员' : '外部成员' }}
        </template>
      </el-table-column>
      <el-table-column prop="unit" label="所在部门/单位" show-overflow-tooltip min-width="180" />
    </el-table>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    info: {
      type: Object,
      default: {},
    },
  });
  const { info } = toRefs(props);
</script>

<style lang="scss" scoped></style>
