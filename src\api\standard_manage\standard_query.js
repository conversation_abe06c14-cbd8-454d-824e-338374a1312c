import request from '@/utils/request';

// 标准查询列表
export const getStandardList = data => {
  return request({
    url: '/process/common/standard/list',
    method: 'get',
    params: data
  });
};

// 删除标准
export const delStandard = data => {
  return request({
    url: '/process/common/standard/deleteBatch',
    method: 'post',
    data: data,
  });
};

// 标准分类
export const getStandardType = data => {
  return request({
    url: '/process/standard/type/tree',
    method: 'get',
    params: data,
  });
};

// 新增标准
export const addStandard = data => {
  return request({
    url: '/process/common/standard/saveBatch',
    method: 'post',
    data: data,
  });
};

// 标准详情
export const getStandardDetail = data => {
  return request({
    url: '/process/common/standard/queryDetailsInfo/' + data,
    method: 'get',
  });
};

// 获取最新标准详情
export const getNowStandardDetail = data => {
  return request({
    url: '/process/common/standard/queryDetailsTransactional/' + data,
    method: 'get',
  });
};

// 更新标准
export const putStandard = data => {
  return request({
    url: '/process/common/standard/update',
    method: 'put',
    data: data
  });
};

// 根据标准号查询详情
export const getQueryStandardDetail = data => {
  return request({
    url: '/process/common/standard/network/queryOne',
    method: 'get',
    params: data
  });
};

// 新增反馈
export const addOpinion = data => {
  return request({
    url: '/process/standardFeedbackInfo',
    method: 'post',
    data: data
  });
};

// 搜索命中目次
export const getHighlightCatalogue = data => {
  return request({
    url: '/process/standard/analysis/search/index',
    method: 'get',
    params: data
  });
};

export const getAnalysisDetail = data => {
  return request({
    url: '/process/common/standard/getAnalysisDetail/' + data,
    method: 'get',
  });
};

export const getAnalysisIndexDetail = data => {
  return request({
    url: '/process/common/standard/getAnalysisIndexDetail',
    method: 'get',
    params: data
  });
};

export const getHighLightAnalysisIndex = data => {
  return request({
    url: '/process/common/standard/getHighLightAnalysisIndex',
    method: 'get',
    params: data
  });
};

export const getStandardAtlas = data => {
  return request({
    url: '/process/common/standard/getStandardAtlas/' + data,
    method: 'get',
  });
};
