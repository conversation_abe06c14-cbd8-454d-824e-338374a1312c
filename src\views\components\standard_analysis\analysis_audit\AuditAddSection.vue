<template>
  <el-form v-loading="loading" ref="formRef" :model="form" :rules="rules" :inline="true" label-width="auto" label-position="top" class="edit-form">
    <el-form-item style="width: 100%;" label="" prop="name">
      <template #label>
        <span>章条名称</span>
        <el-tooltip content="章条名称编写规范：当前章条，若含数字序号，序号与名称中间添加一个空格">
          <el-icon size="16" class="ml10" color="#999999"><InfoFilled /></el-icon>
        </el-tooltip>
      </template>
      <el-input v-model.trim="form.name" maxlength="100" style="width: 100%;" placeholder="请输入章条名称" />
    </el-form-item>
    <el-form-item style="width: 100%;" label="" prop="content">
      <template #label>
        <span>章条内容</span>
        <el-tooltip content='建议：内容编写完成后，使用"清除格式"功能，对数据进行格式化，以保证页面格式一致性！'>
          <el-icon size="16" class="ml10" color="#999999"><InfoFilled /></el-icon>
        </el-tooltip>
      </template>
      <editor v-model="form.content" placeholder="" :style="{width: '100%',height: `calc(100vh - 390px)`}" height="100%" />
    </el-form-item>
    <div class="btns">
      <el-button @click="handleBack">返回</el-button>
      <div v-if="auditStore.opType == 'add'">
        <el-button @click="handleAddAnthor" type="primary" plain>再添加</el-button>
        <el-button @click="handleAdd" type="primary">新增</el-button>
      </div>
      <div v-else-if="auditStore.opType == 'edit'">
        <el-button @click="handleUpdate" type="primary">保存</el-button>
      </div>
    </div>
  </el-form>
</template>
<script setup>
import { getSection } from '@/api/standard_analysis/analysis_audit'
import useAuditStandardStore from '@/store/modules/auditStandard'
import Editor from '@/components/TinymceEditor'
import { processMathText,replaceMathText } from '@/utils/index'
import md5 from 'js-md5'

const { proxy } = getCurrentInstance()

// const props = defineProps({
//   open: Boolean,
// });
// const { open } = toRefs(props)
const auditStore = useAuditStandardStore()

const loading = ref(false)
const form = ref({
  name: '',
  content: ''
});
const rules = reactive({
  name: [{ required: true, message: '请输入章条名称', trigger: 'blur' }],
});
const emit = defineEmits(['update:open'])

const initData = () => {
  auditStore.initMd5 = null
  auditStore.checkMd5 = null
  form.value = {...form.value,...auditStore.sectionInfo}
  
  auditStore.formSection = form.value

  if(auditStore.opType == 'edit'){
    getSectionContent()
  }else if(auditStore.opType == 'add'){
    auditStore.initMd5 = md5(form.value.name+'|'+form.value.content)
  }
}

watch(()=>auditStore.opType,(val)=>{
  if(['add','edit'].includes(val)){
    initData()
  }
})
// onMounted(() => {
//   initData()
// })
const getSectionContent = () => {
  getSection(form.value.id).then(res => {
    form.value.content = res.data.content
    if(res.data) {
      let tmpStr = processMathText(res.data.content)
      form.value.content = tmpStr

      auditStore.initMd5 = md5(form.value.name+'|'+tmpStr)
    }
  })
}

const handleBack = () => {
  auditStore.checkIsModifySection().then((res) => { 
    close()
  }).catch(() => {
    close()
  })
}
const close = () => {
  auditStore.opType = ''
  auditStore.sectionInfo = {}
  auditStore.getAuditData(auditStore.standardId)
  emit('update:open',false)
}

const handleAddAnthor = () => {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      loading.value = true;
      auditStore.addAuditSection().then(res => {
        proxy.$nextTick(() => {
          form.value = {...auditStore.sectionInfo,name:'',content:''}
          auditStore.formSection = form.value
          auditStore.initMd5 = md5(form.value.name+'|'+form.value.content)
        })
      }).finally(() => {
        loading.value = false;
      });
    }
  });
  
}
const handleAdd = () => {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      loading.value = true;
      auditStore.addAuditSection().then(res => {
        close()
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}
const handleUpdate = () => {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      loading.value = true;
      auditStore.updateAuditSection().then(res => {
        close()
      }).finally(() => {
        loading.value = false;
      });
    }
  });
}

</script>
<style lang="scss" scoped>
.edit-form{
  padding: 10px 20px 0 20px;
}
:deep(.el-form-item){
  margin-right: 0px !important;
}
.btns{
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding-bottom: 18px;
}
</style>