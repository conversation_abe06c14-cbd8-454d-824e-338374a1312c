<template>
  <div class="intro">
    <div class="flex flex-sb flex-ai-center">
      <div class="flex-1">
        <div class="f-22 c-33 f-bold">{{ form.standardCode || '-' }} | {{ form.standardName || '-' }}</div>
        <div v-if="form.standardNameEn" class="f-16 c-99 mt10">{{ form.standardNameEn }}</div>
        <div class="flex mt15">
          <div v-if="form.standardTypeName" class="intro-label label-blue">{{ form.standardTypeName }}</div>
          <div v-if="form.standardStatusName" class="intro-label" :class="getStatusColor(form.standardStatus)">
            {{ form.standardStatusName }}
          </div>
          <div v-if="form.standardTextPages" class="intro-label label-yellow">{{ form.standardTextPages }}页</div>
          <div
            v-if="form.isArticleRepeal == 1 && (form.standardStatus == 0 || form.standardStatus == 1)"
            class="intro-label label-orange"
          >
            条文废止
          </div>
          <div class="intro-label label-bluishviolet">查看{{ form.pv || '0' }}次</div>
          <div class="intro-label label-purplishred">下载{{ form.downloadTotal || '0' }}次</div>
        </div>
      </div>
      <div class="intro-tool">
        <div v-hasPermi="['standard_manage:standard_query:collect']" @click="handleTool('collect')" class="intro-tool-item">
          <span
            class="iconfont"
            :class="form.isCollect == 1 ? 'icon-shoucangtianchong c-FFB400' : 'icon-shoucang11 c-primary'"
          ></span>
          <div class="intro-tool-item-title">{{ form.isCollect == 1 ? '已收藏' : '收藏' }}</div>
        </div>
        <div v-hasPermi="['standard_manage:standard_query:download']" @click="handleTool('download')" class="intro-tool-item">
          <span class="iconfont icon-xiazaimoban c-primary"></span>
          <div class="intro-tool-item-title">下载</div>
        </div>
        <div v-hasPermi="['standard_manage:standard_query:opinion']" @click="handleTool('opinion')" class="intro-tool-item">
          <span class="iconfont icon-yijianfankui1 c-primary"></span>
          <div class="intro-tool-item-title">反馈</div>
        </div>
      </div>
    </div>
    <el-divider />
    <standard-opinion-dialog v-if="opinionVisible" v-model:visible="opinionVisible" :tableData="[{ ...form }]" @toHint="toHint" />
    <hint-dialog v-if="hintVisible" v-model:visible="hintVisible" :title="msg" />
  </div>
</template>

<script setup>
  import { userCollect } from '@/api/common';
  import HintDialog from '@/components/HintDialog/index.vue';
  import StandardOpinionDialog from '@/views/components/standard_manage/standard_query/StandardOpinionDialog';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const { form } = toRefs(props);

  const msg = ref('');
  const opinionVisible = ref(false);
  const hintVisible = ref(false);

  const getStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-intro-blue status-intro-bgblue';
        break;
      case 1:
        return 'status-intro-green status-intro-bggreen';
        break;
      case 3:
        return 'status-intro-gray status-intro-bggray';
        break;
      case 4:
        return 'status-intro-red status-intro-bgred';
        break;
      default:
        break;
    }
  };

  const handleTool = type => {
    switch (type) {
      case 'download':
        try {
          let files = JSON.parse(form.value.standardTextFiles) ? JSON.parse(form.value.standardTextFiles) : [];
          let name = form.value.standardCode;
          if (files && files.length > 1) {
            name += '.zip';
          } else {
            name = files[0].name;
          }
          proxy
            .download(
              '/process/userDownloadInfo/download',
              {
                recordId: form.value.standardId,
                recordType: '0',
              },
              name
            )
            .then(() => {
              emit('updateData');
            });
        } catch (error) {
          proxy.$message.warning('暂无标准文件！');
        }
        break;
      case 'collect':
        userCollect({
          recordId: form.value.standardId,
          recordType: 0,
          // 取消收藏: 0   收藏: 1
          isCollect: form.value.isCollect == 1 ? 0 : 1,
        }).then(res => {
          toHint(form.value.isCollect == 1 ? '标准信息取消收藏成功！' : '标准信息收藏成功！');
          emit('updateData');
        });

        break;
      case 'opinion':
        opinionVisible.value = true;
        break;
      default:
        break;
    }
  };

  const toHint = value => {
    msg.value = value;
    hintVisible.value = true;
  };

  const emit = defineEmits(['updateData']);
</script>

<style lang="scss" scoped>
  .intro {
    &-label {
      font-size: 14px;
      // width: 91px;
      padding: 0 15px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      margin-right: 12px;
    }

    &-progress {
      position: relative;

      &-time {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        white-space: nowrap;
      }

      &-title {
        position: absolute;
        bottom: -26px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 15px;
        white-space: nowrap;
      }
    }

    &-line {
      flex: 1;
      max-width: 300px;
      height: 4px;
    }

    &-tool {
      display: flex;
      align-items: center;
      text-align: center;

      &-item {
        cursor: pointer;

        &:not(:first-child) {
          margin-left: 30px;
        }

        &-title {
          font-size: 14px;
          color: #333333;
          margin-top: 5px;
        }
      }
    }
  }

  .iconfont {
    font-size: 22px;
  }

  .label-blue {
    color: #0372ff;
    background-color: #e8f2ff;
  }

  .label-yellow {
    color: #ffb400;
    background-color: #fff4d8;
  }

  .plr25 {
    padding: 0 25px;
    box-sizing: border-box;
  }

  .c-gray {
    color: #c8c8c8;
  }

  .bgc-gray {
    background-color: #c8c8c8;
  }

  .pl50 {
    padding-left: 50px;
    box-sizing: border-box;
  }

  .pb70 {
    padding-bottom: 70px;
    box-sizing: border-box;
  }

  .icon-wenjianyulan:before {
    margin-right: 6px;
  }

  .label-bluishviolet {
    color: #7800ff;
    background-color: #e8d8fa;
  }

  .label-orange {
    color: #ff4f00;
    background-color: #ffe2d5;
  }

  .label-purplishred {
    color: #c600ff;
    background-color: #fee5fe;
  }

  :deep(.el-divider--horizontal) {
    margin: 30px 0;
  }
</style>
