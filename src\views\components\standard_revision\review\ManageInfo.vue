<template>
  <div class="h-title mt30">基础信息</div>
  <el-descriptions :column="3" class="mt20">
    <el-descriptions-item label="项目编号：">{{ form.projectCode || '-' }}</el-descriptions-item>
    <el-descriptions-item label="项目名称：">{{ form.projectName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="项目负责人：">{{ form.projectManagerName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="计划完结日期：">{{ form.planFinishDate || '-' }}</el-descriptions-item>
    <el-descriptions-item label="创建人：">{{ form.createName || '-' }}</el-descriptions-item>
    <el-descriptions-item label="创建时间：">{{ form.createTime || '-' }}</el-descriptions-item>
  </el-descriptions>
  <div class="flex flex-sb mt30">
    <div class="h-title">复审标准</div>
    <div class="flex">
      <el-input v-model="queryParams.keyword" placeholder="请输入标准号或标准名称">
        <template #append>
          <el-button @click="handleQuery" icon="Search" class="img-icon" />
        </template>
      </el-input>
      <el-button plain @click="handleClear" icon="Refresh" class="f-18 ml10"></el-button>
    </div>
  </div>
  <el-table :data="tableData" :border="true" class="mt15">
    <el-table-column label="序号" type="index" fixed width="55" />
    <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip fixed min-width="200" />
    <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
    <el-table-column label="标准状态" show-overflow-tooltip min-width="150">
      <template #default="{ row }">
        <span :class="getStatusColor(row.standardStatus)">{{ row.standardStatusName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="reviewBasisName" label="复审依据" show-overflow-tooltip min-width="200" />
  </el-table>
  <pagination
    v-show="total > 0"
    :total="total"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
</template>

<script setup>
  import { getReviewDetail, getStandardList } from '@/api/standard_revision/review';

  const props = defineProps({
    id: {
      type: [String, Number],
      required: true,
    },
    flowStatus: { type: Number },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    reviewId: props.id,
  });
  const form = ref({});
  const tableData = ref([]);
  const total = ref(0);

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-blue';
        break;
      case 1:
        return 'status-green';
        break;
      case 3:
        return 'status-gray';
        break;
      case 4:
        return 'status-red';
        break;
      default:
        break;
    }
  };

  getReviewDetail(props.id).then(res => {
    form.value = res.data;
  });

  const handleClear = () => {
    queryParams.value.keyword = '';
    handleQuery();
  };

  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  const getList = () => {
    getStandardList(queryParams.value).then(res => {
      tableData.value = res.rows;
      total.value = res.total;
    });
  };

  getList();
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }

  :deep(.el-input-group__append) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  .img-icon :deep(.el-icon) {
    color: #fff !important;
    font-size: 18px !important;
    position: relative !important;
    top: -2px !important;
  }

  :deep(.el-input-group__append .el-button:focus) {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
    box-shadow: none !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
