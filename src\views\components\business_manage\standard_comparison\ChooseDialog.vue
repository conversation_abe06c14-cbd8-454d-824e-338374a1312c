<template>
  <el-dialog width="1100px" title="选择标准" append-to-body v-model="visible" :close-on-click-modal="false"
    :close-on-press-escape="false" :before-close="handleClose">
    <div class="flex flex-ai-center mb20">
      <el-input style="width: 300px; margin-right: 20px" v-model="queryParams.keyword" placeholder="请输入标准号/标准名称" clearable />
      <el-button :loading="loading" @click="handleSearch" type="primary" icon="Search">查询</el-button>
    </div>
    <el-table v-if="visible" ref="tableRef" v-loading="loading" :data="tableData" :border="true" row-key="id"
      highlight-current-row @row-click="handleRowClick">
      <template v-slot:empty>
        <empty />
      </template>
      <!-- <el-table-column type="selection" /> -->
      <el-table-column label="序号" type="index" width="70" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip />
      <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip />
      <el-table-column prop="standardTextPages" label="页数" show-overflow-tooltip width="180" />
      <!-- <el-table-column prop="size" label="大小" show-overflow-tooltip width="200" /> -->
    </el-table>
    <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
    <template #footer>
      <div class="dialog-footer">
        <el-button type="info" @click="handleClose">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { analysisReviewList } from '@/api/business_manage/standard_comparison';
const { proxy } = getCurrentInstance();

const props = defineProps({});
const selectTableData = ref([]);
const type = ref(1);
const tableRef = ref();
const visible = ref(false);
const loading = ref(false);
const btnLoading = ref(false);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  analysisStatus: 5,
  keyword: '',
  searchStandardIdList: [],
});
const tableData = ref([]);
const total = ref(0);
const selectedRows = ref([]);
// 搜索
const handleSearch = () => {
  getList(1);
};

// 列表
const getList = val => {
  loading.value = true;
  if (val) queryParams.value.pageNum = 1;
  analysisReviewList(queryParams.value)
    .then(res => {
      tableData.value = res.rows;
      total.value = res.total;
      selectedRows.value.length && tableRef.value.setCurrentRow(selectedRows.value[0]);
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleRowClick = selection => {
  selectedRows.value = [selection];
};

// 确定
const handleConfirm = () => {
  btnLoading.value = true;
  if (!selectedRows.value.length) {
    proxy.$message.warning('请选择标准');
    btnLoading.value = false;
  } else {
    emit('chooseData', selectedRows.value);
    handleClose();
  }
};

// 打开弹框
const open = async (val, origina, data) => {
  selectedRows.value = data || [];
  queryParams.value.keyword = '';
  visible.value = true;
  type.value = val;
  if (type.value == 2) {
    queryParams.value.searchStandardIdList = origina.map(item => item.id);
  } else {
    queryParams.value.searchStandardIdList = [];
  }
  await getList(1);
};
const handleClose = () => {
  visible.value = false;
  btnLoading.value = false;
};

const emit = defineEmits(['chooseData']);

defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
