<template>
  <div class="intro">
    <div class="flex flex-ai-center f-22 c-33 f-bold">
      <span>{{ form.projectCode }}</span>
      <span>&nbsp;|&nbsp;</span>
      <span>{{ form.projectName || '-' }}</span>
      <div class="intro-label">{{ form.amendName }}</div>
    </div>
    <el-divider />
    <div v-if="form.projectIntroduce" class="f-16 c-66">{{ form.projectIntroduce || '-' }}</div>
  </div>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
</script>

<style lang="scss" scoped>
  .intro {
    background-color: #ffffff;
    border-radius: 15px;

    &-label {
      color: #ffffff;
      font-size: 14px;
      width: 55px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      background: $primary-color;
      border-radius: 5px;
      margin-left: 12px;
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 20px 0 !important;
  }
</style>
