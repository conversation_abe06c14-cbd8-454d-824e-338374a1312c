<template>
  <div class="app-container">
    <div class="comparison">
      <div class="main">
        <el-row :gutter="80">
          <el-col :md="12" :xs="24">
            <div class="flex flex-ai-center mb10">
              <span class="c-F20000">*</span>
              <span>原标准</span>
            </div>
            <div class="flex flex-ai-center flex-jc-between borders" @click="handleOpenDialog(1)">
              <div class="flex-1 w100 mr10">
                <div class="overflow-ellipsis" v-if="originalData.length > 0">{{ originalData[0].standardName }}</div>
                <div v-else class="c-99">请选择原标准</div>
              </div>
              <el-icon>
                <Search />
              </el-icon>
            </div>
          </el-col>
          <el-col :md="12" :xs="24">
            <div class="flex flex-ai-center mb10">
              <span class="c-F20000">*</span>
              <span>比对标准</span>
            </div>
            <div class="flex flex-ai-center flex-jc-between borders" @click="handleOpenDialog(2)">
              <div class="flex-1 w100 mr10">
                <div class="overflow-ellipsis" v-if="compareData.length > 0">{{ compareData[0].standardName }}</div>
                <div v-else class="c-99">请选择比对标准</div>
              </div>
              <el-icon>
                <Search />
              </el-icon>
            </div>
          </el-col>
        </el-row>
        <div class="mt40 nav_bars mb30">
          <div>
            <span class="c-F20000">*</span>
            比对主题
          </div>
          <div class="flex flex-ai-center">
            <el-switch v-model="switchValue" active-text="中中" inactive-text="中英" @change="handleChangeSwitch" />
            <div class="add" @click="handleAdd">
              <el-button icon="Plus">添加主题</el-button>
            </div>
          </div>
        </div>
        <el-scrollbar height="290px" ref="scrollbarRef">
          <div class="flex flex-ai-center mb20 pr15" v-for="(item, index) in sourceList" :key="item.id">
            <div class="label">
              <span>*</span>
              主题{{ index + 1 }}
            </div>
            <el-input
              size="large"
              style="width: calc(50% - 90px)"
              v-model="item.name"
              :placeholder="switchValue ? '请输入标准主题词' : '请输入原标准主题词'"
            />
            <template v-if="!switchValue">
              <div class="mr15 ml15">-</div>
              <el-input size="large" style="width: calc(50% - 90px)" v-model="item.name2" placeholder="请输入比对标准主题词" />
            </template>
            <el-popconfirm title="确定删除吗？" placement="top" @confirm="handleDelete(index)">
              <template #reference>
                <div class="ml20 pl10 pointer">
                  <el-icon color="#F20000">
                    <Delete />
                  </el-icon>
                </div>
              </template>
            </el-popconfirm>
          </div>
        </el-scrollbar>

        <el-button class="cbtn" type="primary" @click="handleOpenDetailsDialog">开始比对</el-button>
      </div>
    </div>
    <!-- 选择比对标准 -->
    <chooseDialog ref="chooseDialogRef" @chooseData="handleChooseData" />

    <!-- 比对详情 -->
    <detailsDialog ref="detailsDialogRef" />
  </div>
</template>

<script setup>
  import { ElLoading } from 'element-plus';
  import { nextTick } from 'vue';
  import chooseDialog from '@/views/components/business_manage/standard_comparison/ChooseDialog.vue';
  import detailsDialog from '@/views/components/business_manage/standard_comparison/DetailsDialog.vue';
  import { startComparing } from '@/api/business_manage/standard_comparison';
  const { proxy } = getCurrentInstance();

  const { bxc_compare_topic } = proxy.useDict('bxc_compare_topic');
  // 比对主题
  const nav_bars = ref([
    {
      name: '章节',
      value: 0,
    },
    {
      name: '编制单位',
      value: 1,
    },
    {
      name: '定义',
      value: 2,
    },
  ]);
  const current_ids = ref([]);
  const handleClick = id => {
    if (current_ids.value.includes(id)) {
      current_ids.value = current_ids.value.filter(item => item !== id);
    } else {
      current_ids.value.push(id);
    }
  };
  const type = ref(1); // 1 原标准 2 比对标准
  const originalData = ref([]); // 原标准
  const compareData = ref([]); // 比对标准

  // 比对主题
  const scrollbarRef = ref(null);
  const switchValue = ref(false);
  const createNewSourceItem = () => {
    return {
      id: new Date().getTime(),
      name: '',
      name2: '',
    };
  };
  const sourceList = ref([createNewSourceItem()]);

  // 选择比对标准
  const chooseDialogRef = ref(null);
  const handleOpenDialog = val => {
    type.value = val;
    chooseDialogRef.value.open(val, originalData.value, val == 1 ? originalData.value : compareData.value);
  };
  const handleChooseData = data => {
    if (type.value == 1) {
      originalData.value = data;
    } else {
      compareData.value = data;
    }
  };
  // 中文正则表达式 (只要包含至少一个中文字符即可)
  const chineseRegex = /^.*[\u4e00-\u9fa50-9].*$/;
  // 英文正则表达式 (允许字母、数字和特殊字符)
  const englishRegex = /^[a-zA-Z0-9\s!@#$%^&*(),.?":{}|<>]+$/;
  // 比对详情
  const detailsDialogRef = ref(null);
  // 开始比对提交
  const handleOpenDetailsDialog = val => {
    if (originalData.value.length == 0 || compareData.value.length == 0) {
      proxy.$message.warning('请选择原标准、比对标准');
      return;
    }
    if (originalData.value[0].id == compareData.value[0].id) {
      proxy.$message.warning('原标准和比对标准不能相同');
      return;
    }

    // 检查sourceList中的每一项是否有值
    const emptyFields = [];
    const invalidLangFields = [];
    sourceList.value.forEach((item, index) => {
      // 检查是否为空
      if (item.name === '') {
        emptyFields.push(`主题${index + 1}项的${switchValue.value ? '标准主题词' : '原标准主题词'}`);
      }
      if (item.name2 === '' && !switchValue.value) {
        emptyFields.push(`主题${index + 1}项的比对标准主题词`);
      }

      // 检查语言类型 (中英模式下)
      if (!switchValue.value) {
        // 第一项必须是中文
        if (item.name && !chineseRegex.test(item.name)) {
          invalidLangFields.push(`主题${index + 1}项的原标准主题词必须输入中文`);
        }
        // 第二项必须是英文
        if (item.name2 && !englishRegex.test(item.name2)) {
          invalidLangFields.push(`主题${index + 1}项的比对标准主题词必须输入英文`);
        }
      } else {
        // 中中模式下，原标准主题词必须是中文
        if (item.name && !chineseRegex.test(item.name)) {
          invalidLangFields.push(`主题${index + 1}项的标准主题词必须输入中文`);
        }
      }
    });

    if (emptyFields.length > 0) {
      proxy.$message.warning(`请输入: ${emptyFields.join('、')}`);
      return;
    }

    if (invalidLangFields.length > 0) {
      proxy.$message.warning(`${invalidLangFields.join('、')}`);
      return;
    }

    let loading = ElLoading.service({
      lock: true,
      text: '比对中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    // 比对数据的名称
    let comparisionInfo = {
      original: originalData.value[0].standardName,
      compare: compareData.value[0].standardName,
    };
    let topicList = sourceList.value.map(item => item.name);
    let topicEnList = sourceList.value.map(item => item.name2);
    startComparing({
      sourceFileId: originalData.value[0].id,
      targetFileId: compareData.value[0].id,
      topicList,
      topicEnList,
      type: switchValue.value ? 1 : 0,
    })
      .then(res => {
        loading.close();
        detailsDialogRef.value.open(val, res.data, comparisionInfo);
        setTimeout(() => {
          MathJax.typesetPromise();
        }, 300);
      })
      .catch(err => {
        loading.close();
      });
  };

  // 添加比对主题
  const handleAdd = () => {
    sourceList.value.push(createNewSourceItem());
    // 等待 DOM 更新完成后滚动到底部
    nextTick(() => {
      if (scrollbarRef.value) {
        const scrollbar = scrollbarRef.value.wrapRef;
        if (scrollbar) {
          scrollbar.scrollTop = scrollbar.scrollHeight;
        }
      }
    });
  };

  // 删除比对主题
  const handleDelete = index => {
    // 必须保留至少一个主题
    if (sourceList.value.length <= 1) {
      proxy.$message.warning('必须保留至少一个主题');
      return;
    }
    sourceList.value.splice(index, 1);
  };

  // 切换中中、中英
  const handleChangeSwitch = val => {
    sourceList.value = [];
    sourceList.value.push(createNewSourceItem());
  };
</script>
<style scoped lang="scss">
  .comparison {
    width: calc(100vw - 220px - 20px - 20px);
    min-height: calc(100vh - 125px);
    display: flex;
    justify-content: center;
    flex-direction: column;
    background-color: #fff;
    padding-left: 10%;

    .main {
      width: 70%;
      max-width: 1200px;
    }

    .borders {
      border: 1px solid #eaeaea;
      padding: 10px;
      border-radius: 4px;
      cursor: pointer;
    }

    .w100 {
      width: 100px;
    }

    .pr15 {
      padding-right: 15px;
    }

    .label {
      width: 80px;
    }

    .nav_bars {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .add {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-left: 50px;
      }
    }

    .pointer {
      cursor: pointer;
    }

    .cbtn {
      height: 40px;
      max-width: 400px;
      width: 50%;
      min-width: 150px;
      margin: 80px auto;
      display: block;
    }
  }
</style>
