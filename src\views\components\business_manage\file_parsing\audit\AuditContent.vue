<template>
  <MarkdownView v-if="auditStore.content" style="padding: 10px 10px 0 10px" :content="content" />
  <el-empty v-else />
</template>

<script setup>
  import MarkdownView from '@/views/components/business_manage/common/MarkdownView.vue';
  import useAuditStandardStore from '@/store/modules/file_parsing';

  const auditStore = useAuditStandardStore();

  const content = ref('');

  watch(
    () => auditStore.content,
    val => {
      if (val) {
        content.value = val
          .replace(/<\?xml/g, '\n\n```\n<?xml')
          .replace(/<\/xs:schema>/g, '</xs:schema>\n\n```\n')
          .replace(/<\/IODEF-Document>/g, '</IODEF-Document>\n\n```\n');
      }
    }
  );
</script>

<style lang="scss">
  .bxcjx-content-wrap {
    .c-title {
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }

    table {
      border-collapse: collapse;
    }

    table,
    th,
    td {
      border: 1px solid #999;
      padding: 8px;
    }

    img {
      max-width: 100%;
      height: auto;
    }
  }

  .markdown-view code.hljs {
    padding-top: 0 !important;
    background-color: #fff !important;
    color: #333 !important;
  }
</style>
