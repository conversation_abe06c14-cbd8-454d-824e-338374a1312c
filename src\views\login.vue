<template>
  <div class="login-wrap">
    <div class="login-main-wrap">
      <div class="login-left">
        <div class="login-logo">
          <img src="@/assets/images/login/login_logo.png" alt="">
        </div>
      </div>
      <div class="login-right">
        <div class="login-form-wrap">
          <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
            <div class="welcome">欢迎登录~</div>
            <h3 class="title">标准数字化平台</h3>
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                type="text"
                size="large"
                auto-complete="off"
                placeholder="账号"
              >
                <template #prefix><svg-icon icon-class="login_user" class="el-input__icon input-icon" /></template>
              </el-input>
            </el-form-item>
            <el-form-item class="mt30" prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                size="large"
                auto-complete="off"
                placeholder="密码"
                show-password
                @keyup.enter="handleLogin"
              >
                <template #prefix><svg-icon icon-class="login_pwd" class="el-input__icon input-icon" /></template>
              </el-input>
            </el-form-item>
            <el-form-item class="verify-wrap mt30" prop="code" v-if="captchaEnabled">
              <el-input
                v-model="loginForm.code"
                size="large"
                auto-complete="off"
                placeholder="验证码"
                @keyup.enter="handleLogin"
              >
                <template #prefix><svg-icon icon-class="login_verify" class="el-input__icon input-icon" /></template>
              </el-input>
              <div class="login-code pointer">
                <img :src="codeUrl" @click="getCode" class="login-code-img"/>
              </div>
            </el-form-item>
            <div class="f-14 m-red mt50 error-wrap">
              {{errorMsg}}
            </div>
            <el-form-item class="mt20" style="width:100%;">
              <el-button
                :loading="loading"
                size="large"
                type="primary"
                style="width:100%;"
                @click.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
            </el-form-item>
          </el-form>
          <div class="mt30 c-primary f-16">“标准数字化平台”演示预约联系电话：400-109-7887</div>
        </div>
      </div>
    </div>
    <div class="login-suggestion">建议使用：1920*1080分辨率/谷歌浏览器访问达到最佳效果 丨技术支持:安徽标信查数据技术有限公司</div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const PROJECT_PREFIX = 'ENTERPRISE_STANDARDIZATION_WEBSITE';

const userStore = useUserStore()
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};
// 协议同意状态
const agreement = ref(true)
const codeUrl = ref("");
const loading = ref(false);
const errorMsg = ref("")
// 验证码开关
const captchaEnabled = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  if (!agreement.value){
    proxy.$modal.msgWarning('需要同意《用户协议与隐私政策》')
    return
  }
  proxy.$refs.loginRef.validate((valid,obj) => {
    if (valid) {
      errorMsg.value = ''
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set(PROJECT_PREFIX+"username", loginForm.value.username, { expires: 30 });
        Cookies.set(PROJECT_PREFIX+"password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set(PROJECT_PREFIX+"rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove(PROJECT_PREFIX+"username");
        Cookies.remove(PROJECT_PREFIX+"password");
        Cookies.remove(PROJECT_PREFIX+"rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        router.push({ path: redirect.value || "/" });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }else{
      let arr = [];
      for (let key in obj) {
        arr.push(obj[key][0].message);
      }
      errorMsg.value = arr[0];
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get(PROJECT_PREFIX+"username");
  const password = Cookies.get(PROJECT_PREFIX+"password");
  const rememberMe = Cookies.get(PROJECT_PREFIX+"rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login-wrap {
  height: 100%;
  background: url("@/assets/images/login/login_bg.png") center center no-repeat;
  background-size: cover;
  display:flex;
  justify-content:center;
  align-items:center;
  position: relative;
  .login-main-wrap {
    width: calc(100vw - 300px);
    height: calc(100vh - 100px);
    min-width: 1200px;
    min-height: 600px;
    border-radius: 30px;
    background: url("@/assets/images/login/login_main.png") center center no-repeat;
    background-size: cover;
    display:flex;
    .login-left{
      flex: 1;
      .login-logo{
        margin-top: 40px;
        margin-left: 55px;
        img{
          height: 66px;
        }
      }
    }
    .login-right{
      flex: 1;
      height: 100%;
      box-sizing:border-box;
      .login-form-wrap{
        height:100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .login-form{
          display:flex;
          flex-direction: column;
          align-items:center;
          width: 500px;
          &:deep(.el-input__wrapper) {
            box-shadow: 0 0 0 0px;
            background: #F8F9FC;
            border-radius: 35px;
          }
          .welcome{
            font-size: 22px;
            font-weight: 500;
            color: #999999;
            margin-bottom: 20px;
            align-self: flex-start;
          }
          .title{
            font-size: 34px;
            font-weight: bold;
            color: #007EFB;
            margin: 0;
            padding: 0;
            padding-bottom: 60px;
            align-self: flex-start;
          }
          :deep(.el-form-item) {
            width: 100%;
            height: 70px;
            line-height: 70px;
            border: none;
            
            .el-input{
              height: 70px;
              line-height: 70px;
              border: none;
              font-size: 16px;
              
            }
            input:-internal-autofill-previewed,
            input:-internal-autofill-selected {
              -webkit-text-fill-color: #333 !important;
              transition: background-color 5000s ease-in-out 0s !important;
            }
            .el-input__prefix{
              margin-left: 20px;
              width: 40px;
            }
            .el-button{
              height: 70px;
              font-size: 22px;
              font-weight: 600;
              background: #007AF5;
              box-shadow: 0px 5px 15px 0px rgba(55,81,254,0.3);
              border-color: #007AF5;
              border-radius: 35px;
            }
            .el-form-item__error{
              display: none;
            }
          }
          .verify-wrap{
            display: flex;
            .el-input{
              flex: 1;
            }
            .login-code{
              margin-left: auto;
              margin-bottom: -15px;
              width: 160px;
              img{
                object-fit: cover;
                width: 160px;
              }
            }
          }
          .privary-wrap{
            align-self: flex-start;
            display: flex;
            align-items: center;
          }          
        }
      }
    }
    .error-wrap{
      height: 20px;
    }
  }
  .login-suggestion {
    color: #FFFFFF;
    position: absolute;
    bottom: 10px;
    font-size: 14px;
  }
}
:deep(.el-input .el-input__password){
  font-size: 18px;
  margin-right: 10px;
}
</style>
