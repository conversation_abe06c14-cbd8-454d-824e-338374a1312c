<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="auto"
        class="demo-form-inline"
        @submit.prevent
      >
        <el-form-item label="关键词:" prop="keyword">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键字" />
        </el-form-item>
        <el-form-item label=" ">
          <el-button
            @click="getList('pageNum')"
            type="primary"
            
            icon="Search"
            >查询</el-button
          >
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="ta-r">
        <el-button
          v-hasPermi="['knowledge:library:add']"
          @click="addDialog = true"
          type="primary"
          icon="Plus"
        >
          新建知识库
        </el-button>
      </div>
      <el-table
        :data="tableData"
        class="mt15"
        :border="true"
      >
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column 
          type="index"
          align="center" 
          label="序号" 
          fixed="left"  
          min-width="55"
        >
          <template #default="scope">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="address" label="知识库封面">
          <template #default="scope">
            <div class="flex-center">
              <el-image 
                :src="scope.row.cover" 
                :preview-src-list="[scope.row.cover]" 
                preview-teleported
                fit="cover"
                class="_cover" 
                alt="" />
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          prop="name" 
          label="知识库名称" 
          show-overflow-tooltip 
        >
          <template #default="scope">
            <span
              @click.stop="handleView(scope.row)"
              class="f-14 c-primary pointer"
            >
              {{ scope.row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="describe"
          label="知识库描述"
          show-overflow-tooltip
          width="450"
        />
        <el-table-column
          prop="createDate"
          label="创建时间"
          show-overflow-tooltip
        />
        <el-table-column prop="fileNum" label="文件数" show-overflow-tooltip />
        <el-table-column prop="address" label="操作" width="180">
          <template #default="scope">
            <el-button
              @click.stop="handleView(scope.row)"
              link
              size="small"
              class="f-14 c-primary"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleSet(scope.row)"
              v-hasPermi="['knowledge:library:edit']"
              link
              size="small"
              class="f-14 c-primary"
            >
              设置
            </el-button>
            <el-button
              @click.stop="handleDelete(scope.row)"
              v-hasPermi="['knowledge:library:remove']"
              link
              size="small"
              type="danger"
              class="f-14 c-F20000"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <add-knowledge-dialog
      v-if="addDialog"
      v-model:dialogVisible="addDialog"
      @getList="getList"
    />
    <set-knowledge-dialog
      v-if="setDialog"
      v-model:id="id"
      v-model:dialogVisible="setDialog"
      @getList="getList"
    />
  </div>
</template>

<script setup>
import {
  knowledgeList,
  delKnowledge,
} from "@/api/knowledge/library";
import AddKnowledgeDialog from "@/views/components/knowledge/library/AddKnowledgeDialog";
import SetKnowledgeDialog from "@/views/components/knowledge/library/SetKnowledgeDialog";

const router = useRouter();
const { proxy } = getCurrentInstance();

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    keyword: null,
  },
  tableData: [],
  total: 0,
  addDialog: false,
  id: null,
  setDialog: false,
});

const { queryParams, tableData, total, addDialog, id, setDialog } =
  toRefs(data);

onMounted(() => {
  getList();
});

const getList = (val) => {
  if (val) queryParams.value[val] = 1;
  knowledgeList(queryParams.value).then((res) => {
    tableData.value = res.rows;
    total.value = res.total;
  });
};

const handleClear = () => {
  proxy.$refs.queryRef.resetFields();
  getList("pageNum");
};

const handleView = (row) => {
  router.push({ path: "main", query: { id: row.id } });
};

const handleRowClick = (row) => {
  router.push({ path: "main", query: { id: row.id } });
};

const handleSet = (row) => {
  id.value = row.id;
  setDialog.value = true;
};

const handleDelete = (row) => {
  proxy.$confirm("您确定要删除该知识库吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delKnowledge(row.id).then(() => {
        proxy.$modal.msgSuccess('删除成功')
        getList();
      });
    })
    .catch(() => {

    });
};
</script>

<style lang="scss" scoped>
._cover {
  width: 70px;
  height: 45px;
}
</style>