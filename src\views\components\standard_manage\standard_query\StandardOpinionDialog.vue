<template>
  <el-dialog
    width="1000px"
    title="意见反馈"
    v-model="visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="h-title">反馈标准</div>
    <el-table :data="tableData" ref="tableRef" :border="true" class="mt20">
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip min-width="200" />
      <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
      <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="150" />
      <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="150" />
      <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
    </el-table>
    <div class="h-title mt40">意见反馈信息</div>
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline mt10">
      <el-form-item label="反馈说明" prop="feedbackContent" class="one-column">
        <el-input
          v-model="form.feedbackContent"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入意见反馈说明信息"
        />
      </el-form-item>
      <el-form-item label="附件" prop="feedbackFileList" class="one-column">
        <div>
          <ele-upload-image
            v-model:value="form.feedbackFileList"
            :fileSize="5"
            :fileType="['jpeg', 'jpg', 'png', 'bmp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']"
            :multiple="true"
            :responseFn="handleResponse"
            @success="handleUpload('feedbackFileList')"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：jpeg、jpg、png、bmp、word、excel、pdf；单个文件大小不超过5MB
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addOpinion } from '@/api/standard_manage/standard_query';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  });
  const { visible, tableData } = toRefs(props);

  const data = reactive({
    form: {},
    rules: {
      feedbackContent: [{ required: true, message: '请输入意见反馈说明信息', trigger: 'blur' }],
    },
    loading: false,
  });
  const { form, rules, loading } = toRefs(data);

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = type => {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        form.value.standardIdList = [tableData.value[0].standardId];
        form.value.standardCodeList = [tableData.value[0].standardCode];
        addOpinion(form.value)
          .then(res => {
            emit('toHint', '反馈成功！');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'toHint']);
</script>

<style lang="scss" scoped></style>
