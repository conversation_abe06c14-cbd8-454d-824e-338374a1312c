<template>
<el-dialog :append-to-body="true" v-model="open" width="40%" :title="title" :close-on-click-modal="false" :close-on-press-escape="false"  @close="close" class="pop-container">
  <div class="tip-box">
    <el-icon color="#0272FF" size="18"><InfoFilled /></el-icon>
    <span class="ml10">确认完成标准【{{standardItem.standardCode}}】的解析数据审核？</span>
  </div>
  <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-position="top" @submit.prevent class="mt-10">
    <el-form-item label="审核结果" prop="analysisAuditResult">
      <el-button :type="form.analysisAuditResult == 0 ? 'primary' : 'default'"  size="large" @click="changeResult('0')" >通过</el-button>
      <el-button :type="form.analysisAuditResult == 1 ? 'primary' : 'default'" size="large" @click="changeResult('1')" >不通过</el-button>
    </el-form-item>
    <template v-if="form.analysisAuditResult == '1'">
      <div class="flex">
        <el-icon color="#0272FF" size="18"><InfoFilled /></el-icon>
        <span class="ml10 c-33 f-14">审核结果不通过，数据将进行二次审核</span>
      </div>
      <el-form-item class="mt10" label="不通过说明" prop="analysisAuditDescription">
        <el-input
          v-model="form.analysisAuditDescription"
          :rows="5"
          :show-word-limit="true"
          maxlength="300"
          type="textarea"
          placeholder="请输入审核不通过说明信息"
        />
      </el-form-item>
    </template>
  </el-form>

  <template #footer>
    <div class="dialog-footer">
      <el-button @click="close">关闭</el-button>
      <el-button @click.prevent="finish" type="primary" :loading="finishLoading">
        <span v-if="!finishLoading">确认完成</span>
        <span v-else>确认完成中...</span>
      </el-button>
    </div>
  </template>
</el-dialog>
</template>
<script setup>
import { auditCompleted } from "@/api/business_manage/file_parsing";

const { proxy } = getCurrentInstance()

const props = defineProps({
  open: Boolean,
  standardItem: {
    type: Object,
    default: {}
  }
});
const { open,standardItem } = toRefs(props)

const title = ref('数据审核结果')
const finishLoading = ref(false)
const form = ref({
  standardId: undefined,
  analysisAuditResult: undefined,
  analysisAuditDescription: undefined
})
const rules = ref({
  analysisAuditResult: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
  analysisAuditDescription: [{ required: true, message: '请输入审核不通过说明信息', trigger: 'blur' }],
});
onMounted(()=>{
  form.value.standardId = standardItem.value.standardId
})

const emit = defineEmits(['update:open','success'])

const close = () => {
  emit('update:open',false)
}
const changeResult = (val) => {
  form.value.analysisAuditResult = val
}
const finish = () => {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      finishLoading.value = true
      auditCompleted(form.value).then(response => {
        finishLoading.value = false;
        emit('update:open',false);
        emit('success');
        proxy.$modal.msgSuccess("数据审核完成");
      }).catch(error => {
        finishLoading.value = false;
      });
    }
  });
}

</script>
<style lang="scss" scoped>
.tip-box {
  padding: 15px 20px;
  background: #EFF6FF;
  border-radius: 5px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
:deep(.el-form-item){
  margin-right: 0px !important;
}
// :deep(.el-form-item__label){
//   font-size: 14px !important;
// }
</style>