<template>
  <el-drawer title="详情" size="80%" append-to-body lock-scroll v-model="props.visible" @close="handleClose">
    <el-tabs v-model="activeName">
      <el-tab-pane label="基础信息" name="info">
        <detail-info :form="form" />
      </el-tab-pane>
      <el-tab-pane label="团队成员" name="person" :lazy="true">
        <detail-person :id="props.id" />
      </el-tab-pane>
      <el-tab-pane v-if="form.archiveStatus == 1" label="标准文本" name="pdf" :lazy="true">
        <iframe :src="previewUrl" style="width: 100%; height: 100vh; border: none; margin-top: 20px"></iframe>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup>
  import { detailStandardRedact } from '@/api/application_tool/standard_redact';
  import { base64Encode } from '@/utils/base64';
  import DetailInfo from '@/views/components/application_tool/standard_redact/DetailInfo.vue';
  import DetailPerson from '@/views/components/application_tool/standard_redact/DetailPerson.vue';

  const emit = defineEmits(['update:visible', 'updateData']);
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: [Number, String],
    },
  });

  const activeName = ref('info');
  const form = ref({});

  onMounted(() => {
    if (props.id) {
      detailStandardRedact(props.id).then(res => {
        form.value = res.data || {};
      });
    }
  });

  const previewUrl = computed(() => {
    return `${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(
      base64Encode(form.value?.pdfFileUrl || '')
    )}`;
  });

  const handleClose = () => {
    emit('updateData');
    emit('update:visible', false);
  };
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__header) {
    margin: 0 !important;
  }
</style>
