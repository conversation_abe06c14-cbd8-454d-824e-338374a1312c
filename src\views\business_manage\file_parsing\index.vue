<template>
  <div class="app-container">
    <div class="app-container-search">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="auto" @submit.prevent>
        <el-form-item label="名称" prop="name">
          <el-input @keyup.enter="getData('pageNum')" v-model="queryParams.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="解析状态" prop="analysisStatus">
          <el-select v-model="queryParams.analysisStatus" clearable placeholder="请选择解析状态" @change="getData('pageNum')">
            <el-option
              v-for="item in bxc_file_analysis_status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button @click="getData('pageNum')" type="primary" icon="Search">查询</el-button>
          <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="app-container-content mt15">
      <div class="flex flex-jc-end flex-ai-center">
        <el-upload
          :headers="upload.headers"
          :action="upload.url"
          :auto-upload="true"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleSuccess"
          :on-error="handleError"
        >
          <el-button icon="UploadFilled" type="primary">上传文件</el-button>
        </el-upload>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" :border="true" class="mt15">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column label="序号" fixed width="80">
          <template #default="{ $index }">
            {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="pageIdx" label="页数" show-overflow-tooltip min-width="100" />
        <el-table-column prop="size" label="大小" show-overflow-tooltip min-width="150" />
        <el-table-column label="解析状态" show-overflow-tooltip min-width="150">
          <template #default="{ row }">
            <span :class="getStatusColor(row.analysisStatus)">
              {{ row.analysisStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" show-overflow-tooltip min-width="180" />
        <el-table-column label="操作" fixed="right" width="220">
          <template #default="{ row }">
            <template v-if="row.analysisStatus == 2">
              <el-button @click="handleReanalysis(row)" type="primary" link>重新解析</el-button>
            </template>
            <template v-else-if="row.analysisStatus == 1">
              <el-button @click="handleDetail(row)" type="primary" link>查看</el-button>
              <el-button @click="handleReanalysis(row)" type="primary" link>重新解析</el-button>
              <!-- <el-button @click="handleIndicator(row)" type="primary" link>指标提取</el-button> -->
            </template>
            <el-button v-else-if="row.analysisStatus == 0" @click="handleAnalysis(row)" type="primary" link>解析</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page-sizes="[10, 30, 50, 100, 200, 300]"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
    <pop-audit-task v-if="openAudit" v-model:open="openAudit" :standardItem="currentRow" fromState="2" @success="getData" />
    <pop-indicator-extraction v-if="open" v-model:open="open" :standardItem="currentRow" />
  </div>
</template>

<script setup>
  import { getOcrList, getAnalysis, getReanalysis } from '@/api/business_manage/file_parsing';
  import PopAuditTask from '@/views/components/business_manage/file_parsing/audit/PopAuditTask';
  import PopIndicatorExtraction from '@/views/components/business_manage/file_parsing/audit/PopIndicatorExtraction';
  import { ElLoading } from 'element-plus';
  import { getToken } from '@/utils/auth';

  const { proxy } = getCurrentInstance();

  const { bxc_file_analysis_status } = proxy.useDict('bxc_file_analysis_status');

  let loadingInstance;
  const fileType = ref(['pdf']);
  const fileSize = ref(1000);
  const upload = ref({
    isUploading: false,
    headers: { Authorization: 'Bearer ' + getToken() },
    url: process.env.VITE_APP_BASE_API + '/ocr/upload',
  });
  const tableLoading = ref(false);
  const openAudit = ref(false);
  const open = ref(false);
  const currentRow = ref({});
  const queryParams = ref({
    pageNum: 1,
    pageSize: 100,
  });
  const tableData = ref([]);
  const total = ref(0);

  const getStatusColor = data => {
    switch (Number(data)) {
      case 0:
        return 'status-red';
        break;
      case 1:
        return 'status-green';
        break;
      case 2:
        return 'status-gray';
        break;
      case 3:
        return 'status-blue';
        break;
      default:
        break;
    }
  };

  const getData = val => {
    // tableLoading.value = true;
    if (val) queryParams.value.pageNum = 1;
    getOcrList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        // tableLoading.value = false;
      });
  };

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    getData('pageNum');
  };

  const handleBeforeUpload = file => {
    let isValid = false;
    if (fileType.value.length) {
      let fileExtension = '';
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
      }
      isValid = fileType.value.some(type => {
        if (file.type.indexOf(type) > -1) return true;
        if (fileExtension && fileExtension.indexOf(type) > -1) return true;
        return false;
      });
    } else {
      isValid = file.type.indexOf('image') > -1;
    }
    if (!isValid) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
      return false;
    }
    if (fileSize.value) {
      const isLt = file.size / 1024 / 1024 < fileSize.value;
      if (!isLt) {
        proxy.$modal.msgError(`上传文件大小不能超过 ${fileSize.value} MB!`);
        return false;
      }
    }
    return true;
  };

  const handleFileUploadProgress = (event, file, fileList) => {
    upload.value.isUploading = true;
    loadingInstance = ElLoading.service({ text: '正在上传，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  };

  const handleSuccess = (response, file, fileList) => {
    upload.value.isUploading = false;
    loadingInstance.close();
    const { code, data, message } = response;
    if (code == 200) {
      getData('pageNum');
    } else {
      proxy.$modal.msgError(message);
    }
  };

  const handleError = () => {
    upload.value.isUploading = false;
    loadingInstance.close();
  };

  const handleAnalysis = row => {
    proxy
      .$confirm('确认开始解析当前标准文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        loadingInstance = ElLoading.service({ text: '正在解析，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
        getAnalysis({ id: row.id, name: row.name, url: row.path })
          .then(() => {
            getData('pageNum');
          })
          .finally(() => {
            loadingInstance.close();
          })
          .catch(err => {
            loadingInstance.close();
          });
      })
      .catch(() => {});
  };

  const handleDetail = row => {
    currentRow.value = row;
    openAudit.value = true;
  };
  const handleIndicator = row => {
    currentRow.value = row;
    open.value = true;
  };
  const handleReanalysis = row => {
    proxy
      .$confirm('确认重新解析当前标准文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        getReanalysis({ id: row.id, name: row.name, url: row.path })
          .then(() => {
            getData('pageNum');
          })
          .finally(() => {
            loadingInstance.close();
          })
          .catch(err => {
            loadingInstance.close();
          });
      })
      .catch(() => {});
  };

  onMounted(() => {
    getData();

    // setInterval(() => {
    //   getData();
    // }, 3000);
  });
</script>

<style lang="scss" scoped></style>
