<template>
  <div class="app-container">
    <div class="approve-setting-wrap">
      <div class="f-22 c-33 f-bold pl30">工作流控制设置</div>
      <div class="setting-list">
        <div v-for="(item,index) in dataList" :key="index" class="settingt-item mt30">
          <div class="item-left pl30">
            <div class="f-18 c-33 f-bold">{{item.configName}}</div>
            <div class="f-14 c-33 mt10">{{item.remark}}</div>
          </div>
          <div class="item-right pr30">
            <el-switch @change="handleSwitchChange(item)" :loading="loading" size="large" active-text="开启" inactive-text="关闭"	v-model="item.flag" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getApproveSettingList } from '@/api/approve/approve_setting'
import { updateConfig } from '@/api/system/config'

const data = reactive({
  loading: false,
  dataList: [],
})
const {
  loading,
  dataList
} = toRefs(data)

const handleSwitchChange = (item) => {
  data.loading = true
  item.configValue = item.flag ? '1' : '0'
  updateConfig(item).then((response) => {
    data.loading = false
  }).catch(() => {
    item.flag = !item.flag
    item.configValue = item.flag ? '1' : '0'
    data.loading = false
  })
}
const getList = () => {
  data.loading = true;
  getApproveSettingList().then((response) => {
    if(response.data){
      data.dataList = response.data.map(item => {
        if(item.configValue == '1'){
          item.flag = true
        }else{
          item.flag = false
        }
        return item
      })
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}

getList()
</script>

<style lang="scss" scoped>
.approve-setting-wrap {
  background: #FFFFFF;
  border-radius: 15px;
  height: calc(100vh - 130px);
  padding: 30px 0px;
  .setting-list {
    .settingt-item {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #E8E8E8;
      padding-bottom: 20px;
      .item-right{
        // margin-top: 20px;
        margin-left: 10px;
        display: flex;
        align-items: flex-end;
        flex-shrink: 0;
      }
      :deep(.el-switch) {
        align-items: flex-end !important;
      }
    }
  }
}
</style>