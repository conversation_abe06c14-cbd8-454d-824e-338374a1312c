.app-container-search {
  padding: 20px 30px;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 5px;

  .el-form-item {
    width: 26% !important;
    min-width: 300px;
    overflow: hidden !important;
    margin-right: 80px !important;
    &:nth-child(n + 4) {
      margin-top: 12px !important;
    }
  }
  .half-form-item {
    width: 39% !important;
    min-width: 370px; 
    overflow: hidden !important;
    margin-right: 80px !important;
    &:nth-child(n + 3) {
      margin-top: 12px !important;
    }
  }

  .el-form-item__label {
    font-size: 14px !important;
    font-weight: 400 !important;
  }

  .one-column {
    width: 100% !important;
    margin-top: 12px;
  }

  .el-input,
  .el-select,
  .el-cascader {
    width: 100%;
  }

  .el-date-editor.el-input {
    width: 100% !important;
  }

  .el-form-item--default {
    margin-bottom: 0 !important;
  }
}

.app-container-content {
  padding: 20px 30px;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 5px;
}
.container-bar {
  display: flex;
  justify-content: space-between;
  .bar-left {
    flex: 1;
  }
  .bar-right {
    display: flex;
    justify-content: flex-end;
  }
}

.dialog-form-inline {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 0 6%;
  .el-form-item {
    width: 47% !important;
    display: flex;
    flex: 0 0 47% !important;
    margin-right: 0px !important;
    .el-input,
    .el-select,
    .el-cascader {
      width: 100%;
    }
  }
  .half-column {
    flex: 0 0 47% !important;
    margin-right: 0px !important;
  }
  .one-column {
    flex: 0 0 100% !important;
  }
}

.app-select {
  font-size: 14px;
  width: 100%;
  height: 39.99px;
  line-height: 39.99px;
  padding: 1px 15px;
  display: flex;
  align-items: center;
  background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  transition: var(--el-transition-box-shadow);
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  ._placeholder {
    color: var(--el-text-color-placeholder);
  }
}

.el-button--large {
  padding: 12px 22px !important;
}
// 鼠标点击后移开，恢复本身样式
.el-button:focus:not(.el-button:hover) {
  border-color: var(--el-button-border-color);
  background-color: var(--el-button-bg-color);
  color: var(--el-button-text-color);
}
.el-form-item.is-error .app-select {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.el-form-item {
  color: #3e4967 !important;
  margin-right: 18px !important;
}

.el-form-item__label {
  font-size: 14px !important;
  color: #333333 !important;
  height: 36px !important;
  line-height: 36px !important;
  margin-bottom: 0 !important;
}

.el-table .cell {
  font-size: 14px !important;
  color: #333333 !important;
  text-align: center !important;
  font-weight: 400 !important;
  line-height: normal !important;

  .el-form-item.is-error {
    margin-bottom: 18px !important;
  }
}

.el-table .cell.el-tooltip {
  font-size: 14px !important;
  color: #3e4967 !important;
}

.el-table thead .el-table__cell {
  background-color: #f3f6fd !important;
}

.el-table .el-table__row--striped .el-table__cell {
  background-color: #f3f6fd !important;
}

.el-table tbody tr:hover > td {
  background-color: #f3f6fd !important;
}

// 抽屉
.maintain-wrap {
  .maintain-type {
    font-size: 14px;
    color: #8e9ab3;
  }
  .maintain-title-wrap {
    display: flex;
    overflow: hidden;
    .title-left {
      flex: 1;
      white-space: nowrap;
      overflow: hidden; //文本超出隐藏
      text-overflow: ellipsis; //文本超出省略号替代
      margin-right: auto;
      display: flex;
      align-items: center;
      .title {
        font-size: 22px;
        font-weight: bold;
        color: #3e4967;
      }
      .type {
        margin-left: 10px;
        width: 42px;
        height: 20px;
        text-align: center;
        border-radius: 3px;
        font-size: 14px;
        color: #fff;
      }
      .w58 {
        width: 58px !important;
      }
      .type-company {
        background: #00ba1f;
      }
      .type-person {
        background: #ffc000;
      }
      .type-0 {
        // 未归档
        background: #8f9bb3;
      }
      .type-1 {
        // 已归档
        background: #00ba1f;
      }
      .type-2 {
        // 已作废
        background: #ec0000;
      }
    }
    .title-right {
      margin-left: 30px;
    }
  }
  .maintain-btn-wrap {
    margin-top: 15px;
    span {
      margin-right: 8px;
      background: #e5e8ef;
      border-radius: 5px;
      font-size: 14px;
      color: #3e4967;
      padding: 5px 10px;
      cursor: pointer;
      &:hover {
        background: #dddddd;
      }
    }
  }
  .maintain-info-wrap {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    background: #f3f6fd;
    border-radius: 5px;
    .maintain-info-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .t {
        font-size: 14px;
        color: #8f9bb3;
      }
      .d {
        margin-top: 5px;
        font-size: 14px;
        color: #3e4967;
      }
    }
  }
  .maintain-content-wrap {
    margin-top: 15px;
    display: flex;
    .maintain-tabs {
      flex: 1;
    }
    .maintain-summary-wrap {
      margin-left: 30px;
      width: 280px;
      .title {
        height: 44px;
        line-height: 44px;
        background: #2f5aff;
        border-radius: 5px 5px 0px 0px;
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
        padding-left: 30px;
        position: relative;
        &::before {
          content: '';
          width: 3px;
          height: 16px;
          background: #ffffff;
          border-radius: 2px;
          margin-left: 8px;
          position: absolute;
          top: 14px;
          left: 10px;
        }
      }
      ul,
      li {
        margin: 0;
        padding: 0;
        list-style: none;
      }
      .summary-list {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        min-height: 280px;
        border: 1px solid #e5e8ef;
        border-radius: 5px;
        .summary-item {
          display: flex;
          .t {
            margin-left: 30px;
            flex: 2;
          }
          .d {
            flex: 3;
          }
        }
      }
    }
  }
}

.workbench-tabs {
  :deep(.el-tabs__item) {
    font-size: 16px !important;
  }
  :deep(.el-tabs__item.is-active) {
    font-size: 16px !important;
    font-weight: bold;
  }
  :deep(.el-tabs__nav-wrap::after) {
    position: static !important;
  }
}

.dialog-footer{
  padding-top: 10px !important;
}

.el-descriptions__cell {
  border-color: #e3e3e3 !important;
}

.el-descriptions__label {
  width: 150px !important;
  min-width: 130px !important;
  font-size: 14px !important;
  color: #333333 !important;
  font-weight: 400 !important;
  background-color: #f3f6fd !important;
}

.el-descriptions__content {
  // width: 325px !important;
  min-width: 225px !important;
  word-break: break-all !important;
}

.s-red {
  color: #FF0000 !important;
  background: #FFE4E4 !important;
  border-color: #FFE4E4 !important;
}
.s-blue {
  color: #2F5AFF !important;
  background: #F2F5FF !important;
  border-color: #F2F5FF !important;
}
.s-green {
  color: #04CA29 !important;
  background: #DBFDDD !important;
  border-color: #DBFDDD !important;
}
.scroller-bar-style {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
.normal-emtpy {
  text-align: center;
  color: #999999;
  font-size: 16px;
  height: 86px;
  line-height: 86px;
}
.scroll-style{
  &::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 8px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  
  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 8px;
    box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
    -webkit-box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
    background: #D5D5D5;
  }
  
  &::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
    -webkit-box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
    border-radius: 8px;
    background: #eeeeee;
  }
}
.zp-popover{
  padding: 0px !important;
}