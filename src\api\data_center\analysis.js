import request from '@/utils/request'

// 数据分析

// 查询标准使用统计图表数据
export const getUseStatChart = (type) => {
  return request({
    url: '/process/common/standard/query/statistics/use/' + type,
    method: 'get'
  })
}
// 查询标准使用统计分页列表
export const getUseStatList = (params) => {
  return request({
    url: '/process/common/standard/query/statistics/use/page',
    method: 'get',
    params
  })
}
// 查询标准数据统计图表
export const getLibraryStatChart = () => {
  return request({
    url: '/process/common/standard/query/statistics/data',
    method: 'get'
  })
}
// 查询统计数据报表列表
export const getLibraryStatList = (params) => {
  return request({
    url: '/process/common/standard/query/statistics/data/list',
    method: 'get',
    params
  })
}
