<template>
  <div>
    <el-dialog
      v-model="open"
      :title="title"
      :before-close="close"
      width="40%"
    >
      <el-form
        :model="form"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5"
      >
        <el-form-item label="模型标识" prop="modelKey">
          <el-input
            size="large"
            maxlength="50"
            v-model="form.modelKey"
            placeholder="请输入模型标识"
            disabled
          />
        </el-form-item>
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            size="large"
            maxlength="50"
            v-model="form.modelName"
            placeholder="请输入模型名称"
            disabled
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            :rows="5"
            :show-word-limit="true"
            maxlength="500"
            type="textarea"
            placeholder="请输入描述说明信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getProcessModelDetail,updateProcess } from "@/api/approve/process_setting";

const { proxy } = getCurrentInstance();
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  modelId: {
    type: String,
    default: null,
  }
});
const { open, modelId } = toRefs(props);

const data = reactive({
  loading: false,
  title: '修改流程模型',
  form: {
    modelId: props.modelId,
    modelKey: undefined,
    modelName: undefined,
    description: undefined
  },
  
  rules: {

  },
});
const { loading, title, form, rules } = toRefs(data);

const handleConfirm = () => {
  data.loading = true;
  if(data.form.modelId){
    updateProcess(data.form)
    .then((res) => {
      proxy.$modal.msgSuccess('修改成功')
      close();
    })
    .finally(() => {
      data.loading = false;
    });
  }
};

const close = () => {
  emit("success");
  emit("update:open", false);
};

const emit = defineEmits(["update:open", "success"]);

const getData = () => {
  data.loading = true;

  getProcessModelDetail(props.modelId).then(response => {
    if (response.data) {
      data.form = response.data;
    }
    data.loading = false;
  }).catch(() => {
    data.loading = false;
  });
}

getData()
</script>

<style lang="scss" scoped>
</style>