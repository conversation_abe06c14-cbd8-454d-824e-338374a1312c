<template>
  <div>
    <div
      v-if="props.activeTab == props.activeStep && !props.isDetail && isAuth(projectDetail.authorizedUserIdList)"
      class="flex flex-ai-center"
    >
      <el-button @click="handleClick('finish')" type="primary">
        <i class="iconfont icon-qidong f-14 mr6"></i>
        完成
      </el-button>
      <el-button v-if="!projectDetail.pushDate" @click="handleClick('publish')">
        <i class="iconfont icon-fabu f-14 mr6"></i>
        发布入库
      </el-button>
      <template v-if="projectDetail.pushDate && !props.isDetail">
        <el-icon class="c-99 ml10" size="18"><InfoFilled /></el-icon>
        <span class="ml5 c-99">制修订标准于{{ projectDetail.pushDate }}发布入库</span>
      </template>
    </div>
    <template v-if="props.isDetail && props.activeStep == 7">
      <div class="h-title">完成发布信息</div>
      <el-descriptions :column="3" class="mt20">
        <el-descriptions-item label="入库操作人：">{{ projectDetail.pushName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="入库时间：">{{ projectDetail.pushDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="完成操作人：">{{ projectDetail.updateName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="完成时间：">{{ projectDetail.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </template>
    <el-tabs v-model="fileTab" class="mt15">
      <el-tab-pane label="记录文件" name="record"></el-tab-pane>
      <el-tab-pane :disabled="true">
        <template #label>
          <ele-upload-image
            v-if="props.activeTab == props.activeStep && !props.isDetail"
            :fileSize="10"
            :multiple="true"
            :customization="true"
            :responseFn="handleResponse"
            :data="{ relationId: props.id, moduleType: props.activeStep, moduleFileType: 1 }"
            :uploadUrl="'/process/amendProjectApproval/upload'"
            :fileType="['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
            class="ml12"
          >
            <el-button>
              <span class="iconfont icon-shangchuan f-14 mr6"></span>
              上传记录文件
            </el-button>
          </ele-upload-image>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-table v-loading="loading" :data="tableData" :border="true">
      <el-table-column label="序号" width="60" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="updateFileName" label="文件名称" show-overflow-tooltip min-width="200" fixed />
      <el-table-column prop="createTime" label="上传时间" show-overflow-tooltip min-width="180" />
      <el-table-column prop="createName" label="上传人" show-overflow-tooltip min-width="150" />
      <el-table-column label="操作" :min-width="120" fixed="right">
        <template #default="{ row }">
          <el-button @click.stop="handleTableBtn(row, 'look')" link type="primary">查看</el-button>
          <el-button @click.stop="handleTableBtn(row, 'down')" link type="primary">下载</el-button>
          <el-button
            v-if="props.activeTab == props.activeStep && !props.isDetail"
            @click.stop="handleTableBtn(row, 'del')"
            type="danger"
            link
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
    <standard-publish-dialog
      v-if="standardPublishVisible"
      v-model:visible="standardPublishVisible"
      :defaultForm="projectDetail"
      @updateData="updateData"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
  </div>
</template>

<script setup>
  import { isAuth } from '@/utils/index';
  import { getRevisionFileList, openPhase, delRevisionFile, putAmendProjectApproval } from '@/api/standard_revision/manage';
  import StandardPublishDialog from '@/views/components/standard_revision/StandardPublishDialog.vue';
  import EleUploadImage from '@/components/EleUploadImage';
  import PreviewFile from '@/components/PreviewFile';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    activeTab: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const loading = ref(true);
  const fileTab = ref('record');
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    moduleType: props.activeTab,
    relationId: props.id,
    moduleFileType: 1,
  });
  const total = ref(0);
  const tableData = ref([]);
  const openFile = ref(false);
  const currentUrl = ref('');
  const projectDetail = ref({});
  const standardPublishVisible = ref(false);

  const getData = () => {
    loading.value = true;
    getRevisionFileList(queryParams.value)
      .then(res => {
        projectDetail.value = res.bean;
        if (projectDetail.value.status) emit('update:activeStep', Number(projectDetail.value.status));
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClick = type => {
    switch (type) {
      case 'finish':
        let title = projectDetail.value.pushDate
          ? '确认项目【' + projectDetail.value.projectName + '】的所有制修订工作已结束，并将项目设置为已完成？'
          : '制修订项目【' + projectDetail.value.projectName + '】尚未进行发布入库，完成后将不可再对项目进行操作，确认完成？';
        proxy
          .$confirm(title, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            putAmendProjectApproval(props.id)
              .then(res => {
                proxy.$modal.msgSuccess('设置成功！');
              })
              .finally(() => {
                getData();
              });
          })
          .catch(() => {});
        break;
      case 'publish':
        standardPublishVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleTableBtn = (row, type) => {
    switch (type) {
      case 'look':
        openFile.value = true;
        currentUrl.value = row.url;
        break;
      case 'down':
        proxy.download('/system/oss/download/' + row.ossId, {}, row.originalName);
        break;
      case 'del':
        proxy
          .$confirm('确认删除文件名称为【' + row.updateFileName + '】的文件？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delRevisionFile({ relationId: props.id, ossId: row.ossId })
              .then(res => {
                proxy.$modal.msgSuccess('文件删除成功！');
              })
              .finally(() => {
                getData();
              });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  const updateData = () => {
    proxy.$modal.msgSuccess('发布成功！');
    getData();
  };

  const handleResponse = () => {
    getData();
  };

  const emit = defineEmits(['update:activeStep']);

  getData();

  defineExpose({
    getData,
  });
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__nav) {
    width: 100%;
    position: relative;
  }

  :deep(.el-tabs__item:last-child) {
    padding: 0 !important;
    position: absolute !important;
    right: 0 !important;
  }

  :deep(.el-tabs__header) {
    margin-bottom: 25px !important;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__cell) {
    width: calc(100% / 3) !important;
  }
</style>
