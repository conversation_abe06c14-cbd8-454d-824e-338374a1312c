<template>
  <el-dialog width="1000px" title="导入体系" append-to-body :lock-scroll="false" v-model="visible" :before-close="handleClose">
    <div class="flex flex-jc-end flex-ai-center">
      <el-button v-if="selectBtnVisible" @click="selectBtnVisible = false" type="primary" class="iconfont icon-piliangxuanze">
        批量选择体系节点
      </el-button>
      <el-form v-else inline ref="queryRef" :model="queryParams">
        <el-form-item label="" prop="systemId">
          <el-tree-select
            v-model="queryParams.systemId"
            :data="options"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="请选择标准导入体系"
            :default-expand-all="true"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="">
          <el-button type="info" @click="handleCancel">取消</el-button>
          <el-button :disabled="!queryParams.systemId" type="primary" @click="handleSelect">确认</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-form ref="queryTableRef" :model="form">
      <el-table :data="form.tableData" ref="tableRef" :border="true" class="mt20">
        <template v-slot:empty>
          <empty />
        </template>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="standardCode" label="标准号" show-overflow-tooltip min-width="200" />
        <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
        <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="150" />
        <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="150" />
        <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
        <el-table-column prop="systemId" label="体系节点" show-overflow-tooltip min-width="150">
          <template #default="{ row, $index }">
            <el-form-item label="" :prop="'tableData.' + $index + '.systemId'" :rules="rules.systemId">
              <el-tree-select
                v-model="row.systemId"
                :data="options"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                placeholder="请选择体系节点"
                :default-expand-all="true"
                check-strictly
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="70">
          <template #default="{ $index }">
            <el-button v-if="form.tableData.length > 1" @click.stop="handleDel($index)" icon="Delete" size="small" link type="danger" class="f-14">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" :disabled="!disabled" type="primary" @click="handleConfirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getStandardSystemTree } from '@/api/standard_manage/standard_system';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => {
        return {
          tableData: [],
        };
      },
    },
  });
  const { visible, form } = toRefs(props);

  let disabled = computed(() => {
    return form.value.tableData.length > 0;
  });

  const data = reactive({
    loading: false,
    selectBtnVisible: true,
    queryParams: {},
    options: [],
    rules: {
      systemId: [{ required: true, message: '', trigger: 'change' }],
    },
  });
  const { loading, selectBtnVisible, queryParams, options, rules } = toRefs(data);

  const getSelect = () => {
    getStandardSystemTree().then(res => {
      options.value = res.data;
    });
  };

  const handleSelect = () => {
    form.value.tableData.forEach(item => {
      item.systemId = queryParams.value.systemId;
    });
  };

  const handleDel = index => {
    form.value.tableData.splice(index, 1);
  };

  const handleCancel = () => {
    proxy.$refs.queryRef.resetFields();
    selectBtnVisible.value = true;
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryTableRef.validate(valid => {
      if (valid) {
        let arr = form.value.tableData.map(item => {
          return { systemId: item.systemId, standardIds: [item.standardId] };
        });
        emit('addSystem', arr);
        handleClose();
        loading.value = false;
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'addSystem']);

  getSelect();
</script>

<style lang="scss" scoped>
  .w400 {
    width: 400px;
  }

  :deep(.el-form-item--default) {
    margin-bottom: 0 !important;
  }

  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 0 !important;

    &:last-child {
      margin-right: 0 !important;
    }
  }

  .icon-piliangxuanze:before {
    margin-right: 6px;
  }
</style>
