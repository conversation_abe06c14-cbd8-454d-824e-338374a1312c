<template>
  <div>
    <el-button
      v-if="props.activeTab == props.activeStep && isAuth(projectDetail.authorizedUserIdList) && !props.isDetail"
      @click="handleStart"
      type="primary"
    >
      <i class="iconfont icon-qidong f-14 mr6"></i>
      启动下一阶段【意见征求】
    </el-button>
    <el-tabs @tab-click="handleFileTab" v-model="fileTab" class="mt15">
      <el-tab-pane label="草案稿" name="draft"></el-tab-pane>
      <el-tab-pane label="记录文件" name="record"></el-tab-pane>
      <el-tab-pane :disabled="true">
        <template #label>
          <ele-upload-image
            v-if="props.activeTab == props.activeStep && !props.isDetail"
            :fileSize="10"
            :multiple="true"
            :customization="true"
            :responseFn="getData"
            :data="{ relationId: props.id, moduleType: props.activeStep, moduleFileType: fileTab == 'draft' ? 0 : 1 }"
            :uploadUrl="'/process/amendProjectApproval/upload'"
            :fileType="['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
          >
            <el-button>
              <span class="iconfont icon-shangchuan f-14 mr6"></span>
              {{ fileTab == 'draft' ? '上传草案稿' : '上传记录文件' }}
            </el-button>
          </ele-upload-image>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-table v-loading="loading" :data="tableData" :border="true">
      <el-table-column label="序号" width="60" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="updateFileName" label="文件名称" show-overflow-tooltip min-width="200" fixed />
      <el-table-column prop="createTime" label="上传时间" show-overflow-tooltip min-width="180" />
      <el-table-column prop="createName" label="上传人" show-overflow-tooltip min-width="150" />
      <el-table-column label="操作" :min-width="120" fixed="right">
        <template #default="{ row }">
          <el-button @click.stop="handleTableBtn(row, 'look')" type="primary" link>查看</el-button>
          <el-button @click.stop="handleTableBtn(row, 'down')" type="primary" link>下载</el-button>
          <el-button
            v-if="props.activeTab == props.activeStep && !props.isDetail"
            @click.stop="handleTableBtn(row, 'del')"
            type="danger"
            link
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
  </div>
</template>

<script setup>
  import { isAuth } from '@/utils/index';
  import { getRevisionFileList, delRevisionFile, startNextPhase } from '@/api/standard_revision/manage';
  import EleUploadImage from '@/components/EleUploadImage';
  import PreviewFile from '@/components/PreviewFile';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    activeStep: {
      type: Number,
    },
    activeTab: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
  });

  const fileTab = ref('draft');
  const loading = ref(true);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    moduleType: props.activeTab,
    relationId: props.id,
  });
  const total = ref(0);
  const tableData = ref([]);
  const openFile = ref(false);
  const currentUrl = ref('');
  const projectDetail = ref({});

  const getData = () => {
    loading.value = true;
    queryParams.value.moduleFileType = fileTab.value == 'draft' ? 0 : 1;
    getRevisionFileList(queryParams.value)
      .then(res => {
        projectDetail.value = res.bean;
        if (projectDetail.value.status) emit('update:activeStep', Number(projectDetail.value.status));
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleFileTab = tab => {
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 10;
    proxy.$nextTick(() => {
      getData();
    });
  };

  const handleStart = () => {
    proxy
      .$confirm('确认将项目【' + projectDetail.value.projectName + '】的制修定阶段设置为意见征求？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        startNextPhase(props.id).then(res => {
          proxy.$modal.msgSuccess('设置成功！');
          emit('update:activeStep', 3);
          emit('update:activeTab', 3);
        });
      })
      .catch(() => {});
  };

  const handleTableBtn = (row, type) => {
    switch (type) {
      case 'look':
        openFile.value = true;
        currentUrl.value = row.url;
        break;
      case 'down':
        proxy.download('/system/oss/download/' + row.ossId, {}, row.updateFileName);
        break;
      case 'del':
        proxy
          .$confirm('确认删除文件名称为【' + row.updateFileName + '】的文件？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delRevisionFile({ relationId: props.id, ossId: row.ossId }).then(res => {
              proxy.$modal.msgSuccess('文件删除成功！');
              getData();
            });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  getData();

  const emit = defineEmits(['update:activeStep', 'update:activeTab']);
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__nav) {
    width: 100%;
    position: relative;
  }

  :deep(.el-tabs__item:last-child) {
    padding: 0 !important;
    position: absolute !important;
    right: 0 !important;
  }

  :deep(.el-tabs__header) {
    margin-bottom: 25px !important;
  }
</style>
