<template>
  <div>
    <el-table v-loading="loading" :data="tableData" :border="true">
      <el-table-column label="序号" width="60" fixed>
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="updateFileName" label="文件名称" show-overflow-tooltip min-width="200" fixed />
      <el-table-column prop="createTime" label="上传时间" show-overflow-tooltip min-width="180" />
      <el-table-column prop="createName" label="上传人" show-overflow-tooltip min-width="150" />
      <el-table-column label="操作" :min-width="120" fixed="right">
        <template #default="{ row }">
          <el-button @click="handleClick(row, 'look')" link size="small" class="f-14 c-primary">查看</el-button>
          <el-button @click="handleClick(row, 'down')" link size="small" class="f-14 c-primary">下载</el-button>
          <el-button v-if="props.activeStep == props.activeTab && !props.isDetail" @click="handleClick(row, 'del')" type="danger" link>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getData"
    />
    <preview-file v-if="openFile" v-model:open="openFile" :url="currentUrl" />
  </div>
</template>

<script setup>
  import { delRevisionFile, getRevisionFileList } from '@/api/standard_revision/manage';
  import PreviewFile from '@/components/PreviewFile';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    id: {
      type: [Number, String],
    },
    isDetail: {
      type: Boolean,
      default: false,
    },
    activeTab: {
      type: [Number, String],
    },
    activeStep: {
      type: [Number, String],
    },
  });

  const loading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    moduleType: props.activeTab,
    relationId: props.id,
    moduleFileType: 1,
  });
  const total = ref(0);
  const tableData = ref([]);
  const openFile = ref(false);
  const currentUrl = ref('');

  const getData = () => {
    loading.value = true;
    getRevisionFileList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleClick = (row, type) => {
    switch (type) {
      case 'look':
        openFile.value = true;
        currentUrl.value = row.url;
        break;
      case 'down':
        proxy.download('/system/oss/download/' + row.ossId, {}, row.originalName);
        break;
      case 'del':
        proxy
          .$confirm('确认删除文件名称为【' + row.updateFileName + '】的文件？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            delRevisionFile({ relationId: props.id, ossId: row.ossId })
              .then(res => {
                proxy.$modal.msgSuccess('文件删除成功！');
              })
              .finally(() => {
                getData();
              });
          })
          .catch(() => {});
        break;
      default:
        break;
    }
  };

  getData();

  defineExpose({
    getData,
  });
</script>

<style lang="scss" scoped></style>
