<template>
  <div class="app-container flex">
    <div class="left-wrap mr20">
      <div class="fw-b f-22 c-33">
        类型目录
      </div>
      <div class="notice-type-wrap fw-b c-33 scroller-bar-style">
        <div class="item flex flex-ai-center" :class="active == item.id? 'active':''" v-for="item in typeDataList" :key="item.id">
          <img src="@/assets/images/notice_rule/file-icon.png" alt="">
          <div @click="chooseType(item)" class="ml-13 pointer flex-1 mr10 w-all">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="right-wrap flex">
      <div class="search-wrap app-container-search mb20">
        <el-form label-width="auto" :model="params" ref="formRef" :inline="true" class="mb20">
          <el-form-item class="half-form-item" label="标题:" prop="title">
            <el-input @keyup.enter="handleQuery" v-model="params.title" placeholder="请输入标题" />
          </el-form-item>
          <el-form-item label="发布日期:" class="half-form-item">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDate"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <div class="search-bar-wrapper">
          <el-button
            type="primary"
            icon="Search"
            @click="handleQuery"
            >查询</el-button
          >
          <el-button
            plain
            icon="Refresh"
            @click="resetQuery"
            >重置</el-button
          >
        </div>
      </div>
      <div class="list-wrap">
        <el-table
        :data="dataList"
        :border="true"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column label="序号" fixed width="60">
            <template #default="{ $index }">
              {{ (params.pageNum - 1) * params.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="title" label="标题" show-overflow-tooltip min-width="200" />
          <el-table-column prop="pname" label="类型" show-overflow-tooltip min-width="150" />
          <el-table-column prop="publishDate" label="发布时间" show-overflow-tooltip min-width="180" />
          <el-table-column prop="pv" label="阅读总量" show-overflow-tooltip min-width="120" />
          <el-table-column prop="collectTotal" label="收藏总量" show-overflow-tooltip min-width="120" />
          <el-table-column label="操作" min-width="150">
            <template #default="scope">
              <el-button
                @click.stop="handleDetail(scope.row)"
                link
                size="small"
                class="f-14 c-primary"
                v-hasPermi="['notice:notice:detail']"
              >
                查看
              </el-button>
              <el-button
                v-if="scope.row.isCollect == 0"
                @click.stop="handleCollect(scope.row)"
                link
                size="small"
                type="danger"
                class="f-14 c-F20000"
                v-hasPermi="['notice:notice:collect']"
              >
                收藏
              </el-button>
              <el-button
                v-if="scope.row.isCollect == 1"
                @click.stop="handleNoCollect(scope.row)"
                link
                size="small"
                type="danger"
                class="f-14 c-F20000"
                v-hasPermi="['notice:notice:collectCancel']"
              >
                取消收藏
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="params.pageNum"
          v-model:limit="params.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <notice-detail-dialog
      v-if="detailDialog"
      :id="currentId"
      v-model:dialogVisible="detailDialog"
    />
  </div>
</template>

<script setup>
import NoticeDetailDialog from '@/views/components/notice_rule/NoticeDetailDialog'
import {getNoticeLawsList,userCollectInfoIsCollect} from '@/api/notice_rule/index.js'
import {getNoticeCatalogList} from '@/api/notice_rule/manage.js'


const {proxy} = getCurrentInstance()

const data = reactive({
  detailDialog:false,
  dateRange: [],
  loading: false,
  open: false,
  openAccept: false,
  currentId: undefined,
  total: 0,
  dataList: [],
  typeDataList:[],
  params: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    startTime: undefined,
    endTime: undefined,
    pid:null
  },
  active:null,
  form:{
    recordType:'1'
  }
})

const {dateRange,loading,total,dataList,params,detailDialog,typeDataList,active,currentId} = toRefs(data)

//获取目录列表
const getTypeList = () => {
  getNoticeCatalogList(data.params).then(res => {
    data.typeDataList = res.rows;
    if(data.typeDataList && data.typeDataList.length > 0){
      data.active = data.typeDataList[0].id;
      data.params.pid = data.typeDataList[0].id;
    }
    getList()
  })
}

//选择目录
const chooseType = (item) => {
  data.active = item.id;
  data.params.pid = item.id;
  getList();
}

const resetQuery = () => {
  data.dateRange = [];
  proxy.resetForm("formRef");
  selectDate();
  handleQuery();
}
/** 搜索按钮操作 */
const handleQuery = () => {
  data.params.pageNum = 1;
  getList();
}
const selectDate = () => {
  if (data.dateRange && data.dateRange.length > 0) {
    data.params.startTime = data.dateRange[0];
    data.params.endTime = data.dateRange[1];
  } else {
    data.params.startTime = undefined;
    data.params.endTime = undefined;
  }
}
const getList = () => {
  data.loading = true;
  getNoticeLawsList(data.params)
  .then(res => {
    data.dataList = res.rows;
    data.total = res.total;
    data.loading = false;
  })
  .catch(() => {
    data.loading = false;
  });
}

//查看
const handleDetail = (row) => {
  data.currentId = row.id;
  data.detailDialog = true;
};

//收藏
const handleCollect = (row) => {
  data.form.recordId = row.id;
  data.form.isCollect = '1'
  userCollectInfoIsCollect(data.form).then(res => {
    proxy.$modal.msgSuccess('收藏成功')
    getList()
  }).catch(res => {
    getList()
  })

};
//取消收藏
const handleNoCollect = (row) => {
  data.form.recordId = row.id;
  data.form.isCollect = '0'
  userCollectInfoIsCollect(data.form).then(res => {
    proxy.$modal.msgSuccess('取消收藏成功')
    getList()
  }).catch(res => {
    getList()
  })
};


getTypeList()
</script>

<style lang="scss" scoped>
.app-container{
  box-sizing: border-box;
  .left-wrap{
    box-sizing: border-box;
    flex: 0 0 24%;
    max-width:24%;
    // height: calc(100vh - 130px);
    padding: 30px;
    border-radius: 15px;
    background-color: #fff;
  }
  .right-wrap{
    flex-direction: column;
    flex: 0 0 75%;
    max-width:75%;
    // height: calc(100vh - 130px);
    .search-wrap{
      box-sizing: border-box;
      height: 150px;
      padding: 30px 30px 20px 30px;
      border-radius: 15px;
      background-color: #fff;
    }
    .list-wrap{
      box-sizing: border-box;
      padding: 30px;
      border-radius: 15px;
      background-color: #fff;
    }
  }
}
.active{
  background-color: #E6EBFF;
  color: #2F5AFF;
}
.notice-type-wrap{
  margin-top: 38px;
  height: calc(100vh - 260px);
  overflow-y:auto;
  .item{
    border-radius: 5px;
    padding: 10px;
    img{
      height: 15px;
    }
  }
}
.top-icon{
  height: 16px;
}
.f-22{
  font-size: 22px;
}
.ml-13{
  margin-left: 13px;
}
.w-all{
  word-break: break-all;
}

</style>