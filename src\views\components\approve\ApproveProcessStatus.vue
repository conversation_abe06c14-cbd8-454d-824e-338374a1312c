<template>
  <div v-if="JSON.stringify(info) != '{}'" class="process-status-wrap" :class="`bgc-${info.processStatus}`">
    <div class="process-status-left">
      <img :src="getImg(info.processStatus)" alt="">
    </div>
    <div class="process-status-middle">
      <div class="title">{{info.procDefName}}</div>
      <div v-if="info.processStatus == 'running'" class="status">
        等待 {{info.taskName}} 审批处理
      </div>
      <div v-else class="status">
        {{info.processStatusName}}
      </div>
    </div>
    <div v-if="info.duration" class="process-status-right">
      <el-icon><Clock /></el-icon>
      <span class="ml10">{{info.duration}}</span>
    </div>
  </div>
</template>

<script setup>
import RUNNING from '@/assets/images/approve/running.png'
import TERMINATED from '@/assets/images/approve/terminated.png'
import COMPLETED from '@/assets/images/approve/completed.png'
import CANCELED from '@/assets/images/approve/canceled.png'

const props = defineProps({
  info:{
    type: Object,
    default: {}
  }
})
const {info} = toRefs(props)

const getImg = (status) => {
  switch (status) {
    case 'running':
      return RUNNING
      break;
    case 'terminated':
      return TERMINATED
      break;
    case 'completed':
      return COMPLETED
      break;
    case 'canceled':
      return CANCELED
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped>
.process-status-wrap{
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #FFFFFF;
  border-radius: 5px;
  .process-status-left{
    width: 56px;
    flex-shrink: 0;
    img{
      width: 100%;
    }
  }
  .process-status-middle{
    margin-left: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    .title{
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .status{
      font-size: 14px;
    }
  }
  .process-status-right{
    width: 150px;
    flex-shrink: 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>