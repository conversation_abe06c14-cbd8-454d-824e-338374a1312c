import request from '@/utils/request'

// 流程设置

// 查询流程模型列表
export const getProcessSettingList = (params) => {
  return request({
    url: 'workflow/model/list',
    method: 'get',
    params
  })
}
// 获取流程表单详细信息
export const getProcessSettingDetail = (modelId) => {
  return request({
    url: 'workflow/model/bpmnXml/' + modelId,
    method: 'get'
  })
}
// 部署
export const deployProcess = (modelId) => {
  return request({
    url: 'workflow/model/deploy?modelId=' + modelId,
    method: 'post'
  })
}
// 获取流程模型详细信息
export const getProcessModelDetail = (modelId) => {
  return request({
    url: 'workflow/model/' + modelId,
    method: 'get'
  })
}
// 修改流程模型
export const updateProcess = (data) => {
  return request({
    url: 'workflow/model',
    method: 'put',
    data
  })
}
// 保存
export const saveWorkflow = (data) => {
  return request({
    url: 'workflow/model/save',
    method: 'post',
    data
  })
}
