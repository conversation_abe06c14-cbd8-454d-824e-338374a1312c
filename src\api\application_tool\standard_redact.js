import request from '@/utils/request';

export const getStandardRedactList = params => {
  return request({
    url: '/process/standardWrite/list',
    method: 'get',
    params,
  });
};

export const deleteStandardRedact = data => {
  return request({
    url: '/process/standardWrite/remove',
    method: 'delete',
    data: data,
  });
};

export const putStandardRedact = data => {
  return request({
    url: '/process/standardWrite/archive/' + data,
    method: 'put',
  });
};

export const putStandardWrite = (id, data) => {
  return request({
    url: '/process/standardWrite/storage/' + id,
    method: 'put',
    data: data,
  });
};

export const addStandardRedact = data => {
  return request({
    url: '/process/standardWrite',
    method: 'post',
    data: data,
  });
};

export const editStandardRedact = data => {
  return request({
    url: '/process/standardWrite',
    method: 'put',
    data: data,
  });
};

export const detailStandardRedact = data => {
  return request({
    url: '/process/standardWrite/' + data,
    method: 'get',
  });
};

export const getIndustryCategoryList = () => {
  return request({
    url: '/process/industryCategory/listAll',
    method: 'get',
  });
};

export const getGroupMemberList = () => {
  return request({
    url: '/process/groupMember/listAll',
    method: 'get',
  });
};

export const getTeamInfo = data => {
  return request({
    url: '/process/standardWrite/getTeamInfo/' + data,
    method: 'get',
  });
};

export const addStandardWriteTeam = data => {
  return request({
    url: '/process/standardWriteTeam',
    method: 'post',
    data: data,
  });
};

export const putStandardWriteTeam = data => {
  return request({
    url: '/process/standardWriteTeam',
    method: 'put',
    data: data,
  });
};

export const removeStandardWriteTeam = data => {
  return request({
    url: '/process/standardWriteTeam/remove',
    method: 'delete',
    data: data,
  });
};

export const getStorageStandardInfo = data => {
  return request({
    url: '/process/standardWrite/getStorageStandardInfo/' + data,
    method: 'get',
  });
};

export const standardWrite = data => {
  return request({
    url: '/process/standardWrite/storage',
    method: 'put',
    data: data,
  });
};
