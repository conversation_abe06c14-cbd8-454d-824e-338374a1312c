<template>
  <div class="overview-wrap">
    <div class="f-22 f-bold c-33">标准概览</div>
    <div class="overview-list">
      <div class="overview-item">
        <div class="item-img">
          <img src="@/assets/images/workbench/gb.png" alt="">
        </div>
        <div class="item-left">
          <div class="f-16 c-33">国家标准</div>
          <div class="num">{{info.countryStandard || '0'}}</div>
        </div>
      </div>
      <div class="overview-item">
        <div class="item-img">
          <img src="@/assets/images/workbench/hb.png" alt="">
        </div>
        <div class="item-left">
          <div class="f-16 c-33">行业标准</div>
          <div class="num">{{info.industryStandard || '0'}}</div>
        </div>
      </div>
      <div class="overview-item">
        <div class="item-img">
          <img src="@/assets/images/workbench/tb.png" alt="">
        </div>
        <div class="item-left">
          <div class="f-16 c-33">团体标准</div>
          <div class="num">{{info.associationStandard || '0'}}</div>
        </div>
      </div>
      <div class="overview-item">
        <div class="item-img">
          <img src="@/assets/images/workbench/db.png" alt="">
        </div>
        <div class="item-left">
          <div class="f-16 c-33">地方标准</div>
          <div class="num">{{info.localStandard || '0'}}</div>
        </div>
      </div>
      <div class="overview-item">
        <div class="item-img">
          <img src="@/assets/images/workbench/qb.png" alt="">
        </div>
        <div class="item-left">
          <div class="f-16 c-33">企业标准</div>
          <div class="num">{{info.enterpriseStandard || '0'}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getOverview } from '@/api/workbench'

const { proxy } = getCurrentInstance()
const data = reactive({
  loading: false,
  info: {},
})
const {loading,info} = toRefs(data)

const getData = () => {
  data.loading = true;
  getOverview().then((response) => {
    if(response.data){
      data.info = response.data
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
getData()

</script>

<style lang="scss" scoped>
.overview-wrap{
  padding: 30px;
  background: #FFFFFF;
  border-radius: 15px;
  .overview-list{
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    gap: 0 20px;
    overflow: hidden;
    .overview-item{
      flex: 0 0 18%;
      height: 130px;
      background-image: url("@/assets/images/workbench/bg.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      .item-img{
        flex:2;
        overflow: hidden;
        margin-left: 20px;
      }
      img{
        width: 100%;
        max-height: 102px;
        object-fit: contain;
      }
      .item-left{
        flex: 3;
        flex-shrink: 0;
        height: 100%;
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        .num {
          margin-top: 10px;
          font-size: 34px;
          font-family: DIN Next LT Pro;
          font-weight: bold;
          color: #2F5AFF;
        }
      }
    }
    
  }
}
</style>