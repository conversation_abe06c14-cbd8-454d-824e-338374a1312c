<template>
  <div class="app-container">
    <div class="standard-system-wrap">
      <div class="standard-system-left scroller-bar-style">
        <div class="f-22 f-bold c-33 mb10">标准体系</div>
        <el-input class="mt20" v-model="searchStr" placeholder="请输入搜索内容" suffix-icon="Search" />
        <div class="mt20 flex">
          <el-button v-hasPermi="['standard_manage:standard_system:add']" type="primary" icon="Plus" @click="handleMenuAdd">
            新增
          </el-button>
          <el-button
            v-hasPermi="['standard_manage:standard_system:rename']"
            plain
            icon="Edit"
            @click="handleMenuRename"
            :disabled="!hasCurrentMenu()"
          >
            重命名
          </el-button>
          <el-button
            v-hasPermi="['standard_manage:standard_system:delete']"
            plain
            icon="Delete"
            @click="handleMenuDelete"
            :disabled="!hasCurrentMenu()"
          >
            删除
          </el-button>
        </div>
        <el-tree
          v-loading="menuLoading"
          class="mt40"
          :data="categoryList"
          ref="treeRef"
          node-key="id"
          empty-text="暂无数据"
          :highlight-current="true"
          :props="defaultProps"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          :draggable="isDrag"
          @node-drop="handleDrop"
          :filter-node-method="filterNode"
        >
          <template #default="{ node }">
            <span class="custom-tree-node">
              <span class="custom-label">
                <suffix-icon class="mr5" fileType="0"></suffix-icon>
                <span v-showToolTip="['tree']">
                  <el-tooltip placement="bottom-start" :content="node.label">
                    <span>{{ node.label }}</span>
                  </el-tooltip>
                </span>
              </span>
            </span>
          </template>
        </el-tree>
      </div>
      <div class="standard-system-right scroller-bar-style">
        <div class="app-container-search br-15">
          <el-form :model="queryParams" ref="queryFormRef" label-width="auto" :inline="true">
            <el-form-item class="half-form-item" label="标准号:" prop="standardCode">
              <el-input v-model="queryParams.standardCode" placeholder="请输入标准号" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item class="half-form-item" label="标准名称:" prop="standardName">
              <el-input v-model="queryParams.standardName" placeholder="请输入标准名称" @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item class="half-form-item" label="标准类型:" prop="standardType">
              <el-select v-model="queryParams.standardType" placeholder="请选择标准类型" clearable>
                <el-option v-for="dict in bxc_standard_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="half-form-item" label="标准状态:" prop="standardStatus">
              <el-select v-model="queryParams.standardStatus" placeholder="请选择标准状态" clearable>
                <el-option v-for="dict in bxc_standard_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="half-form-item" label="标准属性:" prop="standardAttr">
              <el-select v-model="queryParams.standardAttr" placeholder="请选择标准属性" clearable>
                <el-option v-for="dict in bxc_standard_attr" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-form>
          <div class="container-bar mt20">
            <div class="bar-left">
              <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
            </div>
          </div>
        </div>
        <div class="app-container-content mt20 br-15">
          <div class="container-bar">
            <div class="bar-right">
              <el-button v-hasPermi="['standard_manage:standard_system:import']" type="primary" @click="handleImport">
                <i class="iconfont icon-daoru mr5"></i>
                导入标准
              </el-button>
              <el-button v-hasPermi="['standard_manage:standard_system:remove']" icon="Delete" @click="handleRemove">
                移出
              </el-button>
            </div>
          </div>
          <el-table
            v-loading="loading"
            ref="tableRef"
            :data="dataList"
            class="mt15"
            :border="true"
            @selection-change="handleSelectionChange"
          >
            <template v-slot:empty>
              <empty />
            </template>
            <el-table-column type="selection" align="center" width="55" />
            <el-table-column type="index" align="center" label="序号" fixed="left" width="60">
              <template #default="scope">
                {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="standardCode" label="标准号" fixed="left" show-overflow-tooltip min-width="200">
              <template #default="scope">
                <span @click.stop="handleStandardCode(scope.row)" class="f-14 c-primary pointer">
                  {{ scope.row.standardCode }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="standardName" label="标准名称" show-overflow-tooltip min-width="200" />
            <el-table-column prop="standardTypeName" label="标准类型" show-overflow-tooltip min-width="120" />
            <el-table-column prop="standardStatusName" label="标准状态" show-overflow-tooltip min-width="120" />
            <el-table-column prop="systemName" label="体系节点名称" show-overflow-tooltip min-width="150" />
            <el-table-column prop="publishDate" label="发布日期" show-overflow-tooltip min-width="180" />
            <el-table-column prop="executeDate" label="实施日期" show-overflow-tooltip min-width="180" />
            <el-table-column prop="standardAttrName" label="标准属性" show-overflow-tooltip min-width="150" />
            <el-table-column prop="amendName" label="制修订" show-overflow-tooltip min-width="120" />
            <!-- <el-table-column prop="repealDate" label="废止日期" show-overflow-tooltip min-width="180" /> -->
            <el-table-column prop="downloadTotal" label="下载总量" show-overflow-tooltip min-width="120" />
            <el-table-column prop="pv" label="查看总量" show-overflow-tooltip min-width="120" />
            <el-table-column prop="updateTime" label="更新日期" show-overflow-tooltip min-width="180" />
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>
    <!-- 导入标准弹框 -->
    <pop-standard-import v-if="open" v-model:open="open" :id="currentMenu.id" @success="getList" />
    <!-- 新增/重命名弹框 -->
    <pop-standard-menu-add
      v-if="openAdd"
      v-model:open="openAdd"
      :menuList="categoryList"
      :id="currentMenu.id"
      :opType="opMenuType"
      @success="getCategoryTree"
    />
    <detail-drawer
      v-if="drawerVisible"
      v-model:visible="drawerVisible"
      :standardId="selectRow.standardId"
      :standardType="selectRow.standardType"
    />
  </div>
</template>

<script setup>
  import PopStandardImport from '@/views/components/standard_manage/PopStandardImport';
  import {
    getStandardSystemTree,
    getStandardSystemList,
    moveStandardSystem,
    deleteStandardSystem,
    removeStandardSystem,
  } from '@/api/standard_manage/standard_system';
  import SuffixIcon from '@/components/SuffixIcon';
  import PopStandardMenuAdd from '@/views/components/standard_manage/PopStandardMenuAdd';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_type, bxc_standard_status, bxc_standard_attr } = proxy.useDict(
    'bxc_standard_type',
    'bxc_standard_status',
    'bxc_standard_attr'
  );

  const selectRow = ref({});
  const drawerVisible = ref(false);

  const data = reactive({
    loading: false,
    menuLoading: false,
    open: false,
    openAdd: false,
    opMenuType: 0, // 0:新增,1:重命名
    isDrag: true,
    currentMenu: {},
    dateRange: [],
    total: 0,
    selectedList: [],
    dataList: [],
    categoryList: [],
    defaultProps: {
      children: 'children',
      label: 'name',
    },
    searchStr: '',
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      systemId: undefined,
      standardCode: undefined,
      standardName: undefined,
      standardType: undefined,
      standardStatus: undefined,
      standardAttr: undefined,
    },
  });
  const {
    loading,
    menuLoading,
    open,
    openAdd,
    isDrag,
    currentMenu,
    opMenuType,
    total,
    dataList,
    categoryList,
    defaultProps,
    searchStr,
    queryParams,
  } = toRefs(data);
  data.isDrag = proxy.$auth.hasPermi('standard_manage:standard_system:drag');

  const resetQuery = () => {
    proxy.resetForm('queryFormRef');
    handleQuery();
  };
  /** 搜索按钮操作 */
  const handleQuery = () => {
    data.queryParams.pageNum = 1;
    getList();
  };
  const getList = () => {
    data.loading = true;
    getStandardSystemList(data.queryParams)
      .then(response => {
        if (response.rows) {
          data.dataList = response.rows;
          data.total = response.total;
        }
        data.loading = false;
      })
      .catch(() => {
        data.loading = false;
      });
  };
  const handleStandardCode = row => {
    selectRow.value = row;
    drawerVisible.value = true;
  };
  const handleRemove = () => {
    if (data.selectedList.length == 0) {
      proxy.$modal.msgError('请选择需要移出的项');
      return;
    }
    let ids = data.selectedList.map(item => {
      return {
        systemId: item.systemId,
        standardId: item.standardId,
      };
    });
    proxy
      .$confirm('是否确认移出所选项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        removeStandardSystem(ids).then(res => {
          proxy.$message({
            message: '移出成功',
            type: 'success',
          });
          getList();
        });
      })
      .catch(() => {});
  };
  const handleSelectionChange = list => {
    data.selectedList = list;
  };
  const handleNodeClick = (item, nodes) => {
    data.currentMenu = item;
    data.queryParams.systemId = item.id;
    proxy.$nextTick(() => {
      proxy.$refs.treeRef.setCurrentKey(item.id);
      getList();
    });
  };
  const handleImport = () => {
    data.open = true;
  };
  const getCategoryTree = () => {
    data.menuLoading = true;
    getStandardSystemTree()
      .then(response => {
        if (response.data) {
          data.categoryList = response.data;
          if (!data.queryParams.systemId && data.categoryList.length > 0) {
            data.queryParams.systemId = data.categoryList[0].id;
            data.currentMenu = data.categoryList[0];
          }
          proxy.$nextTick(() => {
            proxy.$refs.treeRef.setCurrentKey(data.queryParams.systemId);
            getList();
          });
        }

        data.menuLoading = false;
      })
      .catch(() => {
        data.menuLoading = false;
      });
  };
  const handleMenuAdd = () => {
    data.opMenuType = 0; // 0:新增,1:重命名
    data.openAdd = true;
  };
  const hasCurrentMenu = () => {
    return JSON.stringify(data.currentMenu) != '{}';
  };
  const handleMenuRename = () => {
    if (!hasCurrentMenu()) {
      proxy.$modal.msgError('请选择重命名项');
      return;
    }
    data.opMenuType = 1; // 0:新增,1:重命名
    data.openAdd = true;
  };
  const handleMenuDelete = () => {
    if (JSON.stringify(data.currentMenu) == '{}') {
      proxy.$modal.msgError('请选择删除项');
      return;
    }
    proxy
      .$confirm('是否确认要删除该体系目录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        deleteStandardSystem({ ids: [data.currentMenu.id] }).then(res => {
          proxy.$message({
            message: '删除成功',
            type: 'success',
          });
          data.currentMenu = {};
          getCategoryTree();
        });
      })
      .catch(() => {});
  };
  const handleDrop = (draggingNode, dropNode, dropType, ev) => {
    let data1 = dropType != 'inner' ? dropNode.parent.data : dropNode.data;
    let nodeData = dropNode.level == 1 && dropType != 'inner' ? data1 : data1.children;
    // 设置父ID,当level为1说明在第一级，pid为空
    let nextId = undefined;
    let pid = undefined;
    let currentIndex = 0;
    nodeData.forEach((element, index) => {
      element.pid = data1.id || 0;
      if (element.id == draggingNode.data.id) {
        currentIndex = index;
        pid = element.pid;
      }
    });

    let len = nodeData.length;
    if (!(len == 1 || currentIndex == len - 1)) {
      nextId = nodeData[currentIndex + 1].id;
    }

    let params = {
      id: draggingNode.data.id,
      pid: pid,
      nextId: nextId,
    };
    data.menuLoading = true;
    moveStandardSystem(params)
      .then(response => {
        data.menuLoading = false;
      })
      .catch(() => {
        getCategoryTree();
        data.menuLoading = false;
      });
  };
  watch(searchStr, val => {
    proxy.$refs.treeRef.filter(val);
  });
  /** 通过条件过滤节点  */
  const filterNode = (value, obj) => {
    if (!value) return true;
    return obj.name.indexOf(value) !== -1;
  };

  getCategoryTree();
</script>

<style lang="scss" scoped>
  .standard-system-wrap {
    display: flex;
    height: calc(100vh - 126px);
    .standard-system-left {
      width: 350px;
      flex-shrink: 0;
      background: #ffffff;
      border-radius: 15px;
      overflow-y: auto;
      padding: 30px;
      box-sizing: border-box;

      :deep(.el-tree-node__content) {
        height: 40px !important;
        &:hover {
          background: #e9f0fe;
          border-radius: 5px;
          color: #3377ff;
        }
        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: bold;
          padding-right: 8px;
          height: 40px;
          overflow: hidden;
          .custom-label {
            flex: 1;
            white-space: nowrap;
            overflow: hidden; //文本超出隐藏
            text-overflow: ellipsis;
          }
        }
      }
      :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
        color: #3377ff;
      }
    }
    .standard-system-right {
      margin-left: 20px;
      flex: 1;
      background: #f3f6fd;
      overflow: auto;
    }
  }
</style>
