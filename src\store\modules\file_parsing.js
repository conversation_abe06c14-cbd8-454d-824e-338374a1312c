import { getAnalysisDetail } from '@/api/business_manage/file_parsing'
import { ElMessageBox,ElMessage } from 'element-plus';
import md5 from 'js-md5'
import { processMathText,replaceMathText } from '@/utils/index'


const useAuditStandardStore = defineStore(
  'fileParsingAudit',
  {
    state: () => ({
      treeRef: null,
      fromState: '', // 1: 审核 2: 查看（文本） 3: 二次审核 4: 修正
      categoryList: [],
      content: '',
      pdfUrl: '',
      opType: '', // add,edit
      currentMenu: {},
      standardId: null,
      sectionInfo: {}, // 新增，修改某个章节时的内容
      currentId: null, // 刷新setCurrentKey时用
      initMd5: null, // 新增，修改时name和content的md5值
      checkMd5: null,
      formSection: {}
    }),
    actions: {
      // 获取审核信息
      getAuditData(standardId) {
        return new Promise((resolve, reject) => {
          getAnalysisDetail(standardId).then(res => {
            const data = res.data
            this.categoryList = data.tree || []
            this.content = data.text || ''
            this.pdfUrl = data.url || ''
            this.standardId = standardId
            if(this.treeRef && this.currentId){
              setTimeout(() => {
                this.treeRef.setCurrentKey(this.currentId)
                this.scrollToId(this.currentId)
              }, 100);
            }
            // 解析公式
            setTimeout(() => {
              MathJax.typesetPromise()
            }, 300);

            resolve(res)
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 判断是否是可输入状态
      isInputState() {
        return ['1','4'].includes(this.fromState) && ['add','edit'].includes(this.opType)
      },
      // 判断新增，修改时name,content值是否有修改
      checkIsModifySection() {
        // uMd5 校验时name，content输入的内容md5值
        if (this.formSection.content && this.formSection.content.length > 0 && !this.formSection.content.endsWith('<br>')) {
          this.formSection.content = this.formSection.content + '<br>'
        }

        this.checkMd5 = md5(this.formSection.name+'|'+this.formSection.content)
        return new Promise((resolve, reject) => {
          if(this.initMd5 && this.initMd5 != this.checkMd5){
            ElMessageBox.confirm('当前章条名称或内容有变化信息尚未保存，是否对变化信息进行保存？', '提示', {
              confirmButtonText: '保存',
              cancelButtonText: '放弃',
              type: 'warning',
              showClose: false,
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).then(() => {
              if(this.opType == 'add'){
                this.addAuditSection().then((res)=>{
                  resolve(res)
                }).catch(error => {
                  reject(error)
                })
              }else if(this.opType == 'edit'){
                this.updateAuditSection().then((res)=>{
                  resolve(res)
                }).catch(error => {
                  reject(error)
                })
              }
            }).catch(() => {
              reject()
            })
          }else{
            reject()
          }
        })
      },
      formatFormContent() {
        if(this.formSection.content && this.formSection.content.length > 0) {
          this.formSection.content = replaceMathText(this.formSection.content)
          if (!this.formSection.content.endsWith('<br>')) {
            this.formSection.content = this.formSection.content + '<br>'
          }
        }
      },
      // reset md5值
      resetMd5Data() {
        this.initMd5 = null
        this.checkMd5 = null
      },
      // 滑动到对应id位置
      scrollToId(id) {
        let nId = 't-' + id
        document.getElementById(nId).scrollIntoView({
          behavior: 'smooth'
        })
      },
      // 重置初始量
      resetData() {
        this.fromState = ''
        this.treeRef = null
        this.categoryList = []
        this.content = ''
        this.pdfUrl = ''
        this.opType =  ''
        this.currentMenu = {}
        this.standardId = null
        this.currentId = null
        this.sectionInfo = {}
        this.resetMd5Data()
        this.formSection = {}
      },
    }
  })

export default useAuditStandardStore
