<template>
  <span>
    <el-dropdown @command="handleCommand" trigger="click">
      <el-icon class="pointer"><Plus /></el-icon>
      <!-- <el-icon class="pointer"><MoreFilled /></el-icon> -->
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item :command="beforeHandleCommand('addFolder',item)">新增文件夹</el-dropdown-item>
          <el-dropdown-item :command="beforeHandleCommand('uploadFile',item)">上传文件</el-dropdown-item> 
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 新建文件夹弹框 -->
    <knowledge-add-folder v-if="open" v-model:open="open" @success="addFolderSuccess"></knowledge-add-folder>
  </span>
</template>

<script setup>
import KnowledgeAddFolder from '@/views/components/knowledge/main/KnowledgeAddFolder'

const props = defineProps({
  item: {
    type: Object,
    default: {}
  },
  uploadDom: {
    type: Object,
    default: null
  }
})
const { item,uploadDom } = toRefs(props)

const commonInfo = inject('commonInfo')

const data = reactive({
  open: false
})

const {open} = toRefs(data)

const beforeHandleCommand = (opName,row) => {
 	return {
 		command:opName,
 		row: row
 	}
}
const handleCommand = (command) => {
  switch (command.command) {
    case "addFolder":
      addFolder(command.row)
      break;
    case "uploadFile":
      uploadFile(command.row)
      break;
    default:
      break;
  }
}
const addFolder = (row) => {
  data.open = true
  commonInfo.fileId = row.id ? row.id : 0
}
const uploadFile = (row) => {
  commonInfo.fileId = row.id ? row.id : 0
  uploadDom.value && uploadDom.value.click()
  // proxy.$refs.kFileUpload.$refs.uploadRef.$el.children[0].children[0].click()
}
const addFolderSuccess = () => {
  commonInfo.refreshCategory()
}
</script>

<style lang="scss" scoped>

</style>