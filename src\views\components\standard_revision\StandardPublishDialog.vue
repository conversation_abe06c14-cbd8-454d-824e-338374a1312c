<template>
  <el-dialog
    width="930px"
    title="标准发布"
    append-to-body
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline">
      <el-form-item label="标准号" prop="standardCode">
        <el-input v-model.trim="form.standardCode" placeholder="请输入标准号"></el-input>
      </el-form-item>
      <el-form-item label="标准名称" prop="standardName">
        <el-input maxlength="50" placeholder="请输入标准名称" v-model.trim="form.standardName" />
      </el-form-item>
      <el-form-item label="英文名称" prop="standardNameEn">
        <el-input maxlength="200" placeholder="请输入标准英文名称" v-model.trim="form.standardNameEn" />
      </el-form-item>
      <el-form-item label="标准状态" prop="standardStatus">
        <el-select clearable filterable v-model="form.standardStatus" placeholder="请选择标准状态">
          <el-option v-for="item in bxc_standard_status" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布日期" prop="publishDate">
        <el-date-picker
          clearable
          v-model="form.publishDate"
          type="date"
          placeholder="请选择标准发布日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="实施日期" prop="executeDate">
        <el-date-picker
          clearable
          v-model="form.executeDate"
          type="date"
          placeholder="请选择标准实施日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="标准性质" prop="standardAttr">
        <el-select clearable filterable v-model="form.standardAttr" placeholder="请选择标准性质">
          <el-option v-for="item in bxc_standard_attr" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="单位名称" prop="unitName">
        <el-input maxlength="50" placeholder="请输入标准发布单位名称" v-model.trim="form.unitName" />
      </el-form-item>
      <el-form-item label="全部替代标准号" prop="allReplaceStandardCode">
        <el-input maxlength="50" placeholder="请输入全部替代标准号，以英文逗号分割" v-model.trim="form.allReplaceStandardCode" />
      </el-form-item>
      <el-form-item label="制修订" prop="amend">
        <el-select :disabled="true" clearable filterable v-model="form.amend" placeholder="请选择制修订">
          <el-option v-for="item in bxc_standard_amend" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="主要起草单位" prop="draftUnits" class="one-column">
        <el-input
          v-model="form.draftUnits"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入主要起草单位名称，多个标准起草单位名称时，请使用中文顿号 (、)分割"
        />
      </el-form-item>
      <el-form-item label="主要起草人" prop="drafters" class="one-column">
        <el-input
          v-model="form.drafters"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入主要起草人姓名，多个标准起草人姓名时，请使用中文顿号 (、)分割"
        />
      </el-form-item>
      <el-form-item label="标准文件" prop="standardTextFilesList" class="one-column">
        <div>
          <ele-upload-image
            v-model:value="form.standardTextFilesList"
            :fileSize="200"
            :multiple="false"
            :fileType="['pdf']"
            :responseFn="handleResponse"
            @success="handleUpload('standardTextFilesList')"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：pdf；文件大小不超过200MB
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">发布</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { standardPublish } from '@/api/standard_revision/manage';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();

  const { bxc_standard_attr, bxc_standard_amend, bxc_standard_status } = proxy.useDict(
    'bxc_standard_attr',
    'bxc_standard_amend',
    'bxc_standard_status'
  );

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    defaultForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    standardCode: [{ required: true, message: '请输入标准号', trigger: 'blur' }],
    standardName: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    standardStatus: [{ required: true, message: '请选择标准状态', trigger: 'change' }],
    standardAttr: [{ required: true, message: '请选择标准性质', trigger: 'change' }],
    publishDate: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
    executeDate: [{ required: true, message: '请选择实施日期', trigger: 'change' }],
    amend: [{ required: true, message: '请选择制修订', trigger: 'change' }],
    standardTextFilesList: [{ required: true, message: '请选择标准文件', trigger: 'change' }],
  });

  onMounted(() => {
    form.value = { ...props.defaultForm };
    form.value.allReplaceStandardCode = form.value.beReplacedStandardCode;
  });

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = type => {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        proxy
          .$confirm('确认已经正确且完整填写标准信息，且发布该标准？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            standardPublish(form.value).then(res => {
              emit('updateData');
              handleClose();
            });
          })
          .catch(() => {});
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped></style>
