<template>
  <el-dialog
    width="925px"
    title="意见处理"
    append-to-body
    v-model="props.visible"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="h-title">反馈意见</div>
    <el-descriptions :column="2" size="large" class="br-5 mt20">
      <el-descriptions-item label="提交人：" label-align="left">
        {{ form.createBy || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="所属部门：" label-align="left">
        {{ form.feedbackDept || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="提交时间：" label-align="left">
        {{ form.createTime || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="意见：" :span="2" label-align="left">
        {{ form.opinionContent || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="附件：" :span="2" label-align="left">
        <ele-upload-image
          v-if="form.feedbackFileList && form.feedbackFileList.length > 0"
          :isShowUploadIcon="false"
          v-model:value="form.feedbackFileList"
          class="mt10"
        />
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-title mt10">意见处理</div>
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline mt10">
      <el-form-item label="处理结果" prop="processResult">
        <el-select v-model="form.processResult" placeholder="请选择处理结果">
          <el-option
            v-show="item.value != 0"
            v-for="item in bxc_opinion_process_result"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="说明" prop="processContent" class="one-column">
        <el-input
          v-model="form.processContent"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入意见处理说明信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { putFeedback } from '@/api/standard_revision/review';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();
  const { bxc_opinion_process_result } = proxy.useDict('bxc_opinion_process_result');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    defaultForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    processResult: [{ required: true, message: '请选择处理结果', trigger: 'blur' }],
  });

  onMounted(() => {
    form.value = { ...props.defaultForm };
  });

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        putFeedback(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('处理成功！');
            emit('success');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped>
  .br-5 {
    border-radius: 5px !important;
    overflow: hidden !important;
  }

  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
