<template>
  <div class="app-container">
    <div class="data-center-wrap">
      <div class="flex flex-center c-33 f-22">
        <div class="f-bold">企业标准库统计分析</div>
      </div>
      <!-- 图表区 -->
      <div class="chart-list">
        <div class="chart-item">
          <div class="h-title">标准类型分析</div>
          <data-center-stat-chart v-if="chartData && chartData.length > 0" :show-title="true" :chart-data="chartData" @select="chartSelect" />
          <empty v-else />
        </div>
        <div class="chart-item">
          <div class="h-title">标准状态分析</div>
          <data-center-stat-chart v-if="childData && childData.length > 0" :show-title="true" :chart-data="childData" />
          <empty v-else />
        </div>
      </div>
      <!-- 统计报表 -->
      <div class="stat-wrap mt40">
        <div class="h-title">统计报表</div>
        <div class="flex flex-jc-end">
          <el-button
            v-hasPermi="['data_center:library_stat:export']"
            @click="handleExport"
            type="primary"
          >
            <i class="iconfont icon-daochu mr5"></i>
            导出
          </el-button>
        </div>
        <el-table
          v-loading="loading"
          ref="tableRef"
          :data="dataList"
          class="mt15"
          :border="true"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column
            prop="standardTypeName"
            label="标准类型"
            min-width="100"
            fixed="left"
            show-overflow-tooltip
          />
          <el-table-column
            prop="standardTotal"
            label="标准数量"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="standardTotalProportion"
            label="数量占比"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="futureTotal"
            label="即将实施数"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="executeTotal"
            label="现行有效数"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="repealTotal"
            label="废止数"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="replacedTotal"
            label="被替代数"
            min-width="100"
            show-overflow-tooltip
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getLibraryStatList,getLibraryStatChart } from '@/api/data_center/analysis'
import DataCenterStatChart from '@/views/components/data_center/DataCenterStatChart'

const { proxy } = getCurrentInstance()

const data = reactive({
  loading: false,
  dataList: [],
  chartData: [],
  childData: [],
  originChildData: []
});
const {
  loading,
  dataList,
  chartData,
  childData,
  originChildData
} = toRefs(data);

const getList = () => {
  data.loading = true;
  getLibraryStatList().then((response) => {
    if(response.data){
      data.dataList = response.data
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const getChartData = (type) => {
  data.loading = true
  getLibraryStatChart().then((response) => {
    if(response.data){
      data.chartData = response.data.typeAllList
      data.childData = response.data.statusAllList
      data.originChildData = response.data.statusAllList
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
const chartSelect = (info) => {
  if(info.type == '-99') { // 点击标题
    data.childData = data.originChildData
  }else{
    data.childData = info.list
  }
  
}
const handleExport = () => {
  proxy.download("/process/common/standard/query/statistics/data/export", {}, `企业标准库统计分析_${new Date().getTime()}.xlsx`);
}

getChartData()
getList()
</script>

<style lang="scss" scoped>
.data-center-wrap {
  background: #FFFFFF;
  border-radius: 15px;
  // height: calc(100vh - 130px);
  padding: 30px;
  .chart-list{
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px 0px;
    .chart-item{
      width: calc((100% - 20px)/2);
    }
  }
}
</style>