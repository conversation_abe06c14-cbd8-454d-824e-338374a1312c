import { getStandardTree, getStandardTreeContentById, getStandardTreeContent, getStandardCompare, getRecommendStandardList } from '@/api/application_tool/comparison'
import { ElLoading } from 'element-plus';
import $modal from '@/plugins/modal';

const useComparisonStore = defineStore('comparison', {
  state: () => ({
    tabList: [{
        id: 1,
        name: '条文比对',
      },{
        id: 2,
        name: '全文比对',
      },{
        id: 3,
        name: '目录比对',
      },
      // {
      //   id: 4,
      //   name: '表格比对',
      // },{
      //   id: 5,
      //   name: '图片比对',
      // },{
      //   id: 6,
      //   name: '公式比对',
      // }
    ],
    currentTabId: 1,
    compareType: 'origin',
    originInfo: {},
    compareInfo: {},
    originTreeList: [],
    compareTreeList: [],
    originContent: '',
    compareContent: '',
    currentOriginNode: {},
    currentCompareNode: {},
    insTextList: [],
    delTextList: [],
    spanTextMapList: [],
    isVsing: false,
    recommendInfo: {}
  }),
  getters: {
    
  },
  actions: {
    exchangeInfo() {
      if(JSON.stringify(this.originInfo.value) == '{}' && JSON.stringify(this.compareInfo) == '{}'){
        $modal.msgError('请选择比对的原始标准与比对标准！')
        return
      }
      const temp = this.originInfo
      this.originInfo = this.compareInfo
      this.compareInfo = temp

      this.setTabClick()
    },
    resetInitInfo(){
      this.originTreeList = [] 
      this.compareTreeList = []
      this.originContent = ''
      this.compareContent = ''
      this.currentOriginNode = {}
      this.currentCompareNode = {}
      this.isVsing = false
      this.recommendInfo = {}
    },
    // 重置参数
    _resetTreeInfo(type = 'origin'){
      if(type == 'origin'){
        this.originTreeList = []
        this.originContent = ''
        this.currentOriginNode = {} 
      }else{
        this.compareTreeList = []
        this.compareContent = ''
        this.currentCompareNode = {}
      } 
    },
    getArticleTree(standardId, type = 'origin'){
      this._resetTreeInfo(type)
      getStandardTree({standardId:standardId}).then((res) => {
        if(type == 'origin'){
          this.originTreeList = res.data.tree || [] 
          this.originContent = ''
          if(this.originTreeList.length > 0) {
            this.currentOriginNode = (this.originTreeList[0]) || {}
            this.handleArticleNodeClick(this.originTreeList[0], this.originTreeList, this.originInfo.id, 'origin')
          }
        }else{
          this.compareTreeList = res.data.tree || []
          this.compareContent = ''
          if(this.compareTreeList.length > 0) {
            this.currentCompareNode = (this.compareTreeList[0]) || ''
            this.handleArticleNodeClick(this.compareTreeList[0], this.compareTreeList, this.compareInfo.id, 'compare')
          }
        }
      })
    },
    handleArticleNodeClick(item, nodes,standardId = '',type = 'origin'){
      if(this.isVsing){
        this.isVsing = false
        if(type == 'origin'){
          this.handleArticleNode(item, nodes,standardId,type)
          this.handleArticleNode(this.currentCompareNode,[],this.compareInfo.id,'compare')
        }else{
          this.handleArticleNode(item, nodes,standardId,type)
          this.handleArticleNode(this.currentOriginNode,[],this.originInfo.id,'origin')
        }
      }else{
        this.handleArticleNode(item, nodes,standardId,type)
      }
    },
    handleArticleNode(item, nodes,standardId = '',type = 'origin'){
      if(!item.id || !standardId){
        return
      }

      let params = {
        indexId: item.id,
        standardId: standardId,
      }
      
      getStandardTreeContentById(params).then((res) => {
        if(type == 'origin'){
          this.currentOriginNode = item
          this.originContent = res.data.text || '' 
        }else{
          this.currentCompareNode = item
          this.compareContent = res.data.text || ''
        }
        // 解析公式
        this._mathjax()
      })
    },
    setTabClick(){
      if(this.currentTabId == 1){
        if(this.originInfo.id){
          this.getArticleTree(this.originInfo.id,'origin')
        }else{
          this.originTreeList = []
          this.originContent = ''
          this.currentOriginNode = {}
        }
        if(this.compareInfo.id){
          this.getArticleTree(this.compareInfo.id,'compare')
        }else{
          this.compareTreeList = []
          this.compareContent = ''
          this.currentCompareNode = {}
        }
      }else if(this.currentTabId == 2){
        if(this.originInfo.id){
          this.getFullTextTree(this.originInfo.id,'origin')
        } else{
          this.originTreeList = []
          this.originContent = ''
          this.currentOriginNode = {}
        }
        if(this.compareInfo.id){
          this.getFullTextTree(this.compareInfo.id,'compare') 
        }else{
          this.compareTreeList = []
          this.compareContent = ''
          this.currentCompareNode = {}
        }
      }else if(this.currentTabId == 3){
        if(this.originInfo.id){
          this.getCatalogTree(this.originInfo.id,'origin')
        }else{
          this.originTreeList = []
        }
        if(this.compareInfo.id){
          this.getCatalogTree(this.compareInfo.id,'compare') 
        }else{
          this.compareTreeList = []
        }
      }
    },
    getFullTextTree(standardId, type = 'origin'){
      this._resetTreeInfo(type)
      getStandardTreeContent(standardId).then((res) => {
        if(type == 'origin'){
          this.originTreeList = res.data.tree || [] 
          this.originContent = res.data.text || ''
        }else{
          this.compareTreeList = res.data.tree || []
          this.compareContent = res.data.text || ''
        }
        // 解析公式
        this._mathjax()
      }) 
    },
    handleFullTextNodeClick(item, nodes){

    },
    getCatalogTree(standardId, type = 'origin'){
      // 重置参数
      if(type == 'origin'){
        this.originTreeList = [] 
      }else{
        this.compareTreeList = []
      }
      getStandardTree({standardId:standardId}).then((res) => {
        if(type == 'origin'){
          this.originTreeList = res.data.tree || [] 
        }else{
          this.compareTreeList = res.data.tree || []
        }
      })
    },
    handleVs() {
      // 重置参数
      this.insTextList = []
      this.delTextList = []
      this.spanTextMapList = []
      this.isVsing = false

      if(JSON.stringify(this.originInfo.value) == '{}' || JSON.stringify(this.compareInfo) == '{}'){
        $modal.msgError('请选择比对的原始标准与比对标准！')
        return
      }
      if(this.currentTabId == 1){ // 条文比对
        if(!this.currentOriginNode.id || !this.currentCompareNode.id){
          $modal.msgError('请选择有效原始标准与比对标准！')
          return
        }
      }
      let params = {
        leftStandardId: this.originInfo.id,
        rightStandardId: this.compareInfo.id,
        leftIndexId: this.currentOriginNode.id,
        rightIndexId: this.currentCompareNode.id,
        compareType: this.currentTabId - 1,
      }
      let downloadLoadingInstance;
      downloadLoadingInstance = ElLoading.service({ text: '正在比对数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
      getStandardCompare(params).then((res) => {
        this.originContent = res.data.leftStr || ''
        this.compareContent = res.data.rightStr || '' 
        if(this.currentTabId == 3){
          this.originTreeList = res.data.leftTree || []
          this.compareTreeList = res.data.rightTree || []
        }
        this.insTextList = res.data.insTextList || []
        this.delTextList = res.data.delTextList || []
        this.spanTextMapList = res.data.spanTextMapList || []
        // 解析公式
        this._mathjax()
        this.isVsing = true
      }).finally(() => {
        downloadLoadingInstance.close();
      })
      
    },
    _mathjax() {
      // 解析公式
      setTimeout(() => {
        MathJax.typesetPromise()
      }, 200);
    },
    // 获取推荐标准列表,只有原始标准或比对标准其中一个有值，另一个没有值时，才会调用接口获取对方推荐标准信息
    getRecommendList(type, standardId) {
      
      
      // this.recommendInfo = {
      //   beReplacedStandardList: [],
      //   replaceStandardList: []
      // }

      return new Promise((resolve, reject) => {
        if((type == 'origin' && this.compareInfo.id)
        || (type == 'compare' && this.originInfo.id)){
          return reject()
        }
        if(!standardId){
          return reject()
        }

        this.recommendInfo = {}
        getRecommendStandardList(standardId).then(res => {
          if(res.data){
            this.recommendInfo = res.data
          }
          resolve(this.recommendInfo)
        }).catch(error => {
          reject(error)
        })
      })
      
    }
  }
})

export default useComparisonStore