<template>
  <el-dialog
    width="925px"
    title="征求意见反馈"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <el-descriptions v-if="props.defaultForm.joinType == 0" :column="2" size="large">
      <el-descriptions-item label="项目编号：" :span="2" label-align="left">
        {{ form.projectCode || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="项目名称：" :span="2" label-align="left">
        {{ form.projectName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="项目介绍：" :span="2" label-align="left">
        {{ form.projectIntroduce || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions v-else :column="2" size="large">
      <el-descriptions-item label="标准编号：" :span="2" label-align="left">
        {{ form.standardCode || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="标准名称：" :span="2" label-align="left">
        {{ form.standardName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="所属复审项目编号：" :span="2" label-align="left">
        {{ form.projectCode || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-title mt10">反馈意见</div>
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline mt10">
      <el-form-item label="意见类别" prop="opinionType" class="one-column">
        <el-radio-group v-model="form.opinionType">
          <el-radio v-for="item in bxc_opinion_type" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="意见内容" prop="opinionContent" class="one-column">
        <el-input
          v-model="form.opinionContent"
          :rows="5"
          :show-word-limit="true"
          maxlength="1000"
          type="textarea"
          placeholder="请输入意见反馈说明信息"
        />
      </el-form-item>
      <el-form-item label="附件" prop="feedbackFileList" class="one-column">
        <div>
          <ele-upload-image
            v-model:value="form.feedbackFileList"
            :fileSize="5"
            :limit="form.joinType == 0 ? 9 : 1"
            :fileType="['jpeg', 'jpg', 'png', 'bmp', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
            :multiple="true"
            :responseFn="handleResponse"
            @success="handleUpload('feedbackFileList')"
          />
          <div class="c-EE0000 mt10 flex flex-ai-center">
            <span class="iconfont icon-tishi f-16 f-bold mr5"></span>
            支持文件格式：jpeg、jpg、png、bmp、word、excel、pdf；单个文件大小不超过5MB
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addOpinionFeedback } from '@/api/standard_revision/opinion';
  import EleUploadImage from '@/components/EleUploadImage';

  const { proxy } = getCurrentInstance();
  const { bxc_opinion_type } = proxy.useDict('bxc_opinion_type');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    defaultForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    opinionType: [{ required: true, message: '请选择意见类别', trigger: 'change' }],
  });

  onMounted(() => {
    form.value = { ...props.defaultForm };
  });

  const handleResponse = (response, file, fileList) => {
    return { id: response.data.id, url: response.data.url, name: response.data.name };
  };

  const handleUpload = type => {
    nextTick(() => {
      proxy.$refs.queryRef.validateField(type);
    });
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        addOpinionFeedback(form.value)
          .then(res => {
            proxy.$modal.msgSuccess('反馈成功！');
            emit('updateData');
            handleClose();
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'updateData']);
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
