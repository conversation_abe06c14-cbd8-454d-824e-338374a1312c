<template>
  <div>
    <el-dialog
      width="860"
      title="编辑"
      append-to-body
      v-model="dialogVisible"
      :before-close="handleClose"
    >
    <el-descriptions
      class="margin-top"
      :column="2"
      border
    >
      <el-descriptions-item :span="2">
        <template #label>
          <div>
            职位
          </div>
        </template>
        {{ form.postGroup || '-' }}
      </el-descriptions-item>
      <el-descriptions-item :span="2">
        <template #label>
          <div>
            所属部门
          </div>
        </template>
        <div v-if="form.user">
          {{ form.user.dept.deptName || '-' }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="2">
        <template #label>
          <div>
            所属角色
          </div>
        </template>
        {{ form.roleGroup || '-' }}
      </el-descriptions-item>
    </el-descriptions>
      <el-form
        label-width="auto"
        :model="form.user"
        :label-position="'top'"
        ref="queryRef"
        :rules="rules"
        class="pl10 mt5 dia-form"
      >
        <el-form-item label="性别" prop="sex">
          <template v-if="form.user">
            <el-radio-group v-model="form.user.sex" class="ml-4">
              <el-radio label="0">男</el-radio>
              <el-radio label="1">女</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <template v-if="form.user">
            <el-input
              maxlength="50"
              placeholder="请输入手机号码"
              v-model="form.user.phonenumber"
            />
          </template>
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <template v-if="form.user">
            <el-input
              maxlength="50"
              placeholder="请输入邮箱地址"
              v-model="form.user.email"
            />
          </template>
          
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            @click="handleConfirm"
            :loading="loading"
            type="primary"
            
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { updateUserProfile,getUserProfile } from '@/api/system/user'

const { proxy } = getCurrentInstance();


const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
});
const { dialogVisible } = toRefs(props);

const data = reactive({
  dataList:[],
  clientDialog: false,
  clientContactDialog: false,
  loading: false,
  options: [],
  form: {},
  user:{}
});
const {loading, form,user } = toRefs(data);

const rules = ref({
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
  phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }],
});

onMounted(() => {
  // if (id.value) getDetail();
});

function getUser() {
  getUserProfile().then(res => {
    // user.value = res.data.user;
    form.value = res.data;
    data.form.user = res.data.user;
  })
};

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      updateUserProfile(form.value.user)
      .then(() => {
        proxy.$modal.msgSuccess('编辑成功')
        emit("getUser");
        handleClose();
      })
      .finally(() => {
        loading.value = false;
      });
    }
  });
};

const handleClose = () => {
  emit("update:dialogVisible", false);
  proxy.$refs.queryRef.resetFields();
};

getUser();
const emit = defineEmits(["update:dialogVisible", "getUser"]);
</script>

<style lang="scss" scoped>
.dia-form{
  width: 100%;
  .el-form-item{
    width: 48%;
  }
}
.upload-notice{
  margin-top: 5px;
  img{
    height: 18px;
    margin-right: 8px;
  }
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper){
  width: 100% !important;
}

:deep(.el-form-item__label){
  font-size: 14px !important;
  color: #333;
}


</style>
