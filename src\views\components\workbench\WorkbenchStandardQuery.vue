<template>
  <div class="standard-query-wrap">
    <div class="f-22 f-bold c-33 mb10">标准查阅</div>
    <div style="position: relative;">
      <el-tabs v-model="activeName" class="workbench-tabs" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in info" :key="index" :label="item.title" :name="index">
          <workbench-standard-query-list :list="item.list" />
        </el-tab-pane>
      </el-tabs>
      <!-- <el-button class="more-btn" type="primary" link>
        更多<el-icon class="el-icon--right"><ArrowRight /></el-icon>
      </el-button> -->
    </div>
  </div>
</template>

<script setup>
import WorkbenchStandardQueryList from '@/views/components/workbench/WorkbenchStandardQueryList'
import { getStandardQueryList } from '@/api/workbench'

const data = reactive({
  activeName: 0,
  info: {}
})
const { activeName,info } = toRefs(data)
const handleClick = (val) => {
  
}
const getData = () => {
  data.loading = true;
  getStandardQueryList({pageSize: 5}).then((response) => {
    if(response.data){
      data.info = response.data
    }
    data.loading = false
  }).catch(() => {
    data.loading = false
  });
}
getData()
</script>

<style lang="scss" scoped>

.more-btn{
  position: absolute;
  right: 0;
  top: 10px;
}
.standard-query-wrap {
  padding: 30px;
  flex: 1;
  height: 416px;
  background: #FFFFFF;
  border-radius: 15px;
  box-sizing: border-box;
  overflow: hidden;
}
</style>