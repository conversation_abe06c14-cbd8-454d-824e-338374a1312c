<template>
  <el-dialog
    width="1100px"
    title="添加试验项目"
    append-to-body
    class="test_sheet_dialog"
    v-model="props.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="flex flex-sb">
      <div class="dialog-left">
        <el-form :model="queryParams" ref="queryRef" label-width="auto" inline @submit.prevent>
          <el-form-item label="" prop="keyword" style="width: 360px">
            <el-input v-model="queryParams.keyword" placeholder="检索指标项、试验方法、所属标准名称关键词" />
          </el-form-item>
          <el-form-item label="">
            <el-button @click="getList('pageNum')" type="primary" icon="Search">查询</el-button>
            <el-button plain @click="handleClear" icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="tableData"
          :border="true"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <template v-slot:empty>
            <empty />
          </template>
          <el-table-column :reserve-selection="true" type="selection" />
          <el-table-column label="序号" type="index" width="70" fixed>
            <template #default="{ $index }">
              {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="indexName" label="检验项目" show-overflow-tooltip min-width="180" />
          <el-table-column prop="experimentalPlan" label="试验方法" show-overflow-tooltip min-width="180" />
          <el-table-column prop="indexContent" label="试验要求" show-overflow-tooltip min-width="150" />
          <el-table-column prop="fromStandardCode" label="所属标准" show-overflow-tooltip min-width="150" />
        </el-table>
        <pagination
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <div class="dialog-right">
        <div class="dialog-right-title">已选项目（{{ selectedRows.length }}）</div>
        <div class="scroller-bar-style">
          <div v-for="(item, index) in selectedRows" :key="index" class="dialog-right-item">
            <div class="dialog-right-item-index">{{ index + 1 }}</div>
            <div class="dialog-right-item-content">
              <div class="overflow-ellipsis">{{ item.indexName }}</div>
              <div class="overflow-ellipsis mt5">{{ item.fromStandardCode }}</div>
            </div>
            <el-icon @click="handleUncheck(item)" class="dialog-right-item-icon"><Delete class="f-20 c-FF0000" /></el-icon>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="info" @click="handleClose">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getTestSheetList } from '@/api/business_manage/test_sheet';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    selectTable: {
      type: Array,
      default: () => {
        return [];
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const tableRef = ref();
  const tableLoading = ref(false);
  const btnLoading = ref(false);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
  });
  const tableData = ref([]);
  const total = ref(0);
  const selectedRows = ref([]);

  const handleClear = () => {
    proxy.$refs.queryRef.resetFields();
    getList('pageNum');
  };

  const getList = val => {
    tableLoading.value = true;
    if (val) queryParams.value[val] = 1;
    getTestSheetList(queryParams.value)
      .then(res => {
        tableData.value = res.rows;
        total.value = res.total;
      })
      .finally(() => {
        tableLoading.value = false;
      });
  };

  const handleSelectionChange = selection => {
    selectedRows.value = selection;
  };

  const handleUncheck = row => {
    const selectData = selectedRows.value;
    tableRef.value.clearSelection();
    selectData.forEach(item => {
      tableRef.value.toggleRowSelection(item, item.id != row.id ? true : false);
    });
    proxy.$nextTick(() => {
      getList();
    });
  };

  const handleConfirm = () => {
    btnLoading.value = true;
    if (!selectedRows.value.length) {
      proxy.$message.warning('请选择试验项目');
      btnLoading.value = false;
    } else {
      emit('chooseData', selectedRows.value);
      handleClose();
    }
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'chooseData']);

  getList();
  proxy.$nextTick(() => {
    if (props.selectTable && props.selectTable.length > 0) {
      props.selectTable.forEach(row => {
        tableRef.value.toggleRowSelection(row, true);
      });
    }
  });
</script>

<style lang="scss">
  .test_sheet_dialog .el-table th.el-table__cell:nth-child(1) .cell {
    display: none !important;
  }

  .test_sheet_dialog .el-dialog__body {
    padding-bottom: 10px !important;
  }
</style>
<style lang="scss" scoped>
  .dialog {
    &-left {
      width: 0;
      flex: 1;
    }

    &-right {
      width: 260px;
      border: 1px solid #e3e3e3;
      margin-left: 30px;
      min-height: calc(100vh - 500px);

      &-title {
        height: 50px;
        padding: 0 20px !important;
        box-sizing: border-box;
        line-height: 50px;
        font-size: 16px;
        font-weight: 600;
        color: $primary-color;
        border-bottom: 1px solid #e3e3e3;
      }

      &-item {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #e3e3e3;

        &-index {
          width: 15%;
          font-size: 16px;
        }

        &-content {
          width: 70%;
          font-size: 14px;
        }

        &-icon {
          width: 15%;
          font-size: 18px;
          cursor: pointer;
        }
      }
    }
  }

  .pagination-container {
    padding-bottom: 0 !important;
  }

  .scroller-bar-style {
    overflow-y: scroll;
    max-height: 480px;
    padding: 0 18px;
    box-sizing: border-box;
  }
</style>
