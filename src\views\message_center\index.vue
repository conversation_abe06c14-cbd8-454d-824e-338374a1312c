<template>
  <div class="wrap">
    <div class="message-header flex flex-sb mb20">
      <div class="flex flex-ai-center f-14">
        消息类型：
        <div class="tab mr10 pointer" @click="toggle('all')" :class="active == 'all' ? 'active' : ''">全部</div>
        <el-badge :is-dot="standardUnread == 0 ? false : true" class="mr10">
          <div class="tab pointer" @click="toggle('standard')" :class="active == 'standard' ? 'active' : ''">标准动态</div>
        </el-badge>

        <el-badge :is-dot="feedbackUnread == 0 ? false : true" class="mr10">
          <div class="tab pointer" @click="toggle('feedback')" :class="active == 'feedback' ? 'active' : ''">反馈回复</div>
        </el-badge>

        <el-badge :is-dot="feedbackUnread == 0 ? false : true" class="mr10">
          <div class="tab pointer" @click="toggle('opinion')" :class="active == 'opinion' ? 'active' : ''">意见征集</div>
        </el-badge>

        <el-badge :is-dot="processUnread == 0 ? false : true">
          <div class="tab pointer" @click="toggle('process')" :class="active == 'process' ? 'active' : ''">申请通知</div>
        </el-badge>
      </div>
      <div class="c-33 f-14">
        <el-button v-hasPermi="['message_center:readAll']" @click="readAll" plain>
          <i class="iconfont icon-yidu f-14 mr5"></i>
          全部已读
        </el-button>
        <el-button type="primary" @click="batch">
          <i class="iconfont icon-piliangxuanze f-14 mr5"></i>
          <div v-if="!isBatch">批量设置</div>
          <div v-else>取消批量设置</div>
        </el-button>
        <el-button
          v-hasPermi="['message_center:delete']"
          @click="deleteMessage"
          :disabled="deleteParams.ids.length == 0"
          plain
          v-if="isBatch"
        >
          <el-icon class="mr5"><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </div>
    <div class="message-content">
      <template v-if="dataList && dataList.length > 0">
        <div
          @click="toDetail(item)"
          class="message-item flex flex-ai-center flex-sb pointer"
          v-for="item in dataList"
          :key="indexedDB"
        >
          <div class="flex flex-ai-center flex-1 overflow-ellipsis">
            <el-checkbox
              @click.stop
              class="mr15"
              v-if="isBatch"
              @change="checked => handleChange(checked, item)"
              v-model="item.dataId"
            />
            <img v-if="item.messageType == 1" src="@/assets/images/personal/message-feedback.png" alt="" />
            <img v-if="item.messageType == 0" src="@/assets/images/personal/message-stadard.png" alt="" />
            <i v-if="item.messageType == 2" class="iconfont icon-shenpizhongxin f-24 c-36cb22"></i>
            <i v-if="item.messageType == 3" class="iconfont icon-shenqing f-24 c-primary"></i>

            <div class="ml15 flex-1 overflow-ellipsis">
              <div class="title f-bold overflow-ellipsis mb10">{{ item.messageTitle }}</div>
              <div class="f-14 overflow-ellipsis">{{ item.messageContent }}</div>
            </div>
          </div>
          <div class="date-wrap">
            <div class="c-99 f-14 mb10">{{ item.createTime }}</div>
            <div v-if="item.readTag == 0" class="blot-red float-r"></div>
          </div>
        </div>
      </template>
      <empty v-else />

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="params.pageNum"
        v-model:limit="params.pageSize"
        @pagination="getList"
      />
    </div>
    <feedback-detail-dialog v-if="detailDialog" :id="currentId" v-model:dialogVisible="detailDialog" />
    <pop-apply-detail v-if="processDetailDialog" v-model:open="processDetailDialog" :id="currentId" />
    <!-- 标准查看 -->
    <detail-drawer v-if="drawerVisible" v-model:visible="drawerVisible" :standardId="standardId" :standardType="standardType" />
  </div>
  <detail-opinion-drawer v-if="detailVisible" v-model:visible="detailVisible" :id="currentId" @updateData="getList" />
</template>

<script setup>
  import PopApplyDetail from '@/views/components/approve/PopApplyDetail';
  import FeedbackDetailDialog from '@/views/components/standard_revision/feedback/FeedbackDetailDialog';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';
  import DetailOpinionDrawer from '@/views/components/standard_revision/DetailOpinionDrawer.vue';
  import {
    getMessagePublishList,
    messagePublishReadTag,
    messagePublishRemove,
    unReadCountTopAndCount,
  } from '@/api/message/message';
  import { toRefs } from 'vue';
  import useMessageStore from '@/store/modules/message';

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const messageStore = useMessageStore();
  const state = reactive({
    active: 'all',
    isBatch: false,
    params: {
      pageNum: 1,
      pageSize: 10,
      messageType: '',
    },
    total: 0,
    detailDialog: false,
    processDetailDialog: false,
    currentId: null,
    checkList: [],
    dataList: [],
    feedbackUnread: 0,
    standardUnread: 0,
    processUnread: 0,
    form: {
      messageId: null,
    },
    deleteParams: {
      ids: [],
    },
  });

  const {
    active,
    isBatch,
    params,
    detailDialog,
    processDetailDialog,
    currentId,
    checkList,
    total,
    standardUnread,
    feedbackUnread,
    processUnread,
    dataList,
    deleteParams,
  } = toRefs(state);

  const detailVisible = ref(false);
  const standardId = ref('');
  const standardType = ref(undefined);
  const drawerVisible = ref(false);

  const toggle = type => {
    state.active = type;
    switch (type) {
      case 'process':
        state.params.messageType = '2';
        break;
      case 'feedback':
        state.params.messageType = '1';
        break;
      case 'opinion':
        state.params.messageType = '3';
        break;
      case 'standard':
        state.params.messageType = '0';
        break;
      default:
        state.params.messageType = '';
        break;
    }
    getList();
    // getCount()
  };
  const batch = () => {
    state.isBatch = !state.isBatch;
  };

  const getList = () => {
    getMessagePublishList(state.params).then(res => {
      state.dataList = res.rows;
      state.feedbackUnread = res.otherInfo.feedbackUnread || 0;
      state.standardUnread = res.otherInfo.standardUnread || 0;
      state.processUnread = res.otherInfo.processUnread || 0;
      state.total = res.total;
    });
  };

  const handleChange = (checked, item) => {
    if (checked == true && state.deleteParams.ids.indexOf(item.messageId) == -1) {
      state.deleteParams.ids.push(item.messageId);
    } else {
      state.deleteParams.ids.splice(
        state.deleteParams.ids.findIndex(n => n == item.messageId),
        1
      );
    }
  };

  const toDetail = item => {
    state.showMessage = false;
    if (item.messageType == 1) {
      state.currentId = item.dataId;
      state.detailDialog = true;
    } else if (item.messageType == 0) {
      standardId.value = item.dataId;
      standardType.value = item.standardType;
      drawerVisible.value = true;
    } else if (item.messageType == 3) {
      state.currentId = item.dataId;
      detailVisible.value = true;
    } else {
      if (item.recordDelFlag && item.recordDelFlag == '1') {
        proxy.$modal.msgError('该数据不存在或已被删除！');
      } else {
        state.currentId = item.dataId;
        state.processDetailDialog = true;
      }
    }
    state.form.messageId = item.messageId;
    //标记为已读
    messagePublishReadTag(state.form).then(res => {
      getList();
      getCount();
    });
  };

  //全部已读
  const readAll = () => {
    messagePublishReadTag().then(res => {
      if (messageStore.unReadCount == 0) {
        proxy.$modal.msgSuccess('暂无未读消息');
      } else {
        getList();
        getCount();
        proxy.$modal.msgSuccess('全部已读成功');
      }
    });
  };

  const getCount = () => {
    unReadCountTopAndCount(state.queryParams).then(res => {
      messageStore.unReadCount = res.otherInfo.unReadCount;
    });
  };

  //删除
  const deleteMessage = () => {
    proxy
      .$confirm('您确定要删除选中消息吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        messagePublishRemove(state.deleteParams).then(res => {
          proxy.$modal.msgSuccess('删除成功');
          state.deleteParams.ids = [];
          getList();
          getCount();
        });
      })
      .catch(() => {});
  };
  getList();
</script>

<style lang="scss" scoped>
  .wrap {
    padding: 20px;
  }
  .tab {
    box-sizing: border-box;
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    border: 1px solid #d2d9e9;
    border-radius: 3px;
  }
  .message-header {
    box-sizing: border-box;
    height: 80px;
    background-color: #fff;
    border-radius: 15px;
    padding: 25px 30px;
  }
  .message-content {
    box-sizing: border-box;
    background-color: #fff;
    padding: 25px 30px;
    border-radius: 15px;
  }

  .message-item {
    box-sizing: border-box;
    padding: 12px 0;
    border-bottom: 1px solid #e8e8e8;
    img {
      height: 24px;
    }
  }
  .active {
    background-color: #2f5aff;
    color: #fff;
  }
  .btn-icon {
    height: 14px;
  }
  .blot-red {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #ff0000;
  }
  .date-wrap {
    width: 200px;
    text-align: right;
  }
  .w-400 {
    width: 400px;
  }
  .w-70 {
    width: 70%;
  }
  .float-r {
    float: right;
  }
  .mr15 {
    margin-right: 15px;
  }
  .c-36cb22 {
    color: #36cb22;
  }
  :deep(.el-badge__content.is-dot) {
    width: 10px;
    height: 10px;
  }
</style>
