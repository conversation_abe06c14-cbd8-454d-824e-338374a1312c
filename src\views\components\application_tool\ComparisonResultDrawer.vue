<template>
  <el-drawer title="比对清单" :size="600" append-to-body v-model="props.open" :before-close="handleClose">
    <div class="result-drawer">
      <ul class="tab-list">
        <li v-for="item in tabList" :key="item.id" @click="handleTabClick(item)" class="tab-item" :class="{'active-item':`${currentTabId}` == `${item.id}` }">{{ item.name }}</li>
      </ul>
      <div class="result-content">
        <div class="result-list">
          <div v-if="currentItemList.length == 0" class="flex flex-jc-center">
            <div class="f-14 c-99 mt30">暂无数据</div>
          </div>
          <template v-else>
          <div v-for="(obj,index) in currentItemList" :key="index" class="result-item" :class="getTabClass()">
            <template v-if="currentTabId == 2">
              <div class="result-title">
                <span class="icon "></span>
                <span class="name">修改</span>
              </div>
              <div class="origin-text">
                <span class="name">原始标准：</span>
                <expandable-text :text="obj.del" class="text" />
              </div> 
              <div class="compare-text">
                <span class="name">比对标准：</span>
                <expandable-text :text="obj.ins" class="text" />
              </div> 
            </template>
            <template v-else-if="currentTabId == 3">
              <div class="result-title">
                <span class="icon "></span>
                <span class="name">新增</span>
              </div>
              <div class="compare-text">
                <span class="name">比对标准：</span>
                <expandable-text :text="obj" class="text" />
              </div> 
            </template>
            <template v-else>
              <div class="result-title">
                <span class="icon "></span>
                <span class="name">删除</span>
              </div>
              <div class="origin-text">
                <span class="name">原始标准：</span>
                <expandable-text :text="obj" class="text" />
              </div> 
            </template>
          </div>
          </template>
        </div>
      </div>
    </div>
  </el-drawer>
</template>
<script setup>
import ExpandableText from '@/components/ExpandableText'
import useComparisonStore from '@/store/modules/comparison'

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
  }
})

const loading = ref(false)
const currentTabId = ref(2)
const currentItemList = ref([])
const tabList = [{
  id: 2,
  name: '修改',
},{
  id: 3,
  name: '新增',
},{
  id: 4,
  name: '删除',
}]

const comparisonStore = useComparisonStore()
const { insTextList, delTextList, spanTextMapList } = storeToRefs(comparisonStore);

const emit = defineEmits(['update:open','success'])

const handleClose = () => {
  emit('update:open', false)
}
const handleTabClick = (item) => {
  currentTabId.value = item.id
  if (item.id == 2) {
    currentItemList.value = spanTextMapList.value
  }else if (item.id == 3) {
    currentItemList.value =  insTextList.value
  }else if (item.id == 4) {
    currentItemList.value = delTextList.value
  }
}
const getTabClass = () => {
  if (currentTabId.value == 2) {
    return 'edit-item' 
  }else if (currentTabId.value == 3) {
    return 'add-item' 
  }else if (currentTabId.value == 4) {
    return 'delete-item' 
  }
}
onMounted(() => {
  currentItemList.value = spanTextMapList.value
})
</script>
<style lang="scss" scoped>
:deep(.el-drawer__body){
  position: relative;
}
.result-drawer{
  padding: 0px 5px;
  height: calc(100% - 58px);
  ul,li{
    margin: 0px;
    padding: 0px;
    list-style: none;
  }
  .tab-list{
    height: 44px;
    line-height: 44px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-sizing: border-box;
    width: calc(100% - 0px);
    position: absolute;
    top: 58px;
    left: 0px;
    right: 0px;
    padding-bottom: 5px;
    background: #FFFFFF;
    .tab-item{
      height: 100%;
      font-size: 16px;
      color: #333333;
      cursor: pointer;
    }
    .active-item{
      font-weight: bold;
      color: $primary-color;
      position: relative;
      &::after{
        content: '';
        display: block;
        width: 100%;
        height: 3px;
        position: absolute;
        bottom: 0px;
        left: 0px;
        background: $primary-color;
        border-radius: 2px;
      }
    }
  }
  .result-list{
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    .result-item{
      display: flex;
      justify-content: center;
      flex-direction: column;
      border-bottom: 1px solid #E5E8EF;
      font-size: 14px;
      color: #333333;
      // height: 120px;
      padding: 20px 0px ;
      &:last-child{
        border-bottom: none;
      }
      .result-title{
        display: flex;
        align-items: center;
        .icon{
          display: block;
          width: 9px;
          height: 9px;
          background: #FEB500;
          border-radius: 50%;
          margin-right: 10px;
        }
      }
      .origin-text,.compare-text{
        margin-top: 15px;
        display: flex;
        .name{
          flex-shrink: 0;
        }
        .text{
          color: #FEB500;
        }
        
      }
    }
    .edit-item{
      .result-title .icon{
        background: #FFB400;
      }
      .origin-text .text,.compare-text .text{                
        color: #FFB400;
      }
    }
    .delete-item{
      .result-title .icon{
        background: #FF0000;
      }
      .origin-text .text,.compare-text .text{                
        color: #FF0000;
      }
    }
    .add-item{
      .result-title .icon{
        background: #00B42A;
      }
      .origin-text .text,.compare-text .text{                
        color: #00B42A;
      }
    }
  }
  :deep(table) {
    border-collapse: collapse;
    table,th,td {
      border: 1px solid #999;
      padding: 8px;
    }
  }
  :deep(img){
    max-width: 100%;
    height: auto;
  }
}
</style>