<template>
  <div class="mt15">
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      class="mt15"
      :border="true"
    >
      <template v-slot:empty>
        <empty />
      </template>
      <el-table-column label="序号" fixed min-width="60">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="count"
        label="查新标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="onCount"
        label="检索到标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="activeCount"
        label="现行标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="tobeCount"
        label="即将实施标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="beReplacedCount"
        label="被替代标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="abolishCount"
        label="废止标准数"
        width="150"
        show-overflow-tooltip
      />
      <el-table-column
        prop="createTime"
        label="查询时间"
        width="200"
        show-overflow-tooltip
      />
      <el-table-column width="200" fixed="right" label="操作">
        <template #default="scope">
          <el-button
            @click.stop="handleDetail(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
          >
            查看
          </el-button>
          <el-button
            @click.stop="handleDelete(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
            v-hasPermi="['personal:standardDelete']"
          >
            删除
          </el-button>
          <el-button
            @click.stop="handleDownload(scope.row)"
            link
            size="small"
            class="f-14 c-primary"
            v-hasPermi="['personal:standardDownload']"
          >
            下载结果
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <renew-detail-dialog
      v-if="detailDialog"
      :batchNumber="currentId"
      v-model:dialogVisible="detailDialog"
    />
  </div>
</template>

<script setup>
import {noveltySearchStatisticsList,deleteNoveltySearchStatistics} from '@/api/personal/noveltySearch.js'
const { proxy } = getCurrentInstance();
import RenewDetailDialog from '@/views/components/personal/personal_center/RenewDetailDialog'
const data = reactive({
  loading: false,
  total: 0,
  dataList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  currentId:null,
  detailDialog:false
});

const router = useRouter();
const {
  loading,
  total,
  dataList,
  queryParams,
  currentId,
  detailDialog
} = toRefs(data);

//获取列表
const getList = () => {
  data.loading = true;
  noveltySearchStatisticsList(data.queryParams)
  .then(res => {
    data.dataList = res.rows;
    data.total = res.total;
    data.loading = false;
  })
  .catch(() => {
    data.loading = false;
  });
}

//查看
const handleDetail = (row) => {
  data.currentId = row.batchNumber;
  data.detailDialog = true;
};

//知识下载
const handleDownload = (row) => {
  proxy.download("process/noveltySearch/downloadNoveltySearch", {
    batchNumber: row.batchNumber
  }, `标准查新_${new Date().getTime()}.xlsx`);
}

//删除
const handleDelete = (row) => {
  proxy.$confirm("确认删除该查新记录?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let params = {
      ids:[row.id]
    }
    deleteNoveltySearchStatistics(params).then(res => {
      proxy.$modal.msgSuccess('删除成功')
      getList()
    })
  }).catch(() => {
   
  });
  
}
getList()
</script>

<style lang="scss" scoped>

</style>