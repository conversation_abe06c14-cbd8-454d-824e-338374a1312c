<template>
  <el-dialog
    width="925px"
    title="复审结论"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <div class="h-title">标准</div>
    <el-descriptions :column="2" size="large" class="mt15">
      <el-descriptions-item label="标准号：" :span="2" label-align="left">
        {{ form.standardCode || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="标准名称：" :span="2" label-align="left">
        {{ form.standardName || '-' }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="h-title mt30">结论</div>
    <el-form ref="queryRef" :model="form" :inline="true" :label-position="'top'" :rules="rules" class="dialog-form-inline mt10">
      <el-form-item label="" prop="reviewConclusion" class="one-column">
        <el-radio-group v-model="form.reviewConclusion">
          <el-radio v-for="item in bxc_review_conclusion" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="reviewConclusion" class="one-column">
        <div class="flex flex-ai-center c-99">
          <el-icon class="mr5"><InfoFilled /></el-icon>
          请确认结论对应标准及结论准确性
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">提交</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { addRecheck, putRecheck } from '@/api/standard_revision/review';

  const { proxy } = getCurrentInstance();
  const { bxc_review_conclusion } = proxy.useDict('bxc_review_conclusion');

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    defaultForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({});
  const rules = reactive({
    reviewConclusion: [{ required: true, message: '请选择结论', trigger: 'change' }],
  });

  onMounted(() => {
    form.value = { ...props.defaultForm };
  });

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate(valid => {
      if (valid) {
        if (props.defaultForm.isEdit) {
          putRecheck(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('结论提交成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          addRecheck(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('结论提交成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        }
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    loading.value = false;
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped>
  :deep(.el-descriptions__label) {
    color: #999 !important;
    background-color: #fff !important;
    margin-right: 0 !important;
  }

  :deep(.el-descriptions__content) {
    color: #333 !important;
  }
</style>
