<template>
  <el-dialog
    v-model="props.visible"
    width="60%"
    title="指标提取"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    class="indicators_dialog"
  >
    <div class="container">
      <div class="container-left">
        <div class="f-18 f-bold c-33">标准数据</div>
        <div class="f-14 c-99 mt5">选择需提取指标标准数据资源</div>
        <el-input
          @keyup.enter="handleReset"
          v-model="queryParams.standardCode"
          placeholder="检索标准号"
          style="width: 100% !important"
          class="mt10 mb20"
        >
          <template #suffix>
            <el-icon @click="handleReset" class="pointer f-18"><Search /></el-icon>
          </template>
        </el-input>
        <el-scrollbar v-if="stardardData && stardardData.length > 0" @bottom="getMore" height="387px">
          <el-radio-group @change="handleRadioChange" v-model="radioValue">
            <el-radio v-for="item in stardardData" :key="item.standardCode" :label="item.standardCode">
              <template #default>
                <div class="flex flex-sb flex-ai-center">
                  <div class="radio_label">
                    {{ item.standardCode }}&nbsp;
                    <span v-if="item.indexLibCount > 0" class="c-primary">({{ item.indexLibCount }})</span>
                  </div>
                  <el-icon @click.stop="handleDetail(item)" class="radio_icon" color="#333333"><Document /></el-icon>
                </div>
              </template>
            </el-radio>
          </el-radio-group>
        </el-scrollbar>
        <empty v-else />
      </div>
      <div class="container-line"></div>
      <div v-loading="loading" class="container-right">
        <div class="flex flex-ai-center">
          <el-button @click="handleClick" type="primary">全文提取</el-button>
          <el-button @click="handleCheck" type="primary">指标项提取</el-button>
          <el-input v-show="isShow" v-model="keyWorld" placeholder="请输入指标关键词，多个时以顿号分隔" class="ml12" />
          <el-button v-show="isShow" @click="handlekeyWorld" type="primary" class="ml12">开始提取</el-button>
        </div>
        <el-divider />
        <div v-if="indicatorExtractionData && indicatorExtractionData.length > 0">
          <div class="flex flex-ai-center c-33 f-14 mb20">
            <el-icon class="c-99 f-18 mr5"><InfoFilled /></el-icon>
            提取指标：
            <span class="c-primary">{{ indicatorExtractionData.length }}</span>
            &nbsp;项 &nbsp;｜&nbsp;
            <span class="status-yellow">{{ `< 已存在的指标项在入库时将自动过滤不再重复入库！>` }}</span>
          </div>
          <el-scrollbar height="437px">
            <el-card v-for="(item, index) in indicatorExtractionData" :key="item.id">
              <div class="flex flex-sb flex-ai-center">
                <div class="flex flex-ai-center">
                  <div class="h-title">指标项：{{ item.indexName }}</div>
                  <div class="intro-label status-intro-red status-intro-bgred ml10">
                  <!-- <div v-if="item.existFlag" class="intro-label status-intro-red status-intro-bgred ml20"> -->
                    <el-icon class="f-14 mr5"><InfoFilled /></el-icon>
                    当前指标已存在
                  </div>
                </div>
                <div class="flex flex-ai-center f-18 ml10">
                  <el-icon @click="handleEdit(item, index)" class="pointer"><edit /></el-icon>
                  <el-icon @click="handleDelete(item, index)" class="ml10 pointer"><delete class="status-red" /></el-icon>
                </div>
              </div>
              <div class="card">
                <div class="card-item">
                  <div class="card-item-title">标准对象：</div>
                  <div class="card-item-content">{{ item.standardObject }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标对象：</div>
                  <div class="card-item-content">{{ item.indexObject }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标对象属性：</div>
                  <div class="card-item-content">
                    {{ item.indexObjectAttribute.length || 0 }}
                    <el-tooltip
                      v-if="item.indexObjectAttribute && item.indexObjectAttribute.length > 0"
                      placement="right-end"
                      effect="light"
                      popper-class="indicator-tool-tip"
                    >
                      <template #content>
                        <el-table :data="item.indexObjectAttribute" max-height="230">
                          <el-table-column label="序号" type="index" width="70" />
                          <el-table-column label="指标对象属性名称" prop="indexObjectName" width="150" />
                          <el-table-column label="指标对象属性值" prop="indexObjectValue" width="150" />
                          <el-table-column label="指标对象属性类型" prop="indexObjectType" width="150" />
                        </el-table>
                      </template>
                      <el-icon class="c-99"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标名称：</div>
                  <div class="card-item-content">{{ item.indexName }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标内容：</div>
                  <div class="card-item-content">{{ item.indexContent }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标影响因素：</div>
                  <div class="card-item-content">
                    {{ item.indexInfluenceFactor.length || 0 }}
                    <el-tooltip
                      v-if="item.indexInfluenceFactor && item.indexInfluenceFactor.length > 0"
                      placement="left-end"
                      effect="light"
                      popper-class="indicator-tool-tip"
                    >
                      <template #content>
                        <el-table :data="item.indexInfluenceFactor" max-height="230">
                          <el-table-column label="序号" type="index" width="70" />
                          <el-table-column label="指标影响因素名称" prop="indexInfluenceFactorName" width="150" />
                          <el-table-column label="指标影响因素值" prop="indexInfluenceFactorValue" width="150" />
                          <el-table-column label="指标影响因素类型" prop="indexInfluenceFactorType" width="150" />
                        </el-table>
                      </template>
                      <el-icon class="c-99"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">计量单位：</div>
                  <div class="card-item-content">{{ item.measuringUnit }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标注：</div>
                  <div class="card-item-content">{{ item.indexNote }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">试验方法：</div>
                  <div class="card-item-content">{{ item.experimentalPlan }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标组ID：</div>
                  <div class="card-item-content">{{ item.experimentalGroupId }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">指标类型：</div>
                  <div class="card-item-content">{{ item.indexType }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">表注：</div>
                  <div class="card-item-content">{{ item.tableNote }}</div>
                </div>
                <div class="card-item">
                  <div class="card-item-title">所属标准：</div>
                  <div class="card-item-content">{{ item.fromStandardCode }}</div>
                </div>
              </div>
            </el-card>
          </el-scrollbar>
        </div>
        <empty v-else />
      </div>
    </div>
    <pop-audit-task v-if="openAudit" v-model:open="openAudit" :standardItem="currentRow" fromState="2" />
    <add-dialog v-if="addVisible" v-model:visible="addVisible" :title="'指标更新'" :editData="editData" @success="handleUpdate" />
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :disabled="indicatorExtractionData.length == 0" :loading="btnLoading" type="primary" @click="handleConfirm">
        入库
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getAnalysisReviewList,
    getQueryExtractByCode,
    addIndexLib,
    checkIndexLib,
  } from '@/api/business_manage/standard_indicators';
  import PopAuditTask from '@/views/components/standard_analysis/analysis_audit/PopAuditTask';
  import AddDialog from '@/views/components/business_manage/standard_indicators/AddDialog.vue';

  const { proxy } = getCurrentInstance();
  const emit = defineEmits(['update:visible', 'updateData']);

  const props = defineProps({
    visible: Boolean,
  });

  const loading = ref(false);
  const btnLoading = ref(false);
  const stardardData = ref([]);
  const radioValue = ref('');
  const isShow = ref(false);
  const keyWorld = ref('');
  const indicatorExtractionData = ref([]);
  const openAudit = ref(false);
  const currentRow = ref({});
  const queryParams = ref({
    pageNum: 1,
    pageSize: 40,
    standardCode: '',
  });
  const editData = ref({});
  const addVisible = ref(false);

  const getData = () => {
    loading.value = true;
    getAnalysisReviewList(queryParams.value)
      .then(res => {
        let data = res.rows || [];
        if (queryParams.value.pageNum == 1) {
          stardardData.value = res.rows;
        } else {
          if (data && data.length > 0) {
            stardardData.value = stardardData.value.concat(res.rows);
          } else {
            queryParams.value.pageNum--;
          }
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  getData();

  const handleReset = () => {
    radioValue.value = '';
    indicatorExtractionData.value = [];
    queryParams.value.pageNum = 1;
    getData();
  };

  const getMore = () => {
    queryParams.value.pageNum++;
    getData();
  };

  const handleClick = () => {
    if (radioValue.value) {
      getindicatorExtractionData();
    } else {
      proxy.$modal.msgWarning('请选择需提取指标标准数据资源');
    }
  };

  const handlekeyWorld = () => {
    if (!radioValue.value) {
      proxy.$modal.msgWarning('请选择需提取指标标准数据资源');
      return;
    }
    if (keyWorld.value) {
      getindicatorExtractionData();
    } else {
      proxy.$modal.msgWarning('请输入指标关键词');
    }
  };

  const handleCheck = () => {
    keyWorld.value = '';
    isShow.value = !isShow.value;
  };

  const handleRadioChange = () => {
    indicatorExtractionData.value = [];
  };

  const getindicatorExtractionData = () => {
    loading.value = true;
    getQueryExtractByCode({ indexKeyItem: keyWorld.value, fromStandardCode: radioValue.value })
      .then(res => {
        let data = res.data;
        indicatorExtractionData.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const handleDetail = row => {
    currentRow.value = row;
    openAudit.value = true;
  };

  const handleEdit = (row, index) => {
    editData.value = {
      index: index,
      data: row,
    };
    addVisible.value = true;
  };

  const handleUpdate = data => {
    checkIndexLib(data.data).then(res => {
      data.data.existFlag = res.data.existFlag == 1 ? true : false;
    });
    indicatorExtractionData.value.splice(data.index, 1, data.data);
  };

  const handleDelete = (row, index) => {
    proxy
      .$confirm(`确认移除指标项${row.indexName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        indicatorExtractionData.value.splice(index, 1);
      })
      .catch(() => {});
  };

  const validateIndicatorData = () => {
    const data = indicatorExtractionData.value;

    let hasMainFieldsDuplicate = false;
    let hasInfluenceFactorDuplicate = false;

    const mainFieldsSet = new Set();
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const key = `${item.fromStandardCode}-${item.standardObject}-${item.indexObject}-${item.indexName}-${item.indexContent}-${item.experimentalPlan}`;

      if (mainFieldsSet.has(key)) {
        hasMainFieldsDuplicate = true;
        break;
      }
      mainFieldsSet.add(key);
    }

    const allFactorsSet = new Set();
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.indexInfluenceFactor && item.indexInfluenceFactor.length > 0) {
        for (let j = 0; j < item.indexInfluenceFactor.length; j++) {
          const factor = item.indexInfluenceFactor[j];
          const factorKey = `${factor.indexInfluenceFactorName}-${factor.indexInfluenceFactorValue}-${factor.indexInfluenceFactorType}`;

          if (allFactorsSet.has(factorKey)) {
            hasInfluenceFactorDuplicate = true;
            break;
          }
          allFactorsSet.add(factorKey);
        }
      }
      if (hasInfluenceFactorDuplicate) break;
    }

    if (hasMainFieldsDuplicate && hasInfluenceFactorDuplicate) {
      return '存在重复指标项';
    }

    return null;
  };

  const handleConfirm = () => {
    const validationError = validateIndicatorData();
    if (validationError) {
      proxy.$modal.msgWarning(validationError);
      return;
    }

    btnLoading.value = true;
    addIndexLib(indicatorExtractionData.value)
      .then(res => {
        proxy.$modal.msgSuccess('保存成功！');
        emit('updateData');
        handleClose();
      })
      .finally(() => {
        btnLoading.value = false;
      });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };
</script>

<style>
  .indicator-tool-tip {
    width: 540px !important;
    padding: 15px !important;
  }
  .indicators_dialog .el-dialog__body {
    padding-bottom: 0 !important;
  }
</style>

<style lang="scss" scoped>
  .container {
    display: flex;
    justify-content: space-between;
    min-height: 500px !important;

    &-left {
      width: 25%;
      overflow: hidden;
    }

    &-line {
      width: 1px;
      background-color: #999;
      margin: 0 20px;
    }

    &-right {
      width: 75%;
      padding: 1px;
    }
  }

  .card {
    display: flex;
    flex-wrap: wrap;

    &-item {
      width: 50%;
      display: flex;
      margin-top: 15px;
      font-size: 15px;
      line-height: 25px;

      &-title {
        width: 120px;
        color: #999;
        text-align: right;
      }

      &-content {
        flex: 1;
        color: #333;
        text-align: left;
      }
    }
  }
  .h-title {
    width: auto;
    max-width: 70%;
  }
  .intro-label {
    width: 125px;
    font-size: 12px;
    padding: 0 10px;
    height: 26px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
  }

  :deep(.el-scrollbar__bar) {
    display: none !important;
  }

  .radio_label {
    width: calc(100% - 34px);
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .radio_icon {
    font-size: 15px;
  }

  :deep(.el-radio-group) {
    display: block !important;
  }

  :deep(.el-radio) {
    width: 100%;
    display: flex !important;
    align-items: center !important;
    margin-right: 0 !important;
    height: auto !important;
    margin-bottom: 20px !important;
  }

  :deep(.el-radio__label) {
    width: calc(100% - 14px);
  }

  :deep(.el-input) {
    width: 270px !important;
  }

  :deep(.el-divider--horizontal) {
    margin: 15px 0 !important;
  }

  :deep(.el-card) {
    margin-bottom: 20px !important;
  }

  :deep(.el-card__body) {
    padding: 20px !important;
    box-sizing: border-box;
  }

  :deep(.el-tag) {
    line-height: normal !important;
  }
</style>
