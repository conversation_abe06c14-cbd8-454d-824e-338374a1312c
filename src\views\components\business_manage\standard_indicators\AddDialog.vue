<template>
  <el-dialog
    width="930px"
    :title="props.title"
    v-model="props.visible"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form ref="queryRef" :model="form" label-position="top" :rules="rules" @submit.prevent class="dialog-form-inline">
      <el-form-item class="one-column">
        <div class="h-title">基础信息</div>
      </el-form-item>
      <el-form-item label="标准对象" prop="standardObject">
        <el-input v-model="form.standardObject" placeholder="请输入标准对象" />
      </el-form-item>
      <el-form-item label="指标对象" prop="indexObject">
        <el-input v-model="form.indexObject" placeholder="请输入指标对象" />
      </el-form-item>
      <el-form-item label="指标名称" prop="indexName">
        <el-input v-model="form.indexName" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="指标内容" prop="indexContent">
        <el-input v-model="form.indexContent" placeholder="请输入指标内容" />
      </el-form-item>
      <el-form-item label="计量单位" prop="measuringUnit">
        <el-input v-model="form.measuringUnit" placeholder="请输入计量单位" />
      </el-form-item>
      <el-form-item label="指标注" prop="indexNote">
        <el-input v-model="form.indexNote" placeholder="请输入指标注" />
      </el-form-item>
      <el-form-item label="试验方法" prop="experimentalPlan">
        <el-input v-model="form.experimentalPlan" placeholder="请输入试验方法" />
      </el-form-item>
      <el-form-item label="指标组ID" prop="experimentalGroupId">
        <el-input v-model="form.experimentalGroupId" placeholder="请输入指标组ID" />
      </el-form-item>
      <el-form-item label="指标类型" prop="indexType">
        <el-input v-model="form.indexType" placeholder="请输入指标类型" />
      </el-form-item>
      <el-form-item label="表注" prop="tableNote">
        <el-input v-model="form.tableNote" placeholder="请输入表注" />
      </el-form-item>
      <el-form-item label="所属标准" prop="fromStandardCode">
        <el-input v-model="form.fromStandardCode" placeholder="请输入所属标准" />
      </el-form-item>
      <el-form-item prop="indexObjectAttribute" class="one-column mt30">
        <template #label>
          <div class="flex flex-sb flex-ai-center">
            <div class="h-title">指标对象属性</div>
            <el-button
              v-if="form.indexObjectAttribute && form.indexObjectAttribute.length < 20"
              @click="handleAddIndicators('indexObjectAttribute')"
              type="primary"
              icon="Plus"
            >
              添加对象属性
            </el-button>
          </div>
        </template>
        <el-table :data="form.indexObjectAttribute" :border="true" :max-height="400" class="mt15">
          <el-table-column label="序号" type="index" width="55" fixed />
          <el-table-column prop="indexObjectName" label="指标对象属性名称" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'indexObjectAttribute.' + $index + '.indexObjectName'"
                :rules="rules.indexObjectName"
                class="one-column"
              >
                <el-input v-model="row.indexObjectName" placeholder="请输入指标对象属性名称" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="indexObjectValue" label="指标对象属性值" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'indexObjectAttribute.' + $index + '.indexObjectValue'"
                :rules="rules.indexObjectValue"
                class="one-column"
              >
                <el-input v-model="row.indexObjectValue" placeholder="请输入指标对象属性值" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="indexObjectType" label="指标对象属性类型" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item :prop="'indexObjectAttribute.' + $index + '.indexObjectType'" class="one-column">
                <el-input v-model="row.indexObjectType" placeholder="请输入指标对象属性类型" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" fixed="right">
            <template #default="{ row, $index }">
              <el-button @click="form.indexObjectAttribute.splice($index, 1)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item prop="indexInfluenceFactor" class="one-column mt30">
        <template #label>
          <div class="flex flex-sb flex-ai-center">
            <div class="h-title">指标影响因素</div>
            <el-button
              v-if="form.indexInfluenceFactor && form.indexInfluenceFactor.length < 20"
              @click="handleAddIndicators('indexInfluenceFactor')"
              type="primary"
              icon="Plus"
            >
              添加影响因素
            </el-button>
          </div>
        </template>
        <el-table :data="form.indexInfluenceFactor" :border="true" :max-height="400" class="mt15">
          <el-table-column label="序号" type="index" width="55" fixed />
          <el-table-column prop="indexInfluenceFactorName" label="指标影响因素名称" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'indexInfluenceFactor.' + $index + '.indexInfluenceFactorName'"
                :rules="rules.indexInfluenceFactorName"
                class="one-column"
              >
                <el-input v-model="row.indexInfluenceFactorName" placeholder="请输入指标影响因素名称" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="indexInfluenceFactorValue" label="指标影响因素值" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item
                :prop="'indexInfluenceFactor.' + $index + '.indexInfluenceFactorValue'"
                :rules="rules.indexInfluenceFactorValue"
                class="one-column"
              >
                <el-input v-model="row.indexInfluenceFactorValue" placeholder="请输入指标影响因素值" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="indexInfluenceFactorType" label="指标影响因素类型" min-width="180">
            <template #default="{ row, $index }">
              <el-form-item :prop="'indexInfluenceFactor.' + $index + '.indexInfluenceFactorType'" class="one-column">
                <el-input v-model="row.indexInfluenceFactorType" placeholder="请输入指标影响因素类型" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" fixed="right">
            <template #default="{ row, $index }">
              <el-button @click="form.indexInfluenceFactor.splice($index, 1)" type="danger" link>删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="info" @click="handleClose">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getIndexLibDetail, updateIndexLib } from '@/api/business_manage/standard_indicators';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '编辑指标',
    },
    id: {
      type: [Number, String],
    },
    editData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });

  const loading = ref(false);
  const form = ref({
    indexObjectAttribute: [],
    indexInfluenceFactor: [],
  });
  const rules = ref({
    indexName: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
    indexContent: [{ required: true, message: '请输入指标内容', trigger: 'blur' }],
    experimentalPlan: [{ required: true, message: '请输入试验方法', trigger: 'blur' }],
    fromStandardCode: [{ required: true, message: '请输入所属标准', trigger: 'blur' }],
    indexObjectName: [{ required: true, message: '请输入指标对象属性名称', trigger: 'blur' }],
    indexObjectValue: [{ required: true, message: '请输入指标对象属性值', trigger: 'blur' }],
    indexInfluenceFactorName: [{ required: true, message: '请输入指标影响因素名称', trigger: 'blur' }],
    indexInfluenceFactorValue: [{ required: true, message: '请输入指标影响因素值', trigger: 'blur' }],
  });

  const handleData = data => {
    data.forEach(item => {
      if (item.type == 0) {
        item.disabled = false;
        return;
      }
      if (item.type == 1 && item.children && item.children.length > 0) {
        item.disabled = false;
        handleData(item.children);
      } else {
        item.disabled = true;
      }
    });
    return data;
  };

  if (props.id) {
    getIndexLibDetail({ id: props.id }).then(res => {
      let data = res.data;
      data.indexObjectAttribute =
        data.indexObjectAttribute && data.indexObjectAttribute.length > 0 ? data.indexObjectAttribute : [];
      data.indexInfluenceFactor =
        data.indexInfluenceFactor && data.indexInfluenceFactor.length > 0 ? data.indexInfluenceFactor : [];
      form.value = data;
    });
  }
  if (props.editData.data) {
    form.value = props.editData.data;
  }

  const handleAddIndicators = type => {
    let obj = {
      [`${type}Name`]: '',
      [`${type}Value`]: '',
      [`${type}Type`]: '',
    };
    form.value[type].push(obj);
  };

  const handleConfirm = () => {
    loading.value = true;
    proxy.$refs.queryRef.validate((valid, e) => {
      if (valid) {
        const allObjectsMap = new Map();
        for (let i = 0; i < form.value.indexObjectAttribute.length; i++) {
          const item = form.value.indexObjectAttribute[i];
          const key = `${item.indexObjectName}_${item.indexObjectValue}_${item.indexObjectType}`;
          if (allObjectsMap.has(key)) {
            proxy.$modal.msgError('指标对象属性中存在重复项目！');
            loading.value = false;
            return;
          }
          allObjectsMap.set(key, 'indexObjectAttribute');
        }
        for (let i = 0; i < form.value.indexInfluenceFactor.length; i++) {
          const item = form.value.indexInfluenceFactor[i];
          const key = `${item.indexInfluenceFactorName}_${item.indexInfluenceFactorValue}_${item.indexInfluenceFactorType}`;
          if (allObjectsMap.has(key)) {
            const existingType = allObjectsMap.get(key);
            if (existingType === 'indexObjectAttribute') {
              proxy.$modal.msgError('指标影响因素与指标对象属性中存在重复项目！');
            } else {
              proxy.$modal.msgError('指标影响因素中存在重复项目！');
            }
            loading.value = false;
            return;
          }
          allObjectsMap.set(key, 'indexInfluenceFactor');
        }

        if (props.title == '编辑指标') {
          updateIndexLib(form.value)
            .then(res => {
              proxy.$modal.msgSuccess('保存成功！');
              emit('success');
              handleClose();
            })
            .finally(() => {
              loading.value = false;
            });
        } else {
          let obj = {
            index: props.editData.index,
            data: form.value,
          };
          emit('success', obj);
          handleClose();
        }
      } else {
        loading.value = false;
      }
    });
  };

  const handleClose = () => {
    emit('update:visible', false);
  };

  const emit = defineEmits(['update:visible', 'success']);
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-right: 0 !important;
  }

  :deep(.el-table .el-form-item) {
    margin-bottom: 0 !important;
  }

  :deep(.el-date-editor) {
    width: 50% !important;
  }

  :deep(.el-cascader) {
    width: 50% !important;
  }

  :deep(.el-table td.el-table__cell .cell) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
