<template>
  <div class="pop-container">
    <el-dialog
      :append-to-body="true"
      v-model="open"
      width="96%"
      :title="title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="s-audit-wrap">
        <div class="s-category-wrap">
          <audit-category-tree :standardName="standardItem.standardName" />
          <div v-if="auditStore.isInputState()" class="category-mask"></div>
        </div>
        <div v-show="auditStore.isInputState()" class="s-edit-wrap">
          <audit-add-section />
        </div>
        <!-- <div v-if="auditStore.content"  class="s-content-wrap scroller-bar-style" v-html="auditStore.content"></div> -->
        <div v-show="auditStore.opType == '' || isShowContent" class="s-content-wrap scroller-bar-style">
          <audit-content />
        </div>

        <template v-if="auditStore.fromState != '2'">
          <div v-show="auditStore.opType == '' || !isShowContent" class="s-pdf-wrap">
            <iframe
              v-if="previewUrl"
              :src="previewUrl"
              :style="{ width: '100%', height: `calc(100vh - 206px)`, border: 'none' }"
            ></iframe>
            <el-empty v-else />
          </div>
        </template>

        <div v-if="auditStore.isInputState()" class="switch-pdf" @click="switchPdf">
          <i class="iconfont icon-qiehuan- c-ff"></i>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click.stop="handleClose">关闭</el-button>
          <!-- 审核 -->
          <template v-if="auditStore.fromState == '1'">
            <el-button @click.prevent="finish" type="primary" :disabled="auditStore.isInputState()" :loading="finishLoading">
              提交
            </el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { auditCompleted } from '@/api/standard_analysis/analysis_audit';
  import useAuditStandardStore from '@/store/modules/auditStandard';
  import { base64Encode } from '@/utils/base64';
  import AuditCategoryTree from '@/views/components/standard_manage/standard_query/AuditCategoryTree.vue';
  import AuditContent from '@/views/components/standard_manage/standard_query/AuditContent.vue';
  import AuditAddSection from '@/views/components/standard_manage/standard_query/AuditAddSection.vue';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    open: Boolean,
    standardItem: {
      type: Object,
      default: {},
    },
    fromState: {
      type: String,
      default: '4', // 1: 审核 2: 查看 3:二次审核 4：编辑
    },
  });
  const { open, standardItem, fromState } = toRefs(props);

  const auditStore = useAuditStandardStore();

  const title = ref('审核');
  const loading = ref(false);
  const finishLoading = ref(false);
  const isShowContent = ref(false);

  onMounted(() => {
    loading.value = true;
    title.value = standardItem.value.standardCode + ' | ' + standardItem.value.standardName;
    auditStore.fromState = fromState.value;
    auditStore
      .getAuditData(props.standardItem.standardId)
      .then(res => {})
      .catch(error => {})
      .finally(() => {
        loading.value = false;
        finishLoading.value = false;
      });
  });

  const emit = defineEmits(['update:open', 'success']);

  const handleClose = () => {
    if (['add', 'edit'].includes(auditStore.opType)) {
      auditStore
        .checkIsModifySection()
        .then(res => {
          close();
        })
        .catch(error => {
          if (!error) {
            close();
          }
        });
      return false;
    } else {
      close();
    }
  };
  const close = () => {
    emit('update:open', false);
    auditStore.resetData();
    emit('success');
  };
  const previewUrl = computed(() => {
    return `${process.env.VITE_APP_HOST}/view/onlinePreview?url=${encodeURIComponent(base64Encode(auditStore.pdfUrl))}`;
  });
  const switchPdf = () => {
    isShowContent.value = !isShowContent.value;
  };
  const finish = () => {
    finishLoading.value = true;
    auditCompleted(form.value)
      .then(res => {
        emit('success', '题录编辑成功！');
        emit('update:open', false);
      })
      .finally(() => {
        finishLoading.value = false;
      });
  };
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 0px !important;
    overflow-y: auto !important;
  }
  .s-audit-wrap {
    padding: 0;
    display: flex;
    position: relative;
    box-sizing: border-box;
    border-bottom: 1px solid #e8e8e8;
    .s-category-wrap {
      width: 380px;
      padding-left: 20px;
      position: relative;
      .category-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
    .s-edit-wrap {
      flex: 1;
      box-sizing: border-box;
    }
    .s-content-wrap {
      flex: 1;
      height: calc(100vh - 190px);
      overflow-y: auto;
      box-sizing: border-box;
    }
    .s-pdf-wrap {
      flex: 1;
      height: calc(100vh - 200px);
      overflow-y: auto;
      box-sizing: border-box;
    }
    .switch-pdf {
      background: $primary-color;
      width: 30px;
      height: 30px;
      border-radius: 15px;
      position: absolute;
      right: 25px;
      top: 50px;
      z-index: 999;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
</style>
