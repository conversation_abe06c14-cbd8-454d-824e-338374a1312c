<template>
  <template v-if="cardTable.length > 0">
    <div v-for="item in cardTable" :key="item.id" class="flex card">
      <!-- <div class="card-select">
        <input @change="toggleSelect(item)" :checked="item.selected" type="checkbox" class="card-select-checkbox" />
      </div> -->
      <div class="flex-1" style="overflow: hidden">
        <div class="card-top">
          <div @click="handleDetail(item)" class="flex pointer">
            <div class="card-top-title f-16 c-primary f-bold">【{{ item.standardTypeName }}】</div>
            <div class="flex-1">
              <div class="f-16 lh-24 f-bold card-top-intro" v-html="item.intro"></div>
              <div class="f-14 c-99 mt5">
                {{ item.standardNameEn }}
              </div>
            </div>
          </div>
          <div class="f-14 c-33 mt25 card-top-label">
            &nbsp;
            <div class="card-top-label-left" :class="getStatusColor(item.standardStatus)">{{ item.standardStatusName }}</div>
            <div class="card-top-label-right">
              <div class="card-top-label-right-item">发布日期：{{ item.publishDate || '-' }}</div>
              <div class="card-top-label-right-item">实施日期：{{ item.executeDate || '-' }}</div>
              <div class="card-top-label-right-item">
                CCS分类：
                <template v-if="item.standardTypeCodeGb">{{ item.standardTypeCodeGb }}</template>
                <template v-if="item.standardTypeCodeGbName">{{ item.standardTypeCodeGbName }}</template>
              </div>
              <div class="card-top-label-right-item">
                ICS分类：
                <template v-if="item.standardTypeCodeIso">{{ item.standardTypeCodeIso }}</template>
                <template v-if="item.standardTypeCodeIsoName">{{ item.standardTypeCodeIsoName }}</template>
              </div>
            </div>
          </div>
        </div>
        <div @click="handleCatalogue(item)" v-if="item.textRetrieval" class="flex-1 card-down flex f-14 pointer">
          <div class="c-FF0000">【正文检索】</div>
          <div class="overflow-two-ellipsis flex-1" v-html="item.textRetrieval"></div>
        </div>
      </div>
    </div>
  </template>
  <empty v-else />
  <tree-preview v-if="analysisVisible" v-model:open="analysisVisible" :id="analysisId" :standardItem="analysisItem" />
  <detail-drawer
    v-if="drawerVisible"
    v-model:visible="drawerVisible"
    :standardId="standardId"
    :standardType="standardType"
    :content="content"
  />
</template>

<script setup>
  import useComparisonStore from '@/store/modules/comparison';
  import TreePreview from '@/views/components/standard_manage/standard_query/TreePreview';
  import DetailDrawer from '@/views/components/standard_manage/standard_query/DetailDrawer';

  const router = useRouter();

  const props = defineProps({
    cardTable: {
      type: Array,
      default: () => {
        return [];
      },
    },
    title: {
      type: String,
      default: '',
    },
    content: {
      type: String,
      default: '',
    },
  });

  const { cardTable, title, content } = toRefs(props);

  const analysisVisible = ref(false);
  const analysisId = ref();
  const analysisItem = ref({});
  const standardId = ref('');
  const standardType = ref('');
  const drawerVisible = ref(false);

  watch(cardTable, val => {
    let text = '';
    val.forEach(item => {
      text = item.standardCode + '&nbsp; | &nbsp;' + item.standardName;
      if (title.value) {
        let keyword = title.value.trim().replace(/\s+/g, ',').split(',');
        keyword.forEach(keywordItem => {
          text = text.replace(new RegExp(keywordItem, 'ig'), `<span class="c-FF0000">$&</span>`);
        });
        item.intro = text;
      } else {
        item.intro = text;
      }
      if (props.content != '') {
        item.textRetrieval = replaceEmTags(item.textRetrieval);
        useComparisonStore()._mathjax();
      }
    });
  });

  const replaceEmTags = html => {
    let result = html.replace(/<em>/g, '<span class="c-FF0000">');
    result = result.replace(/<\/em>/g, '</span>');
    return result;
  };

  const getStatusColor = status => {
    switch (Number(status)) {
      case 0:
        return 'status-intro-blue';
        break;
      case 1:
        return 'status-intro-green';
        break;
      case 3:
        return 'status-intro-gray';
        break;
      case 4:
        return 'status-intro-red';
        break;
      default:
        break;
    }
  };

  const handleDetail = item => {
    standardId.value = item.standardId;
    standardType.value = item.standardType;
    drawerVisible.value = true;
  };

  const handleCatalogue = row => {
    analysisItem.value = {
      standardId: row.standardId,
      standardCode: row.standardCode,
      standardName: row.standardName,
      content: content.value || '',
    };
    analysisId.value = row.indexId;
    analysisVisible.value = true;
  };

  const toggleSelect = item => {
    item.selected = !item.selected;
    const arr = cardTable.value.filter(item => {
      return item.selected == true;
    });
    emit('change', arr);
  };

  const emit = defineEmits(['change']);
</script>

<style lang="scss" scoped>
  .card {
    border: 1px solid #d2d9e9;
    border-radius: 3px;
    min-height: 100px;
    margin-top: 20px;

    &:hover .card-top-intro {
      color: $primary-color;
    }

    &-select {
      width: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-right: 1px solid #d2d9e9;
    }

    &-top {
      padding: 20px 25px;
      box-sizing: border-box;
      overflow: hidden;

      &-title {
        width: 100px;
      }

      &-label {
        display: flex;
        align-items: center;
        overflow: hidden;

        &-left {
          max-width: 100px;
        }

        &-right {
          width: calc(100% - 100px);
          display: flex;
          align-items: center;

          &-item {
            max-width: calc((100% - 120px) / 4);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 30px;
          }
        }
      }
    }

    &-down {
      padding: 20px 25px;
      box-sizing: border-box;
      border-top: 1px solid #d2d9e9;
    }
  }

  .card-select-checkbox {
    /* 隐藏默认多选框 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;

    /* 添加自定义样式 */
    width: 14px;
    height: 14px;
    border: 1px solid #d2d9e9; /* 默认边框颜色 */
    border-radius: 3px;
  }

  .card-select-checkbox:checked {
    /* 隐藏默认多选框 */
    -webkit-appearance: checkbox;
    -moz-appearance: checkbox;
    appearance: checkbox;
    outline: checkbox;
  }

  .lh-24 {
    line-height: 24px;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .c-00bae2 {
    color: #00bae2;
  }

  .c-f56c6c {
    color: #f56c6c;
  }

  .c-FFAE00 {
    color: #ffae00;
  }
</style>
