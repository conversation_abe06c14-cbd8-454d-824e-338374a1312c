import request from '@/utils/request'

// 列表
export function knowledgeList(data) {
  return request({
    url: '/library/list',
    method: 'get',
    params: data
  })
}

// 关注
export function followKnowledge(data) {
  return request({
    url: '/library/isFollow',
    method: 'get',
    params: data
  })
}

// 删除
export function delKnowledge(id) {
  return request({
    url: '/library/delete/' + id,
    method: 'delete'
  })
}

// 新增
export function addKnowledge(data) {
  return request({
    url: '/library/add',
    method: 'post',
    data: data
  })
}

// 详情
export function detailKnowledge(id) {
  return request({
    url: '/library/findOne/' + id,
    method: 'get',
  })
}

// 修改
export function editKnowledge(data) {
  return request({
    url: '/library/update',
    method: 'put',
    data: data
  })
}

// 目录下的文件或目录
export function getLibraryFiles(params) {
  return request({
    url: '/knowledge/library/file/queryFilePageList',
    method: 'get',
    params
  })
}

// 目录树
export function getLibraryTree(libraryId) {
  return request({
    url: '/knowledge/library/file/queryFileTree/' + libraryId,
    method: 'get'
  })
}

// 保存目录
export function setFolder(data) {
  return request({
    url: '/knowledge/library/file/saveFolder',
    method: 'post',
    data
  })
}

// 保存文件
export function setFile(data) {
  return request({
    url: '/knowledge/library/file/saveFile',
    method: 'post',
    data
  })
}

// 查询当文件信息
export function getFileInfo(fileId) {
  return request({
    url: '/knowledge/library/file/queryOneFileInfo/' + fileId,
    method: 'get'
  })
}

// 删除知识库文件或文件夹
export function deleteByFileId(fileId) {
  return request({
    url: '/knowledge/library/file/delete/' + fileId,
    method: 'delete'
  })
}

// 文件或文件夹重命名
export function renameFolderOrFile(data) {
  return request({
    url: '/knowledge/library/file/rename',
    method: 'put',
    data
  })
}

// 移动文件或文件夹
export function moveFolderOrFile(data) {
  return request({
    url: '/knowledge/library/file/move',
    method: 'put',
    data
  })
}

// 新增/取消关注
export function followFile(data) {
  return request({
    url: '/process/userCollectInfo/isCollect',
    method: 'post',
    data
  })
}
// 查询所有知识库列表不分页
export function getLibraryAll(data) {
  return request({
    url: '/library/listAll',
    method: 'get'
  })
}
// 获取知识库下所有文件目录和所有子节点（除节点以外）
export function getMoveLibraryTree(params) {
  return request({
    url: '/knowledge/library/file/queryExceptLevelFileTree',
    method: 'get',
    params
  })
}
// 拖拽文件/文件夹
export function dragMove(data) {
  return request({
    url: '/knowledge/library/file/dragMove',
    method: 'put',
    data
  })
}